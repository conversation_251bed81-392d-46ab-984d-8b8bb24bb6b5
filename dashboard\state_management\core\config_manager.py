# -*- coding: utf-8 -*-
"""
统一配置管理系统
提供配置加载、验证、热更新和环境管理功能
"""

import logging
import os
import json
import threading
import time

# 可选依赖
try:
    import yaml
    HAS_YAML = True
except ImportError:
    yaml = None
    HAS_YAML = False
from typing import Dict, List, Optional, Any, Callable, Union, Type
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import copy
import hashlib
from collections import defaultdict


class ConfigFormat(Enum):
    """配置文件格式枚举"""
    JSON = "json"
    YAML = "yaml"
    YML = "yml"
    INI = "ini"
    ENV = "env"


class ConfigSource(Enum):
    """配置源类型枚举"""
    FILE = "file"
    ENVIRONMENT = "environment"
    COMMAND_LINE = "command_line"
    DATABASE = "database"
    REMOTE = "remote"
    DEFAULT = "default"


class ValidationLevel(Enum):
    """验证级别枚举"""
    NONE = "none"           # 不验证
    BASIC = "basic"         # 基本类型验证
    STRICT = "strict"       # 严格验证
    CUSTOM = "custom"       # 自定义验证


@dataclass
class ConfigSchema:
    """配置模式定义"""
    key: str
    data_type: Type
    required: bool = False
    default_value: Any = None
    description: str = ""
    validation_rules: List[Callable] = field(default_factory=list)
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    pattern: Optional[str] = None


@dataclass
class ConfigEntry:
    """配置条目"""
    key: str
    value: Any
    source: ConfigSource
    timestamp: datetime = field(default_factory=datetime.now)
    schema: Optional[ConfigSchema] = None
    validated: bool = False
    encrypted: bool = False


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(
        self, 
        config_dirs: List[str] = None,
        auto_reload: bool = True,
        validation_level: ValidationLevel = ValidationLevel.BASIC,
        enable_encryption: bool = False
    ):
        """
        初始化配置管理器
        
        Args:
            config_dirs: 配置文件目录列表
            auto_reload: 是否自动重载配置
            validation_level: 验证级别
            enable_encryption: 是否启用加密
        """
        self.logger = logging.getLogger(__name__)
        
        # 配置存储
        self._configs: Dict[str, ConfigEntry] = {}
        self._schemas: Dict[str, ConfigSchema] = {}
        self._config_files: Dict[str, str] = {}  # file_path -> file_hash
        
        # 配置目录
        self._config_dirs = config_dirs or []
        self._setup_default_config_dirs()
        
        # 设置
        self._auto_reload = auto_reload
        self._validation_level = validation_level
        self._enable_encryption = enable_encryption
        
        # 监听器和回调
        self._change_listeners: Dict[str, List[Callable]] = defaultdict(list)
        self._global_listeners: List[Callable] = []
        
        # 环境和命名空间
        self._current_environment = os.getenv('APP_ENV', 'development')
        self._namespace_stack: List[str] = []
        
        # 统计信息
        self._stats = {
            'total_configs': 0,
            'loaded_files': 0,
            'validation_errors': 0,
            'reload_count': 0,
            'last_reload': None
        }
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 文件监控线程
        if self._auto_reload:
            self._monitor_thread = threading.Thread(target=self._file_monitor_worker, daemon=True)
            self._monitor_thread.start()
        else:
            self._monitor_thread = None
        
        # 加载默认配置
        self._load_default_configs()
        
        self.logger.info("ConfigManager initialized")
    
    def _setup_default_config_dirs(self):
        """设置默认配置目录"""
        try:
            # 当前项目的配置目录
            current_dir = Path(__file__).parent.parent
            default_dirs = [
                str(current_dir / "config"),
                str(current_dir / "configs"),
                str(current_dir / "settings"),
                "/etc/app",
                "~/.config/app",
                "./config",
                "./configs"
            ]
            
            # 添加存在的目录
            for dir_path in default_dirs:
                expanded_path = os.path.expanduser(dir_path)
                if os.path.exists(expanded_path) and expanded_path not in self._config_dirs:
                    self._config_dirs.append(expanded_path)
            
            # 禁用默认配置目录创建
            # if not self._config_dirs:
            #     default_config_dir = str(current_dir / "config")
            #     os.makedirs(default_config_dir, exist_ok=True)
            #     self._config_dirs.append(default_config_dir)
                
        except Exception as e:
            self.logger.warning(f"Failed to setup default config dirs: {e}")
    
    def _load_default_configs(self):
        """加载默认配置"""
        try:
            # 注册基本配置模式
            self._register_default_schemas()
            
            # 从环境变量加载配置
            self._load_from_environment()
            
            # 从配置文件加载
            self._load_from_files()
            
        except Exception as e:
            self.logger.error(f"Failed to load default configs: {e}")
    
    def _register_default_schemas(self):
        """注册默认配置模式"""
        default_schemas = [
            ConfigSchema(
                key="app.name",
                data_type=str,
                required=True,
                default_value="dashboard_app",
                description="应用程序名称"
            ),
            ConfigSchema(
                key="app.version",
                data_type=str,
                required=False,
                default_value="1.0.0",
                description="应用程序版本"
            ),
            ConfigSchema(
                key="app.debug",
                data_type=bool,
                required=False,
                default_value=False,
                description="调试模式开关"
            ),
            ConfigSchema(
                key="logging.level",
                data_type=str,
                required=False,
                default_value="INFO",
                allowed_values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                description="日志级别"
            ),
            ConfigSchema(
                key="database.host",
                data_type=str,
                required=False,
                default_value="localhost",
                description="数据库主机"
            ),
            ConfigSchema(
                key="database.port",
                data_type=int,
                required=False,
                default_value=5432,
                min_value=1,
                max_value=65535,
                description="数据库端口"
            ),
            ConfigSchema(
                key="server.port",
                data_type=int,
                required=False,
                default_value=8000,
                min_value=1024,
                max_value=65535,
                description="服务器端口"
            ),
            ConfigSchema(
                key="cache.ttl",
                data_type=int,
                required=False,
                default_value=3600,
                min_value=0,
                description="缓存TTL（秒）"
            )
        ]
        
        for schema in default_schemas:
            self.register_schema(schema)
    
    def _load_from_environment(self):
        """从环境变量加载配置"""
        try:
            env_prefix = "APP_"
            for key, value in os.environ.items():
                if key.startswith(env_prefix):
                    config_key = key[len(env_prefix):].lower().replace('_', '.')
                    self._set_config_value(config_key, value, ConfigSource.ENVIRONMENT)
            
            self.logger.debug("Loaded configuration from environment variables")
            
        except Exception as e:
            self.logger.error(f"Failed to load from environment: {e}")
    
    def _load_from_files(self):
        """从配置文件加载"""
        try:
            config_files = []
            
            # 查找配置文件
            for config_dir in self._config_dirs:
                if not os.path.exists(config_dir):
                    continue
                
                for file_name in os.listdir(config_dir):
                    file_path = os.path.join(config_dir, file_name)
                    if os.path.isfile(file_path) and self._is_config_file(file_name):
                        config_files.append(file_path)
            
            # 按优先级排序（环境特定的配置文件优先）
            config_files.sort(key=lambda x: (
                self._current_environment not in os.path.basename(x),
                os.path.basename(x)
            ))
            
            # 加载配置文件
            for file_path in config_files:
                try:
                    self._load_config_file(file_path)
                except Exception as e:
                    self.logger.error(f"Failed to load config file {file_path}: {e}")
            
            self._stats['loaded_files'] = len(config_files)
            
        except Exception as e:
            self.logger.error(f"Failed to load from files: {e}")
    
    def _is_config_file(self, filename: str) -> bool:
        """检查是否是配置文件"""
        config_extensions = ['.json', '.yaml', '.yml', '.ini', '.env']
        return any(filename.lower().endswith(ext) for ext in config_extensions)
    
    def _load_config_file(self, file_path: str):
        """加载单个配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 计算文件哈希
            file_hash = hashlib.md5(content.encode()).hexdigest()
            self._config_files[file_path] = file_hash
            
            # 根据文件扩展名解析内容
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.json':
                data = json.loads(content)
            elif file_ext in ['.yaml', '.yml']:
                if not HAS_YAML:
                    self.logger.warning(f"YAML support not available, skipping {file_path}")
                    return
                data = yaml.safe_load(content)
            elif file_ext == '.ini':
                data = self._parse_ini_file(content)
            elif file_ext == '.env':
                data = self._parse_env_file(content)
            else:
                self.logger.warning(f"Unsupported config file format: {file_path}")
                return
            
            # 扁平化配置数据
            flat_data = self._flatten_dict(data)
            
            # 设置配置值
            for key, value in flat_data.items():
                self._set_config_value(key, value, ConfigSource.FILE)
            
            self.logger.debug(f"Loaded config file: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load config file {file_path}: {e}")
            raise
    
    def _parse_ini_file(self, content: str) -> Dict[str, Any]:
        """解析INI文件内容"""
        import configparser
        parser = configparser.ConfigParser()
        parser.read_string(content)
        
        result = {}
        for section_name in parser.sections():
            section = {}
            for key, value in parser.items(section_name):
                # 尝试转换数据类型
                section[key] = self._convert_value_type(value)
            result[section_name] = section
        
        return result
    
    def _parse_env_file(self, content: str) -> Dict[str, Any]:
        """解析ENV文件内容"""
        result = {}
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                result[key.lower().replace('_', '.')] = self._convert_value_type(value)
        
        return result
    
    def _convert_value_type(self, value: str) -> Any:
        """转换值类型"""
        if not isinstance(value, str):
            return value
        
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 字符串
        return value
    
    def _flatten_dict(self, data: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """扁平化字典"""
        result = {}
        
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                result.update(self._flatten_dict(value, full_key))
            else:
                result[full_key] = value
        
        return result
    
    def _set_config_value(self, key: str, value: Any, source: ConfigSource):
        """设置配置值"""
        with self._lock:
            try:
                # 获取完整键名（包含命名空间）
                full_key = self._get_full_key(key)
                
                # 验证配置值
                if self._validation_level != ValidationLevel.NONE:
                    validated_value = self._validate_config_value(full_key, value)
                    if validated_value is not None:
                        value = validated_value
                
                # 创建配置条目
                entry = ConfigEntry(
                    key=full_key,
                    value=value,
                    source=source,
                    schema=self._schemas.get(full_key),
                    validated=True
                )
                
                # 检查是否有变化
                old_entry = self._configs.get(full_key)
                has_changed = old_entry is None or old_entry.value != value
                
                # 设置配置
                self._configs[full_key] = entry
                self._stats['total_configs'] = len(self._configs)
                
                # 触发变化监听器
                if has_changed:
                    self._notify_change_listeners(full_key, value, old_entry.value if old_entry else None)
                
            except Exception as e:
                self.logger.error(f"Failed to set config value {key}: {e}")
                raise

    def _get_full_key(self, key: str) -> str:
        """获取完整键名（包含命名空间）"""
        if self._namespace_stack:
            namespace = ".".join(self._namespace_stack)
            return f"{namespace}.{key}"
        return key

    def _validate_config_value(self, key: str, value: Any) -> Any:
        """验证配置值"""
        try:
            schema = self._schemas.get(key)
            if not schema:
                return value

            # 类型验证
            if not isinstance(value, schema.data_type):
                try:
                    # 尝试类型转换
                    if schema.data_type == bool and isinstance(value, str):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = schema.data_type(value)
                except (ValueError, TypeError):
                    raise ValueError(f"Invalid type for {key}: expected {schema.data_type.__name__}")

            # 范围验证
            if schema.min_value is not None and value < schema.min_value:
                raise ValueError(f"Value for {key} is below minimum: {value} < {schema.min_value}")

            if schema.max_value is not None and value > schema.max_value:
                raise ValueError(f"Value for {key} is above maximum: {value} > {schema.max_value}")

            # 允许值验证
            if schema.allowed_values and value not in schema.allowed_values:
                raise ValueError(f"Invalid value for {key}: {value} not in {schema.allowed_values}")

            # 模式验证
            if schema.pattern and isinstance(value, str):
                import re
                if not re.match(schema.pattern, value):
                    raise ValueError(f"Value for {key} does not match pattern: {schema.pattern}")

            # 自定义验证规则
            for rule in schema.validation_rules:
                if not rule(value):
                    raise ValueError(f"Custom validation failed for {key}")

            return value

        except Exception as e:
            self._stats['validation_errors'] += 1
            if self._validation_level == ValidationLevel.STRICT:
                raise
            else:
                self.logger.warning(f"Validation warning for {key}: {e}")
                return value

    def _notify_change_listeners(self, key: str, new_value: Any, old_value: Any):
        """通知变化监听器"""
        try:
            # 通知特定键的监听器
            for listener in self._change_listeners.get(key, []):
                try:
                    listener(key, new_value, old_value)
                except Exception as e:
                    self.logger.error(f"Error in change listener for {key}: {e}")

            # 通知全局监听器
            for listener in self._global_listeners:
                try:
                    listener(key, new_value, old_value)
                except Exception as e:
                    self.logger.error(f"Error in global change listener: {e}")

        except Exception as e:
            self.logger.error(f"Failed to notify change listeners: {e}")

    def _file_monitor_worker(self):
        """文件监控工作线程"""
        while True:
            try:
                time.sleep(5)  # 每5秒检查一次
                self._check_file_changes()
            except Exception as e:
                self.logger.error(f"Error in file monitor worker: {e}")

    def _check_file_changes(self):
        """检查文件变化"""
        try:
            changed_files = []

            for file_path, old_hash in self._config_files.items():
                if not os.path.exists(file_path):
                    continue

                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                new_hash = hashlib.md5(content.encode()).hexdigest()
                if new_hash != old_hash:
                    changed_files.append(file_path)

            # 重新加载变化的文件
            if changed_files:
                self.logger.info(f"Detected changes in config files: {changed_files}")
                for file_path in changed_files:
                    self._load_config_file(file_path)

                self._stats['reload_count'] += 1
                self._stats['last_reload'] = datetime.now()

        except Exception as e:
            self.logger.error(f"Failed to check file changes: {e}")

    def register_schema(self, schema: ConfigSchema):
        """
        注册配置模式

        Args:
            schema: 配置模式
        """
        with self._lock:
            self._schemas[schema.key] = schema

            # 如果有默认值且当前没有配置，设置默认值
            if schema.default_value is not None and schema.key not in self._configs:
                self._set_config_value(schema.key, schema.default_value, ConfigSource.DEFAULT)

            self.logger.debug(f"Registered config schema: {schema.key}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key: 配置键
            default: 默认值

        Returns:
            Any: 配置值
        """
        with self._lock:
            full_key = self._get_full_key(key)
            entry = self._configs.get(full_key)

            if entry is not None:
                return entry.value

            # 检查是否有模式定义的默认值
            schema = self._schemas.get(full_key)
            if schema and schema.default_value is not None:
                return schema.default_value

            return default

    def set(self, key: str, value: Any, source: ConfigSource = ConfigSource.COMMAND_LINE):
        """
        设置配置值

        Args:
            key: 配置键
            value: 配置值
            source: 配置源
        """
        self._set_config_value(key, value, source)

    def has(self, key: str) -> bool:
        """
        检查配置是否存在

        Args:
            key: 配置键

        Returns:
            bool: 是否存在
        """
        with self._lock:
            full_key = self._get_full_key(key)
            return full_key in self._configs

    def delete(self, key: str) -> bool:
        """
        删除配置

        Args:
            key: 配置键

        Returns:
            bool: 是否成功删除
        """
        with self._lock:
            full_key = self._get_full_key(key)
            if full_key in self._configs:
                old_value = self._configs[full_key].value
                del self._configs[full_key]
                self._stats['total_configs'] = len(self._configs)

                # 通知监听器
                self._notify_change_listeners(full_key, None, old_value)

                self.logger.debug(f"Deleted config: {full_key}")
                return True

            return False

    def get_all(self, prefix: str = "") -> Dict[str, Any]:
        """
        获取所有配置或指定前缀的配置

        Args:
            prefix: 键前缀

        Returns:
            Dict[str, Any]: 配置字典
        """
        with self._lock:
            result = {}

            for key, entry in self._configs.items():
                if not prefix or key.startswith(prefix):
                    # 移除前缀
                    display_key = key[len(prefix):].lstrip('.') if prefix else key
                    result[display_key] = entry.value

            return result

    def get_config_info(self, key: str) -> Optional[Dict[str, Any]]:
        """
        获取配置详细信息

        Args:
            key: 配置键

        Returns:
            Optional[Dict[str, Any]]: 配置信息
        """
        with self._lock:
            full_key = self._get_full_key(key)
            entry = self._configs.get(full_key)

            if entry:
                return {
                    'key': entry.key,
                    'value': entry.value,
                    'source': entry.source.value,
                    'timestamp': entry.timestamp.isoformat(),
                    'validated': entry.validated,
                    'encrypted': entry.encrypted,
                    'schema': {
                        'data_type': entry.schema.data_type.__name__ if entry.schema else None,
                        'required': entry.schema.required if entry.schema else None,
                        'description': entry.schema.description if entry.schema else None
                    } if entry.schema else None
                }

            return None

    def add_change_listener(self, key: str, listener: Callable[[str, Any, Any], None]):
        """
        添加配置变化监听器

        Args:
            key: 配置键
            listener: 监听器函数
        """
        with self._lock:
            full_key = self._get_full_key(key)
            self._change_listeners[full_key].append(listener)
            self.logger.debug(f"Added change listener for: {full_key}")

    def add_global_listener(self, listener: Callable[[str, Any, Any], None]):
        """
        添加全局配置变化监听器

        Args:
            listener: 监听器函数
        """
        with self._lock:
            self._global_listeners.append(listener)
            self.logger.debug("Added global change listener")

    def remove_change_listener(self, key: str, listener: Callable[[str, Any, Any], None]):
        """
        移除配置变化监听器

        Args:
            key: 配置键
            listener: 监听器函数
        """
        with self._lock:
            full_key = self._get_full_key(key)
            if full_key in self._change_listeners:
                try:
                    self._change_listeners[full_key].remove(listener)
                    self.logger.debug(f"Removed change listener for: {full_key}")
                except ValueError:
                    pass

    def push_namespace(self, namespace: str):
        """
        推入命名空间

        Args:
            namespace: 命名空间
        """
        with self._lock:
            self._namespace_stack.append(namespace)
            self.logger.debug(f"Pushed namespace: {namespace}")

    def pop_namespace(self) -> Optional[str]:
        """
        弹出命名空间

        Returns:
            Optional[str]: 弹出的命名空间
        """
        with self._lock:
            if self._namespace_stack:
                namespace = self._namespace_stack.pop()
                self.logger.debug(f"Popped namespace: {namespace}")
                return namespace
            return None

    def with_namespace(self, namespace: str):
        """
        命名空间上下文管理器

        Args:
            namespace: 命名空间
        """
        return NamespaceContext(self, namespace)

    def reload_configs(self):
        """重新加载所有配置"""
        with self._lock:
            try:
                # 清除当前配置（保留模式和默认值）
                configs_to_remove = [
                    key for key, entry in self._configs.items()
                    if entry.source != ConfigSource.DEFAULT
                ]

                for key in configs_to_remove:
                    del self._configs[key]

                # 重新加载
                self._load_from_environment()
                self._load_from_files()

                self._stats['reload_count'] += 1
                self._stats['last_reload'] = datetime.now()

                self.logger.info("Configuration reloaded")

            except Exception as e:
                self.logger.error(f"Failed to reload configs: {e}")
                raise

    def validate_all_configs(self) -> Dict[str, List[str]]:
        """
        验证所有配置

        Returns:
            Dict[str, List[str]]: 验证错误
        """
        errors = {}

        with self._lock:
            # 检查必需的配置
            for key, schema in self._schemas.items():
                if schema.required and key not in self._configs:
                    if key not in errors:
                        errors[key] = []
                    errors[key].append("Required configuration is missing")

            # 验证现有配置
            for key, entry in self._configs.items():
                try:
                    self._validate_config_value(key, entry.value)
                except ValueError as e:
                    if key not in errors:
                        errors[key] = []
                    errors[key].append(str(e))

        return errors

    def export_configs(self, format: ConfigFormat = ConfigFormat.JSON, include_defaults: bool = False) -> str:
        """
        导出配置

        Args:
            format: 导出格式
            include_defaults: 是否包含默认值

        Returns:
            str: 导出的配置内容
        """
        with self._lock:
            # 收集配置数据
            data = {}
            for key, entry in self._configs.items():
                if not include_defaults and entry.source == ConfigSource.DEFAULT:
                    continue

                # 构建嵌套字典
                keys = key.split('.')
                current = data
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = entry.value

            # 根据格式导出
            if format == ConfigFormat.JSON:
                return json.dumps(data, indent=2, default=str)
            elif format in [ConfigFormat.YAML, ConfigFormat.YML]:
                if not HAS_YAML:
                    raise ValueError("YAML support not available")
                return yaml.dump(data, default_flow_style=False)
            else:
                raise ValueError(f"Unsupported export format: {format}")

    def import_configs(self, content: str, format: ConfigFormat, source: ConfigSource = ConfigSource.FILE):
        """
        导入配置

        Args:
            content: 配置内容
            format: 配置格式
            source: 配置源
        """
        with self._lock:
            try:
                # 解析内容
                if format == ConfigFormat.JSON:
                    data = json.loads(content)
                elif format in [ConfigFormat.YAML, ConfigFormat.YML]:
                    if not HAS_YAML:
                        raise ValueError("YAML support not available")
                    data = yaml.safe_load(content)
                else:
                    raise ValueError(f"Unsupported import format: {format}")

                # 扁平化并设置配置
                flat_data = self._flatten_dict(data)
                for key, value in flat_data.items():
                    self._set_config_value(key, value, source)

                self.logger.info(f"Imported {len(flat_data)} configurations")

            except Exception as e:
                self.logger.error(f"Failed to import configs: {e}")
                raise

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = self._stats.copy()

            # 按源统计
            source_stats = {}
            for entry in self._configs.values():
                source = entry.source.value
                source_stats[source] = source_stats.get(source, 0) + 1

            stats['configs_by_source'] = source_stats
            stats['total_schemas'] = len(self._schemas)
            stats['config_dirs'] = len(self._config_dirs)
            stats['change_listeners'] = sum(len(listeners) for listeners in self._change_listeners.values())
            stats['global_listeners'] = len(self._global_listeners)

            return stats

    def clear_all_configs(self, keep_defaults: bool = True):
        """
        清除所有配置

        Args:
            keep_defaults: 是否保留默认值
        """
        with self._lock:
            if keep_defaults:
                # 只删除非默认配置
                configs_to_remove = [
                    key for key, entry in self._configs.items()
                    if entry.source != ConfigSource.DEFAULT
                ]
                for key in configs_to_remove:
                    del self._configs[key]
            else:
                # 删除所有配置
                self._configs.clear()

            self._stats['total_configs'] = len(self._configs)
            self.logger.info("Cleared all configurations")


class NamespaceContext:
    """命名空间上下文管理器"""

    def __init__(self, config_manager: ConfigManager, namespace: str):
        self.config_manager = config_manager
        self.namespace = namespace

    def __enter__(self):
        self.config_manager.push_namespace(self.namespace)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.config_manager.pop_namespace()


def create_config_schema(
    key: str,
    data_type: Type,
    required: bool = False,
    default_value: Any = None,
    description: str = "",
    **kwargs
) -> ConfigSchema:
    """
    创建配置模式的便捷函数

    Args:
        key: 配置键
        data_type: 数据类型
        required: 是否必需
        default_value: 默认值
        description: 描述
        **kwargs: 其他参数

    Returns:
        ConfigSchema: 配置模式
    """
    return ConfigSchema(
        key=key,
        data_type=data_type,
        required=required,
        default_value=default_value,
        description=description,
        **kwargs
    )
