# -*- coding: utf-8 -*-
"""
DFM模型分析页面组件

完全重构版本，与dfm_old_ui/dfm_ui.py保持完全一致
"""

import streamlit as st
import pandas as pd
import logging
import plotly.graph_objects as go
import numpy as np
import joblib
import pickle
import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime

# 添加路径以导入统一状态管理
current_dir = os.path.dirname(os.path.abspath(__file__))
dashboard_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..', '..'))
if dashboard_root not in sys.path:
    sys.path.insert(0, dashboard_root)

# 导入统一状态管理
from dashboard.state_management.refactor import get_global_dfm_refactor

# 配置日志记录器
logger = logging.getLogger(__name__)

# 配置已移除，所有参数通过UI设置
CONFIG_AVAILABLE = False


def get_dfm_refactor():
    """获取DFM重构模块实例（使用全局单例）"""
    try:
        dfm_refactor = get_global_dfm_refactor()
        if dfm_refactor is None:
            raise RuntimeError("全局DFM重构器不可用")
        return dfm_refactor
    except Exception as e:
        print(f"[DFM Model Analysis] Error getting DFM refactor: {e}")
        raise RuntimeError(f"DFM重构器获取失败: {e}")


def get_dfm_state(key, default=None):
    """获取DFM状态值（只使用统一状态管理）"""
    dfm_refactor = get_dfm_refactor()

    # 数据相关的键从data_prep命名空间获取，其他从model_analysis获取
    data_keys = [
        'dfm_prepared_data_df',
        'dfm_transform_log_obj',
        'dfm_industry_map_obj',
        'dfm_removed_vars_log_obj',
        'dfm_var_type_map_obj'
    ]

    if key in data_keys:
        return dfm_refactor.get_dfm_state('data_prep', key, default)
    else:
        return dfm_refactor.get_dfm_state('model_analysis', key, default)


def set_dfm_state(key, value):
    """设置DFM状态值（只使用统一状态管理）"""
    dfm_refactor = get_dfm_refactor()

    success = dfm_refactor.set_dfm_state('model_analysis', key, value)
    if not success:
        print(f"[DFM Model Analysis] ⚠️ 统一状态管理器设置失败: {key}")
    return success


def load_dfm_data() -> tuple[Optional[Any], Optional[Dict]]:
    """从统一状态管理加载模型结果和元数据。"""
    model_file = get_dfm_state('dfm_model_file_indep', None)
    metadata_file = get_dfm_state('dfm_metadata_file_indep', None)

    model_results = None
    metadata = None

    # 安全地检查模型文件是否存在且为有效的文件对象
    if model_file is not None:
        # 增加文件对象类型检查，避免对非文件对象调用seek方法
        if hasattr(model_file, 'seek') and hasattr(model_file, 'read'):
            try:
                model_file.seek(0)  # 重置文件指针
                model_results = joblib.load(model_file)
                logger.info("成功加载模型文件")
            except Exception as e:
                logger.error(f"加载模型文件失败: {e}")
                model_results = None
        else:
            logger.warning("模型文件不是有效的文件对象")

    # 安全地检查元数据文件是否存在且为有效的文件对象
    if metadata_file is not None:
        if hasattr(metadata_file, 'seek') and hasattr(metadata_file, 'read'):
            try:
                metadata_file.seek(0)  # 重置文件指针
                metadata = pickle.load(metadata_file)
                logger.info("成功加载元数据文件")
            except Exception as e:
                logger.error(f"加载元数据文件失败: {e}")
                metadata = None
        else:
            logger.warning("元数据文件不是有效的文件对象")

    return model_results, metadata


def load_dfm_results_from_uploads(model_results, metadata):
    """模拟后端处理函数"""
    load_errors = []

    # 简单的验证
    if model_results is None:
        load_errors.append("模型结果为空")
    if metadata is None:
        load_errors.append("元数据为空")

    return model_results, metadata, load_errors


def render_nowcasting_chart(nowcasting_df, target_variable, st_instance):
    """渲染Nowcasting图表"""
    if nowcasting_df is None or nowcasting_df.empty:
        st_instance.warning("无Nowcasting数据可显示")
        return

    fig = go.Figure()

    # 添加实际值
    if 'actual' in nowcasting_df.columns:
        fig.add_trace(go.Scatter(
            x=nowcasting_df.index,
            y=nowcasting_df['actual'],
            mode='lines+markers',
            name='实际值',
            line=dict(color='blue', width=2),
            marker=dict(size=4)
        ))

    # 添加预测值
    if 'predicted' in nowcasting_df.columns:
        fig.add_trace(go.Scatter(
            x=nowcasting_df.index,
            y=nowcasting_df['predicted'],
            mode='lines+markers',
            name='预测值',
            line=dict(color='red', width=2, dash='dash'),
            marker=dict(size=4)
        ))

    fig.update_layout(
        title=f'{target_variable} - Nowcasting结果',
        xaxis_title='时间',
        yaxis_title='值',
        hovermode='x unified',
        showlegend=True,
        height=400
    )

    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='LightGrey')
    fig.update_xaxes(showgrid=False)

    st_instance.plotly_chart(fig, use_container_width=True)


def render_file_upload_section(st_instance):
    """
    渲染文件上传区域
    """
    # 清理无效的文件状态
    def cleanup_invalid_file_states():
        """清理无效的文件状态，避免过期的文件对象导致错误"""
        model_file = get_dfm_state('dfm_model_file_indep', None)
        metadata_file = get_dfm_state('dfm_metadata_file_indep', None)

        # 检查模型文件是否有效
        if model_file is not None:
            if not (hasattr(model_file, 'seek') and hasattr(model_file, 'read')):
                logger.warning("检测到无效的模型文件对象，正在清理...")
                set_dfm_state('dfm_model_file_indep', None)

        # 检查元数据文件是否有效
        if metadata_file is not None:
            if not (hasattr(metadata_file, 'seek') and hasattr(metadata_file, 'read')):
                logger.warning("检测到无效的元数据文件对象，正在清理...")
                set_dfm_state('dfm_metadata_file_indep', None)

    # 执行清理
    cleanup_invalid_file_states()

    st_instance.markdown("#### 📁 步骤1: 模型加载")
    st_instance.markdown("**模型分析工作流**")

    # 文件上传区域
    col1, col2 = st_instance.columns(2)

    with col1:
        st_instance.markdown("**DFM 模型文件 (.joblib)**")
        model_file = st_instance.file_uploader(
            "上传模型文件",
            type=['joblib'],
            key="dfm_model_file_upload_indep",
            help="请上传训练好的DFM模型文件（.joblib格式）"
        )

        if model_file is not None:
            set_dfm_state('dfm_model_file_indep', model_file)
            st_instance.success(f"✅ 模型文件已上传: {model_file.name}")

    with col2:
        st_instance.markdown("**元数据文件 (.pkl)**")
        metadata_file = st_instance.file_uploader(
            "上传元数据文件",
            type=['pkl'],
            key="dfm_metadata_file_upload_indep",
            help="请上传对应的元数据文件（.pkl格式）"
        )

        if metadata_file is not None:
            set_dfm_state('dfm_metadata_file_indep', metadata_file)
            st_instance.success(f"✅ 元数据文件已上传: {metadata_file.name}")

    # 检查文件状态
    current_model_file = get_dfm_state('dfm_model_file_indep', None)
    current_metadata_file = get_dfm_state('dfm_metadata_file_indep', None)

    # 显示当前状态
    if current_model_file is not None and current_metadata_file is not None:
        st_instance.success("✅ 所有必需文件已上传，可以开始分析")
        return True
    else:
        missing_files = []
        if current_model_file is None:
            missing_files.append("模型文件")
        if current_metadata_file is None:
            missing_files.append("元数据文件")

        st_instance.warning(f"⚠️ 缺少文件: {', '.join(missing_files)}。请上传所有文件后再进行分析。")
        return False


def render_dfm_tab(st):
    """Renders the DFM Model Results tab using independently uploaded files."""

    # 添加文件上传区域
    st.markdown("---")
    files_ready = render_file_upload_section(st)

    if not files_ready:
        st.info("💡 请上传模型文件和元数据文件以继续分析。")
        return

    # 只有在文件准备好后才尝试加载数据
    try:
        model_results, metadata = load_dfm_data()
    except Exception as e:
        st.error(f"❌ 加载文件时出现意外错误: {e}")
        return

    if model_results is None or metadata is None:
        st.error("❌ 无法加载模型数据，请检查文件格式和内容。")
        return

    # 调用后端处理函数
    model, metadata, load_errors = load_dfm_results_from_uploads(model_results, metadata)

    all_errors = load_errors

    if all_errors:
        st.error("加载 DFM 相关文件时遇到错误:")
        for error in all_errors:
            st.error(f"- {error}")
        if model is None or metadata is None:
            return

    if model is None or metadata is None:
        st.warning("未能成功加载 DFM 模型或元数据。请检查文件内容或格式。")
        return

    st.success("成功加载 DFM 模型和元数据！")

    # --- 关键结果摘要 ---
    st.write(f"- **目标变量:** {metadata.get('target_variable', 'N/A')}")
    train_start = metadata.get('training_start_date', 'N/A')
    # 修正训练结束日期键名 - 训练模块使用 'training_end_date'
    train_end = metadata.get('training_end_date', metadata.get('train_end_date', 'N/A'))
    val_start = metadata.get('validation_start_date', 'N/A')
    val_end = metadata.get('validation_end_date', 'N/A')
    st.write(f"- **训练期:** {train_start} 至 {train_end}")
    st.write(f"- **验证期:** {val_start} 至 {val_end}")

    best_params_dict = metadata.get('best_params', {})
    var_select_method = best_params_dict.get('variable_selection_method', '未指定')
    tuning_objective = best_params_dict.get('tuning_objective', '未指定')
    st.write(f"- **变量选择方法:** {var_select_method} (优化目标: {tuning_objective})")
    if var_select_method == '未指定' or tuning_objective == '未指定':
        st.caption(":grey[注：未能从元数据 'best_params' 字典中找到 'variable_selection_method' 或 'tuning_objective'。]")
    st.markdown("---")  # 分隔线
    # --- 结束关键结果摘要 ---

    # 从元数据获取指标
    # 修复因子数量获取逻辑 - 多重回退策略
    k_factors = 'N/A'

    # 策略1: 从best_params获取 (支持多种键名)
    best_params = metadata.get('best_params', {})
    if isinstance(best_params, dict):
        # 尝试多种可能的键名
        possible_keys = ['k_factors', 'k_factors_final', 'best_k_factors']
        for key in possible_keys:
            if key in best_params:
                k_factors = best_params[key]
                logger.info(f"从best_params['{key}']获取k_factors: {k_factors}")
                break

    # 策略2: 从多种可能的因子数键获取
    if k_factors == 'N/A':
        factor_keys = ['n_factors', 'k_factors_final', 'best_k_factors']
        for key in factor_keys:
            value = metadata.get(key)
            if value is not None and value != 'N/A':
                k_factors = value
                logger.info(f"从{key}获取k_factors: {k_factors}")
                break

    # 策略3: 从optimal_k_factors获取
    if k_factors == 'N/A':
        optimal_k = metadata.get('optimal_k_factors')
        if optimal_k is not None and optimal_k != 'N/A':
            k_factors = optimal_k
            logger.info(f"从optimal_k_factors获取k_factors: {k_factors}")

    # 策略4: 从factor_loadings推断
    if k_factors == 'N/A':
        factor_loadings = metadata.get('factor_loadings_df')
        if factor_loadings is not None and hasattr(factor_loadings, 'columns'):
            k_factors = len(factor_loadings.columns)
            logger.info(f"从factor_loadings推断k_factors: {k_factors}")

    # 策略5: 从factor_series推断
    if k_factors == 'N/A':
        factor_series = metadata.get('factor_series_df')
        if factor_series is not None and hasattr(factor_series, 'columns'):
            k_factors = len(factor_series.columns)
            logger.info(f"从factor_series推断k_factors: {k_factors}")

    # 显示模型信息
    st.markdown("#### 📊 模型信息")
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("因子数量", k_factors)

    with col2:
        # 获取变量数量
        n_variables = metadata.get('n_variables', 'N/A')
        if n_variables == 'N/A':
            factor_loadings = metadata.get('factor_loadings_df')
            if factor_loadings is not None and hasattr(factor_loadings, 'index'):
                n_variables = len(factor_loadings.index)
        st.metric("变量数量", n_variables)

    with col3:
        # 获取观测数量
        n_observations = metadata.get('n_observations', 'N/A')
        st.metric("观测数量", n_observations)

    # 显示Nowcasting结果（如果有）
    nowcasting_df = metadata.get('nowcasting_df')
    if nowcasting_df is not None and not nowcasting_df.empty:
        st.markdown("#### 📈 Nowcasting结果")
        target_variable = metadata.get('target_variable', '目标变量')
        render_nowcasting_chart(nowcasting_df, target_variable, st)
    else:
        st.info("📈 暂无Nowcasting结果数据")

    # 显示因子载荷（如果有）
    factor_loadings = metadata.get('factor_loadings_df')
    if factor_loadings is not None and not factor_loadings.empty:
        st.markdown("#### 🔍 因子载荷矩阵")
        with st.expander("查看因子载荷详情", expanded=False):
            st.dataframe(factor_loadings, use_container_width=True)
    else:
        st.info("🔍 暂无因子载荷数据")

    # 显示因子时间序列（如果有）
    factor_series = metadata.get('factor_series_df')
    if factor_series is not None and not factor_series.empty:
        st.markdown("#### 📊 因子时间序列")
        with st.expander("查看因子时间序列详情", expanded=False):
            st.line_chart(factor_series)
    else:
        st.info("📊 暂无因子时间序列数据")


def render_dfm_model_analysis_page(st_module: Any) -> Dict[str, Any]:
    """
    渲染DFM模型分析页面

    Args:
        st_module: Streamlit模块

    Returns:
        Dict[str, Any]: 渲染结果
    """
    try:
        # 调用主要的UI渲染函数
        render_dfm_tab(st_module)

        return {
            'status': 'success',
            'page': 'model_analysis',
            'components': ['file_upload', 'model_info', 'nowcasting', 'factor_analysis']
        }

    except Exception as e:
        st_module.error(f"模型分析页面渲染失败: {str(e)}")
        return {
            'status': 'error',
            'page': 'model_analysis',
            'error': str(e)
        }


def render_dfm_analysis_tab(st_module: Any) -> Dict[str, Any]:
    """
    兼容性接口：渲染DFM模型分析标签页

    Args:
        st_module: Streamlit模块

    Returns:
        Dict[str, Any]: 渲染结果
    """
    return render_dfm_model_analysis_page(st_module)
