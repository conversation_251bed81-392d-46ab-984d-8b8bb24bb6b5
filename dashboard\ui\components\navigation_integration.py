"""
导航系统集成组件
提供工业数据预览模块的导航集成功能
"""

import streamlit as st
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class IndustrialPreviewNavigationIntegration:
    """
    工业数据预览导航集成类
    负责将工业数据预览模块集成到主导航系统中
    """
    
    def __init__(self, nav_manager):
        """
        初始化导航集成
        
        Args:
            nav_manager: 导航管理器实例
        """
        self.nav_manager = nav_manager
        self.module_name = "工业数据预览"
        self.routes = IndustrialPreviewRoutes()
        self.module_config = self._create_module_config()
        
        logger.info(f"初始化工业数据预览导航集成")
    
    def _create_module_config(self) -> Dict[str, Any]:
        """创建模块配置"""
        return {
            'name': self.module_name,
            'icon': '🏭',
            'description': '工业数据分析和预览',
            'order': 2,  # 在主导航中的顺序
            'permissions': ['read', 'analyze'],
            'sub_modules': [
                {
                    'name': '数据概览',
                    'route': '/industrial/overview',
                    'icon': '📊',
                    'description': '数据总览和摘要'
                },
                {
                    'name': '日度数据',
                    'route': '/industrial/daily',
                    'icon': '📅',
                    'description': '日度数据分析'
                },
                {
                    'name': '周度数据',
                    'route': '/industrial/weekly',
                    'icon': '📈',
                    'description': '周度数据分析'
                },
                {
                    'name': '月度数据',
                    'route': '/industrial/monthly',
                    'icon': '📆',
                    'description': '月度数据分析'
                },
                {
                    'name': '扩散分析',
                    'route': '/industrial/diffusion',
                    'icon': '🔍',
                    'description': '扩散指数分析'
                }
            ]
        }
    
    def register_industrial_preview_module(self) -> Dict[str, Any]:
        """
        注册工业数据预览模块到主导航
        
        Returns:
            注册结果
        """
        try:
            # 注册主模块
            main_module_result = self._register_main_module()
            
            # 注册子模块路由
            sub_routes_result = self._register_sub_module_routes()
            
            # 注册权限
            permissions_result = self._register_permissions()
            
            result = {
                'success': True,
                'module_name': self.module_name,
                'main_module': main_module_result,
                'routes': sub_routes_result,
                'permissions': permissions_result
            }
            
            logger.info(f"成功注册工业数据预览模块")
            return result
            
        except Exception as e:
            logger.error(f"注册工业数据预览模块失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _register_main_module(self) -> Dict[str, Any]:
        """注册主模块"""
        return {
            'name': self.module_name,
            'config': self.module_config,
            'registered': True
        }
    
    def _register_sub_module_routes(self) -> Dict[str, Any]:
        """注册子模块路由"""
        routes = {}
        
        for sub_module in self.module_config['sub_modules']:
            route_name = sub_module['name']
            route_config = {
                'path': sub_module['route'],
                'component': f"Industrial{route_name.replace(' ', '')}Component",
                'icon': sub_module['icon'],
                'description': sub_module['description']
            }
            
            routes[route_name] = route_config
            
            # 注册到路由系统
            self.routes.register_custom_route(route_name, route_config)
        
        return routes
    
    def _register_permissions(self) -> Dict[str, Any]:
        """注册权限"""
        return {
            'module': self.module_name,
            'permissions': self.module_config['permissions'],
            'registered': True
        }
    
    def get_module_routes(self) -> Dict[str, Any]:
        """
        获取模块路由配置
        
        Returns:
            路由配置字典
        """
        return {
            'main': {
                'name': self.module_name,
                'path': '/industrial',
                'component': 'IndustrialPreviewMainComponent'
            },
            'sub_modules': {
                sub_module['name']: {
                    'path': sub_module['route'],
                    'component': f"Industrial{sub_module['name'].replace(' ', '')}Component",
                    'icon': sub_module['icon']
                }
                for sub_module in self.module_config['sub_modules']
            }
        }
    
    def create_navigation_menu(self) -> Dict[str, Any]:
        """
        创建导航菜单配置
        
        Returns:
            菜单配置字典
        """
        return {
            'title': self.module_name,
            'icon': self.module_config['icon'],
            'route': '/industrial',
            'order': self.module_config['order'],
            'sub_items': [
                {
                    'name': sub_module['name'],
                    'route': sub_module['route'],
                    'icon': sub_module['icon'],
                    'description': sub_module['description']
                }
                for sub_module in self.module_config['sub_modules']
            ]
        }
    
    def handle_navigation_change(self, target_type: str, target_value: str) -> Dict[str, Any]:
        """
        处理导航变化
        
        Args:
            target_type: 目标类型 ('main_module' 或 'sub_module')
            target_value: 目标值
            
        Returns:
            处理结果
        """
        try:
            if target_type == 'main_module':
                self.nav_manager.set_main_module(target_value)
                logger.info(f"导航到主模块: {target_value}")
                
            elif target_type == 'sub_module':
                # 验证子模块是否有效
                if self.validate_navigation_permissions(target_value):
                    self.nav_manager.set_sub_module(target_value)
                    logger.info(f"导航到子模块: {target_value}")
                else:
                    raise ValueError(f"无效的子模块: {target_value}")
            
            return {
                'success': True,
                'target_type': target_type,
                'target_value': target_value
            }
            
        except Exception as e:
            logger.error(f"导航变化处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_navigation_permissions(self, target_module: str) -> bool:
        """
        验证导航权限
        
        Args:
            target_module: 目标模块名
            
        Returns:
            是否有权限访问
        """
        valid_modules = [sub_module['name'] for sub_module in self.module_config['sub_modules']]
        return target_module in valid_modules


class IndustrialPreviewRoutes:
    """
    工业数据预览路由管理类
    """
    
    def __init__(self):
        """初始化路由管理"""
        self.route_map = {}
        self.component_map = {}
        self._initialize_default_routes()
    
    def _initialize_default_routes(self):
        """初始化默认路由"""
        default_routes = {
            '数据概览': {
                'path': '/industrial/overview',
                'component': 'IndustrialOverviewComponent'
            },
            '日度数据': {
                'path': '/industrial/daily',
                'component': 'IndustrialDailyComponent'
            },
            '周度数据': {
                'path': '/industrial/weekly',
                'component': 'IndustrialWeeklyComponent'
            },
            '月度数据': {
                'path': '/industrial/monthly',
                'component': 'IndustrialMonthlyComponent'
            },
            '扩散分析': {
                'path': '/industrial/diffusion',
                'component': 'IndustrialDiffusionComponent'
            }
        }
        
        for module_name, route_config in default_routes.items():
            self.register_custom_route(module_name, route_config)
    
    def get_route_for_module(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模块对应的路由
        
        Args:
            module_name: 模块名
            
        Returns:
            路由配置或None
        """
        return self.route_map.get(module_name)
    
    def get_component_for_route(self, route_path: str) -> Optional[str]:
        """
        获取路由对应的组件
        
        Args:
            route_path: 路由路径
            
        Returns:
            组件名或None
        """
        return self.component_map.get(route_path)
    
    def register_custom_route(self, module_name: str, route_config: Dict[str, Any]) -> bool:
        """
        注册自定义路由
        
        Args:
            module_name: 模块名
            route_config: 路由配置
            
        Returns:
            是否注册成功
        """
        try:
            self.route_map[module_name] = route_config
            self.component_map[route_config['path']] = route_config['component']
            return True
        except Exception as e:
            logger.error(f"注册路由失败: {e}")
            return False


class IndustrialPreviewBreadcrumb:
    """
    工业数据预览面包屑导航类
    """
    
    def __init__(self):
        """初始化面包屑导航"""
        self.base_items = [
            {'name': '首页', 'route': '/', 'active': False}
        ]
    
    def generate_breadcrumb_items(
        self, 
        main_module: str, 
        sub_module: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        生成面包屑项目
        
        Args:
            main_module: 主模块名
            sub_module: 子模块名（可选）
            
        Returns:
            面包屑项目列表
        """
        items = self.base_items.copy()
        
        # 添加主模块
        items.append({
            'name': main_module,
            'route': '/industrial',
            'active': sub_module is None
        })
        
        # 添加子模块（如果存在）
        if sub_module:
            items.append({
                'name': sub_module,
                'route': f'/industrial/{sub_module.lower()}',
                'active': True
            })
        
        return items
    
    def render_breadcrumb(self, items: List[Dict[str, Any]]):
        """
        渲染面包屑导航
        
        Args:
            items: 面包屑项目列表
        """
        html = self.get_breadcrumb_html(items)
        st.markdown(html, unsafe_allow_html=True)
    
    def get_breadcrumb_html(self, items: List[Dict[str, Any]]) -> str:
        """
        获取面包屑HTML
        
        Args:
            items: 面包屑项目列表
            
        Returns:
            HTML字符串
        """
        html = "<div style='margin-bottom: 1rem; color: #666; font-size: 14px;'>"
        
        for i, item in enumerate(items):
            if i > 0:
                html += " > "
            
            if item.get('active', False):
                html += f"<strong style='color: #1f77b4;'>{item['name']}</strong>"
            else:
                html += f"<span style='color: #666;'>{item['name']}</span>"
        
        html += "</div>"
        return html
