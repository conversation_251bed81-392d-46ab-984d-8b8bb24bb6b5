# -*- coding: utf-8 -*-
"""
模块重构基类
提供模块重构的通用功能和接口
"""

import streamlit as st
from typing import Dict, Any, Optional, List, Callable
from abc import ABC, abstractmethod
from datetime import datetime

from ..unified_state_manager import UnifiedStateManager
# 避免循环导入，在运行时动态导入


class ModuleRefactorBase(ABC):
    """模块重构基类 - 提供统一的状态管理接口"""
    
    def __init__(
        self,
        module_name: str,
        unified_manager: UnifiedStateManager = None
    ):
        """
        初始化模块重构基类

        Args:
            module_name: 模块名称
            unified_manager: 统一状态管理器实例
        """
        self.module_name = module_name
        self.unified_manager = unified_manager or self._get_global_manager()

        # 状态键前缀
        self.state_prefix = f"{module_name}."

        # 初始化logger属性
        self._logger = None

        # 初始化模块
        self._initialize_module()
    
    def _get_global_manager(self) -> UnifiedStateManager:
        """获取全局统一状态管理器"""
        # 避免循环导入，使用已传入的unified_manager实例
        if self.unified_manager is not None:
            return self.unified_manager
        
        # 如果没有传入实例，动态导入并创建
        try:
            from ..unified_state_manager import UnifiedStateManager
            return UnifiedStateManager()
        except Exception as e:
            print(f"Failed to create UnifiedStateManager: {e}")
            return None
    
    @abstractmethod
    def _initialize_module(self):
        """初始化模块 - 子类必须实现"""
        pass
    
    # 统一状态管理接口
    def get_state(self, key: str, default: Any = None) -> Any:
        """获取状态"""
        # 只从统一状态管理器获取，不再回退到session_state
        if self.unified_manager:
            value = self._get_from_unified_manager(key, default)
            self.log_debug(f"State retrieved: {key} = {type(value).__name__}")
            return value

        # 如果统一管理器不可用，记录错误并返回默认值
        self.log_error(f"Unified manager not available for key: {key}")
        return default
    
    def set_state(self, key: str, value: Any) -> bool:
        """设置状态"""
        # 只使用统一状态管理器，不再双重存储
        success = self._set_to_unified_manager(key, value)

        if success:
            self.log_debug(f"State set successfully: {key}")
        else:
            self.log_error(f"Failed to set state: {key}")

        return success
    
    def delete_state(self, key: str) -> bool:
        """删除状态"""
        # 只从统一状态管理器删除
        success = self._delete_from_unified_manager(key)

        if success:
            self.log_debug(f"State deleted successfully: {key}")
        else:
            self.log_error(f"Failed to delete state: {key}")

        return success
    
    def clear_module_state(self):
        """清空模块状态"""
        # 只清空统一状态管理器中的模块状态
        self._clear_unified_manager_state()
        self.log_info(f"Module state cleared for: {self.module_name}")
    
    @abstractmethod
    def _get_from_unified_manager(self, key: str, default: Any = None) -> Any:
        """从统一状态管理器获取状态 - 子类必须实现"""
        pass
    
    @abstractmethod
    def _set_to_unified_manager(self, key: str, value: Any) -> bool:
        """设置到统一状态管理器 - 子类必须实现"""
        pass
    
    @abstractmethod
    def _delete_from_unified_manager(self, key: str) -> bool:
        """从统一状态管理器删除状态 - 子类必须实现"""
        pass
    
    @abstractmethod
    def _clear_unified_manager_state(self):
        """清空统一状态管理器中的模块状态 - 子类必须实现"""
        pass
    
    # 通用UI辅助方法
    def create_ui_component(
        self,
        component_type: str,
        component_id: str,
        config: Dict[str, Any]
    ) -> str:
        """创建UI组件"""
        if self.unified_manager:
            full_config = {
                'component_type': component_type,
                'component_id': component_id,
                'module': self.module_name,
                **config
            }
            return self.unified_manager.create_ui_component(full_config)
        return ""
    
    def update_ui_component(self, component_id: str, updates: Dict[str, Any]) -> bool:
        """更新UI组件"""
        if self.unified_manager:
            return self.unified_manager.update_ui_component(component_id, updates)
        return False
    
    # 数据存储辅助方法
    def store_data(self, key: str, data: Any, storage_type: str = "memory") -> bool:
        """存储数据"""
        if self.unified_manager:
            full_key = f"{self.module_name}.{key}"
            return self.unified_manager.store_data(full_key, data, storage_type)
        return False
    
    def retrieve_data(self, key: str, default: Any = None) -> Any:
        """检索数据"""
        if self.unified_manager:
            full_key = f"{self.module_name}.{key}"
            return self.unified_manager.retrieve_data(full_key, default)
        return default
    
    # 导航辅助方法
    def navigate_to(self, path: str) -> bool:
        """导航到指定路径"""
        if self.unified_manager:
            return self.unified_manager.navigate_to(path)
        return False
    
    def add_navigation_item(
        self,
        item_id: str,
        label: str,
        path: str,
        icon: str = None,
        parent_id: str = None,
        order: int = 0
    ) -> bool:
        """添加导航项"""
        if self.unified_manager:
            return self.unified_manager.add_navigation_item(
                item_id, label, path, icon, parent_id, order
            )
        return False
    
    # 性能监控辅助方法
    def record_metric(self, metric_name: str, value: float, tags: Dict[str, str] = None):
        """记录性能指标"""
        if self.unified_manager and self.unified_manager.performance_monitor:
            full_tags = {'module': self.module_name}
            if tags:
                full_tags.update(tags)
            self.unified_manager.performance_monitor.record_metric(
                metric_name, value, full_tags
            )
    
    def start_timer(self, operation_name: str) -> str:
        """开始计时"""
        if self.unified_manager and self.unified_manager.performance_monitor:
            return self.unified_manager.performance_monitor.start_timer(
                f"{self.module_name}.{operation_name}"
            )
        return ""
    
    def end_timer(self, timer_id: str):
        """结束计时"""
        if self.unified_manager and self.unified_manager.performance_monitor:
            self.unified_manager.performance_monitor.end_timer(timer_id)
    
    # 错误处理辅助方法
    def handle_error(self, error: Exception, context: Dict[str, Any] = None):
        """处理错误"""
        if self.unified_manager and self.unified_manager.error_handler:
            full_context = {'module': self.module_name}
            if context:
                full_context.update(context)
            self.unified_manager.error_handler.handle_error(error, full_context)
    
    # 日志记录辅助方法
    @property
    def logger(self):
        """获取日志记录器属性"""
        if self._logger is None:
            self._logger = self.get_logger()
        return self._logger

    def get_logger(self):
        """获取日志记录器"""
        if self.unified_manager and self.unified_manager.logging_monitor:
            return self.unified_manager.logging_monitor.get_logger(self.module_name)
        return None
    
    def log_info(self, message: str, extra: Dict[str, Any] = None):
        """记录信息日志"""
        logger = self.get_logger()
        if logger:
            logger.info(message, extra=extra)
    
    def log_debug(self, message: str, extra: Dict[str, Any] = None):
        """记录调试日志"""
        logger = self.get_logger()
        if logger:
            logger.debug(message, extra=extra)

    def log_warning(self, message: str, extra: Dict[str, Any] = None):
        """记录警告日志"""
        logger = self.get_logger()
        if logger:
            logger.warning(message, extra=extra)

    def log_error(self, message: str, extra: Dict[str, Any] = None):
        """记录错误日志"""
        logger = self.get_logger()
        if logger:
            logger.error(message, extra=extra)
    
    # 模块生命周期方法
    def on_module_load(self):
        """模块加载时调用"""
        # 检查是否已经记录过加载信息，避免重复日志
        if hasattr(self, '_load_logged') and self._load_logged:
            return
        self.log_info(f"Module {self.module_name} loaded")
        self._load_logged = True
    
    def on_module_unload(self):
        """模块卸载时调用"""
        self.log_info(f"Module {self.module_name} unloaded")
    
    def on_state_change(self, key: str, old_value: Any, new_value: Any):
        """状态变化时调用"""
        self.log_info(f"State changed: {key} = {new_value}")
    
    # 兼容性方法
    def migrate_session_state(self, mapping: Dict[str, str] = None):
        """迁移旧状态到统一状态管理器"""
        if not mapping:
            # 默认映射：直接迁移所有模块相关的状态
            mapping = {}
            # 从统一状态管理器获取所有键
            if self.unified_manager:
                try:
                    # 获取统一状态管理器的统计信息来获取状态列表
                    stats = self.unified_manager.get_system_statistics()
                    if stats and 'adapters' in stats:
                        for adapter_name, adapter_stats in stats['adapters'].items():
                            if hasattr(adapter_stats, 'states'):
                                for key in adapter_stats.states:
                                    if key.startswith(self.state_prefix):
                                        clean_key = key[len(self.state_prefix):]
                                        mapping[key] = clean_key
                except:
                    pass  # 如果获取失败，使用空映射
        
        for session_key, unified_key in mapping.items():
            # 从统一状态管理器获取旧值并设置到新位置
            value = self._get_from_unified_manager(session_key, None)
            if value is not None:
                self._set_to_unified_manager(unified_key, value)
                self.log_info(f"Migrated state: {session_key} -> {unified_key}")
    
    def get_module_statistics(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        state_count = 0
        if self.unified_manager:
            try:
                # 尝试从统一状态管理器获取状态数量
                stats = self.unified_manager.get_system_statistics()
                if stats and 'adapters' in stats:
                    for adapter_stats in stats['adapters'].values():
                        if hasattr(adapter_stats, 'states'):
                            state_count += len([k for k in adapter_stats.states if k.startswith(self.state_prefix)])
            except:
                state_count = 0
        
        return {
            'module_name': self.module_name,
            'state_count': state_count,
            'last_activity': datetime.now(),
            'unified_manager_connected': self.unified_manager is not None
        }
