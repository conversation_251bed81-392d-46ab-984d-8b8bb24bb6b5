# -*- coding: utf-8 -*-
"""
监测分析欢迎页面组件
"""

from typing import List
from dashboard.ui.components.base import UIComponent
from dashboard.ui.constants import UIConstants


class MonitoringAnalysisWelcomePage(UIComponent):
    """监测分析欢迎页面"""
    
    def __init__(self):
        self.constants = UIConstants
        self.module_config = self.constants.MAIN_MODULES["监测分析"]
    
    def render(self, st_obj, **kwargs) -> None:
        """渲染监测分析欢迎页面"""
        # 只显示标题和介绍
        self._render_header(st_obj)
    
    def _render_header(self, st_obj):
        """渲染页面头部"""
        st_obj.markdown(f"""
        <div style="text-align: center; padding: 3rem 0 2rem 0;">
            <div style="font-size: 4em; margin-bottom: 1rem;">{self.module_config['icon']}</div>
            <h1 style="color: #333; margin-bottom: 1rem; font-weight: 700;">监测分析</h1>
            <p style="font-size: 1.3em; color: #666; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                {self.module_config['description']}
            </p>
        </div>
        """, unsafe_allow_html=True)

    def _render_sub_modules(self, st_obj):
        """渲染子模块选择卡片"""
        st_obj.markdown("### 选择分析领域")

        # 创建两列布局
        col1, col2 = st_obj.columns(2)

        with col1:
            # 工业监测分析卡片
            if st_obj.button(
                "🏭 工业",
                key="nav_monitoring_analysis_industrial",
                use_container_width=True,
                help="进行工业增加值和工业企业利润拆解分析"
            ):
                # 使用统一状态管理器
                try:
                    from dashboard.state_management import get_unified_manager
                    import time
                    state_manager = get_unified_manager()
                    if state_manager:
                        # 设置导航时间戳，用于循环渲染检测
                        current_time = time.time()
                        state_manager.set_state('dashboard.last_navigation_time', current_time)
                        state_manager.set_state('navigation.navigate_to_sub_module', '工业')
                    else:
                        # 降级到session_state
                        st_obj.session_state['navigate_to_sub_module'] = '工业'
                except ImportError:
                    # 降级到session_state
                    st_obj.session_state['navigate_to_sub_module'] = '工业'
                st_obj.rerun()

        with col2:
            # 消费监测分析卡片
            if st_obj.button(
                "🛒 消费",
                key="nav_monitoring_analysis_consumption",
                use_container_width=True,
                help="进行消费宏观运行和企业经营分析"
            ):
                # 使用统一状态管理器
                try:
                    from dashboard.state_management import get_unified_manager
                    import time
                    state_manager = get_unified_manager()
                    if state_manager:
                        # 设置导航时间戳，用于循环渲染检测
                        current_time = time.time()
                        state_manager.set_state('dashboard.last_navigation_time', current_time)
                        state_manager.set_state('navigation.navigate_to_sub_module', '消费')
                    else:
                        # 降级到session_state
                        st_obj.session_state['navigate_to_sub_module'] = '消费'
                except ImportError:
                    # 降级到session_state
                    st_obj.session_state['navigate_to_sub_module'] = '消费'
                st_obj.rerun()

        # 添加一些说明文字
        st_obj.markdown("""
        <div style="margin-top: 2rem; padding: 1rem; background-color: #f8f9fa; border-radius: 0.5rem;">
            <h4 style="color: #495057; margin-bottom: 0.5rem;">💡 使用提示</h4>
            <ul style="color: #6c757d; margin-bottom: 0;">
                <li><strong>工业</strong>：提供工业增加值分析和工业企业利润拆解功能</li>
                <li><strong>消费</strong>：提供宏观运行监测和企业经营分析功能</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    def _navigate_to_sub_module(self, st_obj, sub_module: str):
        """导航到子模块"""
        try:
            # 使用统一状态管理器设置导航状态
            from dashboard.state_management import get_unified_manager
            from dashboard.state_management.refactor.navigation_module_refactor import NavigationModuleRefactor

            unified_manager = get_unified_manager()
            if not unified_manager:
                st_obj.error("统一状态管理器不可用")
                return

            nav_manager = NavigationModuleRefactor(unified_manager)

            # 设置子模块
            nav_manager.set_current_sub_module(sub_module)

            # 强制重新渲染
            st_obj.rerun()

        except Exception as e:
            st_obj.error(f"导航失败: {e}")

    def _handle_navigation(self, st_obj):
        """处理导航事件"""
        try:
            from dashboard.state_management import get_unified_manager
            state_manager = get_unified_manager()

            if state_manager:
                # 使用统一状态管理器检查导航请求
                sub_module = state_manager.get_state('navigation.navigate_to_sub_module')
                if sub_module:
                    state_manager.clear_state('navigation.navigate_to_sub_module')
                    self._navigate_to_sub_module(st_obj, sub_module)
            else:
                # 降级到session_state
                if 'navigate_to_sub_module' in st_obj.session_state:
                    sub_module = st_obj.session_state.pop('navigate_to_sub_module')
                    self._navigate_to_sub_module(st_obj, sub_module)
        except ImportError:
            # 降级到session_state
            if 'navigate_to_sub_module' in st_obj.session_state:
                sub_module = st_obj.session_state.pop('navigate_to_sub_module')
                self._navigate_to_sub_module(st_obj, sub_module)
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return ['navigate_to_sub_module']
