# -*- coding: utf-8 -*-
"""
智能LRU缓存管理器
提供高性能的缓存管理，支持LRU策略、TTL、内存限制和性能监控
"""

import time
import threading
import logging
import psutil
import os
from typing import Any, Dict, Optional, List, Tuple
from collections import OrderedDict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class CachePolicy(Enum):
    """缓存策略枚举"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL_ONLY = "ttl_only"  # 仅基于TTL


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_time: float = field(default_factory=time.time)
    last_accessed: float = field(default_factory=time.time)
    access_count: int = 0
    ttl: Optional[float] = None  # 生存时间（秒）
    size_bytes: int = 0  # 条目大小（字节）
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_time > self.ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_accessed = time.time()
        self.access_count += 1


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    expired_removals: int = 0
    total_size_bytes: int = 0
    entry_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 1.0 - self.hit_rate


class IntelligentLRUCache:
    """智能LRU缓存管理器"""
    
    def __init__(self,
                 max_size: int = 1000,
                 max_memory_mb: float = 100.0,
                 default_ttl: Optional[float] = None,
                 policy: CachePolicy = CachePolicy.LRU,
                 cleanup_interval: float = 60.0,
                 enable_stats: bool = True):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大条目数
            max_memory_mb: 最大内存使用（MB）
            default_ttl: 默认TTL（秒）
            policy: 缓存策略
            cleanup_interval: 清理间隔（秒）
            enable_stats: 是否启用统计
        """
        self.max_size = max_size
        self.max_memory_bytes = int(max_memory_mb * 1024 * 1024)
        self.default_ttl = default_ttl
        self.policy = policy
        self.cleanup_interval = cleanup_interval
        self.enable_stats = enable_stats
        
        # 缓存存储
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = CacheStats()
        
        # 清理线程
        self._cleanup_thread = None
        self._stop_cleanup = threading.Event()
        
        # 启动清理线程
        self._start_cleanup_thread()
        
        logger.info(f"智能LRU缓存初始化完成 - 最大条目: {max_size}, 最大内存: {max_memory_mb}MB")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            entry = self._cache.get(key)
            
            if entry is None:
                if self.enable_stats:
                    self.stats.misses += 1
                logger.debug(f"缓存未命中: {key}")
                return default
            
            # 检查是否过期
            if entry.is_expired():
                self._remove_entry(key)
                if self.enable_stats:
                    self.stats.misses += 1
                    self.stats.expired_removals += 1
                logger.debug(f"缓存过期: {key}")
                return default
            
            # 更新访问信息
            entry.touch()
            
            # LRU策略：移动到末尾
            if self.policy == CachePolicy.LRU:
                self._cache.move_to_end(key)
            
            if self.enable_stats:
                self.stats.hits += 1
            
            logger.debug(f"缓存命中: {key}")
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            try:
                # 计算值的大小
                size_bytes = self._estimate_size(value)
                
                # 检查内存限制
                if size_bytes > self.max_memory_bytes:
                    logger.warning(f"值太大，无法缓存: {key}, 大小: {size_bytes} bytes")
                    return False
                
                # 如果键已存在，先移除
                if key in self._cache:
                    self._remove_entry(key)
                
                # 确保有足够空间
                self._ensure_capacity(size_bytes)
                
                # 创建新条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    ttl=ttl or self.default_ttl,
                    size_bytes=size_bytes
                )
                
                # 添加到缓存
                self._cache[key] = entry
                
                # 更新统计
                if self.enable_stats:
                    self.stats.total_size_bytes += size_bytes
                    self.stats.entry_count += 1
                
                logger.debug(f"缓存设置成功: {key}, 大小: {size_bytes} bytes")
                return True
                
            except Exception as e:
                logger.error(f"设置缓存失败: {key} - {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                self._remove_entry(key)
                logger.debug(f"缓存删除: {key}")
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self.stats = CacheStats()
            logger.info("缓存已清空")
    
    def has_key(self, key: str) -> bool:
        """检查键是否存在且未过期"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return False
            
            if entry.is_expired():
                self._remove_entry(key)
                return False
            
            return True
    
    def keys(self) -> List[str]:
        """获取所有有效键"""
        with self._lock:
            valid_keys = []
            expired_keys = []
            
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
                else:
                    valid_keys.append(key)
            
            # 移除过期键
            for key in expired_keys:
                self._remove_entry(key)
            
            return valid_keys
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'hits': self.stats.hits,
                'misses': self.stats.misses,
                'hit_rate': self.stats.hit_rate,
                'miss_rate': self.stats.miss_rate,
                'evictions': self.stats.evictions,
                'expired_removals': self.stats.expired_removals,
                'entry_count': len(self._cache),
                'total_size_bytes': self.stats.total_size_bytes,
                'total_size_mb': self.stats.total_size_bytes / (1024 * 1024),
                'max_size': self.max_size,
                'max_memory_mb': self.max_memory_bytes / (1024 * 1024),
                'policy': self.policy.value
            }
    
    def _remove_entry(self, key: str):
        """移除缓存条目"""
        entry = self._cache.pop(key, None)
        if entry and self.enable_stats:
            self.stats.total_size_bytes -= entry.size_bytes
            self.stats.entry_count -= 1
    
    def _ensure_capacity(self, new_size: int):
        """确保有足够的容量"""
        # 检查条目数量限制
        while len(self._cache) >= self.max_size:
            self._evict_one()
        
        # 检查内存限制
        while (self.stats.total_size_bytes + new_size) > self.max_memory_bytes:
            if not self._evict_one():
                break  # 无法继续驱逐
    
    def _evict_one(self) -> bool:
        """驱逐一个条目"""
        if not self._cache:
            return False
        
        if self.policy == CachePolicy.LRU:
            # LRU: 移除最久未使用的
            key = next(iter(self._cache))
        elif self.policy == CachePolicy.LFU:
            # LFU: 移除使用频率最低的
            key = min(self._cache.keys(), key=lambda k: self._cache[k].access_count)
        elif self.policy == CachePolicy.FIFO:
            # FIFO: 移除最早创建的
            key = min(self._cache.keys(), key=lambda k: self._cache[k].created_time)
        else:
            # 默认LRU
            key = next(iter(self._cache))
        
        self._remove_entry(key)
        
        if self.enable_stats:
            self.stats.evictions += 1
        
        logger.debug(f"驱逐缓存条目: {key}")
        return True
    
    def _estimate_size(self, value: Any) -> int:
        """估算值的大小"""
        try:
            import sys
            return sys.getsizeof(value)
        except Exception:
            # 简单估算
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (list, tuple)):
                return sum(self._estimate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) for k, v in value.items())
            else:
                return 64  # 默认大小
    
    def _cleanup_expired(self):
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_entry(key)
                if self.enable_stats:
                    self.stats.expired_removals += 1
            
            if expired_keys:
                logger.debug(f"清理过期条目: {len(expired_keys)}个")
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        if self.cleanup_interval > 0:
            self._cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
            self._cleanup_thread.start()
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while not self._stop_cleanup.wait(self.cleanup_interval):
            try:
                self._cleanup_expired()
            except Exception as e:
                logger.error(f"清理线程错误: {e}")
    
    def shutdown(self):
        """关闭缓存管理器"""
        self._stop_cleanup.set()
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5.0)
        self.clear()
        logger.info("智能LRU缓存已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except Exception:
            pass


# 全局缓存实例
_global_cache: Optional[IntelligentLRUCache] = None
_cache_lock = threading.Lock()


def get_global_cache() -> IntelligentLRUCache:
    """获取全局缓存实例"""
    global _global_cache
    
    if _global_cache is None:
        with _cache_lock:
            if _global_cache is None:
                _global_cache = IntelligentLRUCache(
                    max_size=2000,
                    max_memory_mb=200.0,
                    default_ttl=3600.0,  # 1小时
                    cleanup_interval=300.0  # 5分钟清理一次
                )
    
    return _global_cache


def shutdown_global_cache():
    """关闭全局缓存"""
    global _global_cache
    
    if _global_cache is not None:
        _global_cache.shutdown()
        _global_cache = None


# 缓存集成函数
def integrate_with_state_manager(state_manager):
    """将智能缓存集成到状态管理器中"""
    cache = get_global_cache()

    # 为状态管理器添加缓存方法
    def cached_get_state(key: str, default=None, use_cache=True, ttl=None):
        if not use_cache:
            return state_manager._original_get_state(key, default)

        # 尝试从缓存获取
        cached_value = cache.get(f"state:{key}")
        if cached_value is not None:
            return cached_value

        # 从状态管理器获取
        value = state_manager._original_get_state(key, default)

        # 缓存值
        if value != default:
            cache.set(f"state:{key}", value, ttl)

        return value

    def cached_set_state(key: str, value, use_cache=True, ttl=None):
        # 设置到状态管理器
        result = state_manager._original_set_state(key, value)

        # 更新缓存
        if result and use_cache:
            cache.set(f"state:{key}", value, ttl)

        return result

    # 保存原始方法
    if not hasattr(state_manager, '_original_get_state'):
        state_manager._original_get_state = state_manager.get_state
        state_manager._original_set_state = state_manager.set_state

    # 替换为缓存版本
    state_manager.get_state = cached_get_state
    state_manager.set_state = cached_set_state
    state_manager._cache = cache

    logger.info("智能缓存已集成到状态管理器")


# 导出的公共接口
__all__ = [
    'IntelligentLRUCache',
    'CachePolicy',
    'CacheEntry',
    'CacheStats',
    'get_global_cache',
    'shutdown_global_cache',
    'integrate_with_state_manager'
]
