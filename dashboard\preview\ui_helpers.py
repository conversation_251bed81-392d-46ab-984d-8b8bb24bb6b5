import streamlit as st

def display_welcome_placeholder(icon: str, title: str, message: str):
    """
    显示一个带有图标、标题和消息的欢迎/占位符组件。

    Args:
        icon (str): 要显示的表情符号或图标。
        title (str): 组件的主标题。
        message (str): 组件的描述性消息。
    """
    st.markdown(f"""
        <div class="welcome-container">
            <div class="welcome-icon">{icon}</div>
            <h2 class="welcome-title">{title}</h2>
            <p class="welcome-message">{message}</p>
        </div>
    """, unsafe_allow_html=True) 