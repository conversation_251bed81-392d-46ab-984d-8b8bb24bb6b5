# -*- coding: utf-8 -*-
"""
数据预处理组件基类
提供数据预处理组件的基础接口和通用功能
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from abc import abstractmethod
import logging

from ..base import UIComponent
from ...utils.state_helpers import (
    get_tools_refactor_instance,
    get_tools_state,
    set_tools_state
)

logger = logging.getLogger(__name__)


class PreprocessingComponent(UIComponent):
    """数据预处理组件基类"""
    
    def __init__(self, component_name: str, title: str = None):
        self.component_name = component_name
        self.title = title or component_name
        self.logger = logging.getLogger(f"{__name__}.{component_name}")
    
    def get_component_id(self) -> str:
        """获取组件ID"""
        return f"preprocessing_{self.component_name}"
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return [
            f'{self.component_name}_data',
            f'{self.component_name}_processed_data',
            f'{self.component_name}_parameters',
            f'{self.component_name}_last_operation',
            f'{self.component_name}_operation_history'
        ]
    
    def get_state(self, key: str, default=None):
        """获取组件状态"""
        module_key = 'time_series_clean' if 'clean' in self.component_name else 'time_series_compute'
        return get_tools_state(module_key, f'{self.component_name}.{key}', default)
    
    def set_state(self, key: str, value):
        """设置组件状态"""
        module_key = 'time_series_clean' if 'clean' in self.component_name else 'time_series_compute'
        return set_tools_state(module_key, f'{self.component_name}.{key}', value)
    
    def get_staged_data_dict(self) -> Dict[str, pd.DataFrame]:
        """获取暂存数据字典"""
        try:
            tools_refactor = get_tools_refactor_instance()
            if tools_refactor:
                return tools_refactor.get_tools_state('time_series_clean', 'staged_data_dict', {})
            return {}
        except Exception as e:
            self.logger.error(f"获取暂存数据失败: {e}")
            return {}
    
    def set_staged_data_dict(self, staged_data_dict: Dict[str, pd.DataFrame]) -> bool:
        """设置暂存数据字典"""
        try:
            tools_refactor = get_tools_refactor_instance()
            if tools_refactor:
                return tools_refactor.set_tools_state('time_series_clean', 'staged_data_dict', staged_data_dict)
            return False
        except Exception as e:
            self.logger.error(f"设置暂存数据失败: {e}")
            return False
    
    def render_data_selector(self, st_obj, key_suffix: str = "") -> Optional[pd.DataFrame]:
        """
        渲染数据选择器
        
        Args:
            st_obj: Streamlit对象
            key_suffix: 键后缀，用于区分不同的选择器
            
        Returns:
            Optional[pd.DataFrame]: 选择的数据
        """
        # 获取暂存数据
        staged_data_dict = self.get_staged_data_dict()
        
        if not staged_data_dict:
            st_obj.info("暂存区暂无数据，请先在数据清洗模块中处理数据")
            return None
        
        # 数据选择
        data_options = ["请选择..."] + list(staged_data_dict.keys())
        selected_data_name = st_obj.selectbox(
            "选择数据源:",
            options=data_options,
            key=f"{self.component_name}_data_selector{key_suffix}"
        )
        
        if selected_data_name != "请选择...":
            selected_data = staged_data_dict[selected_data_name]
            if isinstance(selected_data, pd.DataFrame):
                st_obj.success(f"已选择数据: {selected_data_name}")
                st_obj.info(f"数据形状: {selected_data.shape[0]} 行 × {selected_data.shape[1]} 列")
                
                # 显示数据预览
                with st_obj.expander("数据预览", expanded=False):
                    st_obj.dataframe(selected_data.head(10), use_container_width=True)
                
                return selected_data
            else:
                st_obj.error(f"数据 '{selected_data_name}' 格式无效")
        
        return None
    
    def render_operation_history(self, st_obj):
        """渲染操作历史"""
        history = self.get_state('operation_history', [])
        
        if history:
            st_obj.markdown("**操作历史:**")
            for i, operation in enumerate(reversed(history[-5:]), 1):  # 显示最近5个操作
                timestamp = operation.get('timestamp', 'Unknown')
                operation_type = operation.get('type', 'Unknown')
                description = operation.get('description', 'No description')
                st_obj.write(f"{i}. [{timestamp}] {operation_type}: {description}")
    
    def add_to_operation_history(self, operation_type: str, description: str, parameters: Dict[str, Any] = None):
        """添加操作到历史记录"""
        import datetime
        
        history = self.get_state('operation_history', [])
        
        operation = {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'type': operation_type,
            'description': description,
            'parameters': parameters or {}
        }
        
        history.append(operation)
        
        # 保留最近50个操作
        if len(history) > 50:
            history = history[-50:]
        
        self.set_state('operation_history', history)
    
    def render_data_export(self, st_obj, data: pd.DataFrame, filename_prefix: str = None):
        """渲染数据导出功能"""
        if data is None or data.empty:
            return
        
        filename_prefix = filename_prefix or self.component_name
        
        st_obj.markdown("**数据导出:**")
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            # CSV导出，使用utf-8-sig编码避免中文乱码
            csv_string = data.to_csv(index=False, encoding='utf-8-sig')
            csv_data = csv_string.encode('utf-8-sig')
            st_obj.download_button(
                label="导出为CSV",
                data=csv_data,
                file_name=f"{filename_prefix}_processed.csv",
                mime="text/csv",
                key=f"{self.component_name}_export_csv"
            )
        
        with col2:
            # Excel导出
            try:
                import io
                excel_buffer = io.BytesIO()
                with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                    data.to_excel(writer, sheet_name='ProcessedData', index=False)
                excel_data = excel_buffer.getvalue()
                
                st_obj.download_button(
                    label="导出为Excel",
                    data=excel_data,
                    file_name=f"{filename_prefix}_processed.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    key=f"{self.component_name}_export_excel"
                )
            except ImportError:
                st_obj.info("Excel导出需要安装openpyxl库")
    
    @abstractmethod
    def render_processing_interface(self, st_obj, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        渲染处理界面
        
        Args:
            st_obj: Streamlit对象
            data: 输入数据
            
        Returns:
            Optional[pd.DataFrame]: 处理后的数据
        """
        pass
    
    def render(self, st_obj, **kwargs) -> Optional[pd.DataFrame]:
        """
        渲染完整的预处理组件
        
        Args:
            st_obj: Streamlit对象
            **kwargs: 其他参数
            
        Returns:
            Optional[pd.DataFrame]: 处理后的数据
        """
        try:
            # 渲染标题
            st_obj.markdown(f"### {self.title}")
            
            # 渲染数据选择器
            input_data = self.render_data_selector(st_obj)
            
            if input_data is None:
                return None
            
            # 渲染处理界面
            processed_data = self.render_processing_interface(st_obj, input_data)
            
            if processed_data is not None:
                # 渲染导出功能
                self.render_data_export(st_obj, processed_data)
                
                # 渲染操作历史
                self.render_operation_history(st_obj)
            
            return processed_data
            
        except Exception as e:
            self.handle_error(st_obj, e, f"渲染{self.title}组件")
            return None


__all__ = ['PreprocessingComponent']
