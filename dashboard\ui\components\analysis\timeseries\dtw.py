# -*- coding: utf-8 -*-
"""
DTW分析组件
迁移自 dashboard/tools/time_series_property/dtw_frontend.py
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import logging
from typing import List, Dict, Any, Optional, Tuple

# 配置matplotlib中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

from .base import TimeSeriesAnalysisComponent

logger = logging.getLogger(__name__)


class DTWAnalysisComponent(TimeSeriesAnalysisComponent):
    """DTW分析组件"""
    
    def __init__(self):
        super().__init__("dtw", "DTW分析")
    
    def render_analysis_parameters(self, st_obj, data: pd.DataFrame, data_name: str) -> <PERSON><PERSON>[str, str, str, int]:
        """
        渲染分析参数设置界面
        
        Args:
            st_obj: Streamlit对象
            data: 分析数据
            data_name: 数据名称
            
        Returns:
            Tuple[str, str, str, int]: (目标序列, 比较序列, 窗口类型, 窗口大小)
        """
        numeric_cols = [col for col in data.columns if pd.api.types.is_numeric_dtype(data[col])]
        
        if len(numeric_cols) < 2:
            st_obj.warning("DTW分析需要至少两个数值列")
            return None, None, None, 0
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            # 目标序列选择
            target_series = st_obj.selectbox(
                "选择目标序列:",
                options=numeric_cols,
                key=f"dtw_{data_name}_target_series"
            )
            
            # 窗口类型
            window_type = st_obj.selectbox(
                "窗口类型:",
                options=["无约束", "Sakoe-Chiba", "Itakura"],
                key=f"dtw_{data_name}_window_type"
            )
        
        with col2:
            # 比较序列选择
            comparison_options = [col for col in numeric_cols if col != target_series]
            comparison_series = st_obj.selectbox(
                "选择比较序列:",
                options=comparison_options,
                key=f"dtw_{data_name}_comparison_series"
            )
            
            # 窗口大小
            window_size = st_obj.number_input(
                "窗口大小:",
                min_value=1,
                max_value=min(50, len(data) // 4),
                value=10,
                disabled=(window_type == "无约束"),
                key=f"dtw_{data_name}_window_size"
            )
        
        return target_series, comparison_series, window_type, window_size
    
    def plot_dtw_path(self, st_obj, s1_np, s2_np, path, s1_name, s2_name):
        """绘制DTW路径图"""
        try:
            fig, ax = plt.subplots(figsize=(10, 5))
            
            ax.plot(np.arange(len(s1_np)), s1_np, "o-", label=s1_name, markersize=4, linewidth=1.5)
            ax.plot(np.arange(len(s2_np)), s2_np, "s-", label=s2_name, markersize=4, linewidth=1.5)
            
            for idx1, idx2 in path:
                if idx1 < len(s1_np) and idx2 < len(s2_np):
                    ax.plot([idx1, idx2], [s1_np[idx1], s2_np[idx2]], 
                           color='grey', linestyle='--', linewidth=0.8, alpha=0.7)
            
            ax.set_xlabel("时间索引")
            ax.set_ylabel("数值")
            ax.set_title(f"DTW对齐路径: {s1_name} vs {s2_name}")
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            st_obj.pyplot(fig)
            plt.close(fig)
            
        except Exception as e:
            logger.error(f"绘制DTW路径图失败: {e}")
            st_obj.error(f"绘制图表失败: {str(e)}")
    
    def render_analysis_results(self, st_obj, results: Dict[str, Any]):
        """渲染分析结果"""
        
        if not results:
            st_obj.warning("没有分析结果可显示")
            return
        
        # 显示DTW距离
        dtw_distance = results.get('dtw_distance', 'N/A')
        if dtw_distance != 'N/A':
            st_obj.metric("DTW距离", f"{dtw_distance:.4f}")
        
        # 显示对齐路径信息
        path = results.get('path', [])
        if path:
            st_obj.metric("对齐路径长度", len(path))
            
            # 绘制DTW路径
            s1_data = results.get('s1_data')
            s2_data = results.get('s2_data')
            s1_name = results.get('s1_name', 'Series1')
            s2_name = results.get('s2_name', 'Series2')
            
            if s1_data is not None and s2_data is not None:
                st_obj.markdown("**DTW对齐路径图:**")
                self.plot_dtw_path(st_obj, s1_data, s2_data, path, s1_name, s2_name)
        
        # 显示参数信息
        parameters = results.get('parameters', {})
        if parameters:
            st_obj.markdown("**分析参数:**")
            for key, value in parameters.items():
                st_obj.write(f"- {key}: {value}")
    
    def render_analysis_interface(self, st_obj, data: pd.DataFrame, data_name: str) -> Any:
        """渲染DTW分析界面"""
        
        try:
            # 渲染参数设置
            target_series, comparison_series, window_type, window_size = self.render_analysis_parameters(st_obj, data, data_name)
            
            if target_series is None or comparison_series is None:
                return None
            
            # 分析按钮
            analysis_key = f"dtw_analyze_btn_{data_name}"
            if st_obj.button("开始分析", key=analysis_key, type="primary"):
                
                with st_obj.spinner("正在进行DTW分析..."):
                    try:
                        # 导入后端分析函数
                        from dashboard.tools.time_series_property import dtw_backend
                        
                        # 准备数据
                        s1 = data[target_series].dropna()
                        s2 = data[comparison_series].dropna()
                        
                        # 映射窗口类型
                        window_mapping = {
                            "无约束": None,
                            "Sakoe-Chiba": "sakoe_chiba",
                            "Itakura": "itakura"
                        }
                        window_constraint = window_mapping.get(window_type)
                        
                        # 调用后端函数
                        dtw_distance, path = dtw_backend.compute_dtw(
                            s1.values, s2.values, 
                            window_constraint=window_constraint,
                            window_size=window_size if window_constraint else None
                        )
                        
                        # 构建结果
                        results = {
                            'dtw_distance': dtw_distance,
                            'path': path,
                            's1_data': s1.values,
                            's2_data': s2.values,
                            's1_name': target_series,
                            's2_name': comparison_series,
                            'parameters': {
                                '目标序列': target_series,
                                '比较序列': comparison_series,
                                '窗口类型': window_type,
                                '窗口大小': window_size if window_constraint else 'N/A'
                            }
                        }
                        
                        # 保存结果到状态
                        self.set_state('results', results)
                        
                        st_obj.success("DTW分析完成！")
                        
                        # 显示结果
                        self.render_analysis_results(st_obj, results)
                        
                        return results
                        
                    except Exception as e:
                        error_msg = f"DTW分析失败: {str(e)}"
                        st_obj.error(error_msg)
                        logger.error(error_msg)
                        return None
            
            # 显示之前的分析结果（如果有）
            previous_results = self.get_state('results')
            
            if previous_results:
                st_obj.markdown("---")
                st_obj.markdown("#### 上次分析结果")
                self.render_analysis_results(st_obj, previous_results)
                return previous_results
            
            return None
            
        except Exception as e:
            self.handle_error(st_obj, e, "渲染DTW分析界面")
            return None


__all__ = ['DTWAnalysisComponent']
