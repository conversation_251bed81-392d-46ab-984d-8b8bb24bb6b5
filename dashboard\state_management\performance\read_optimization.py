# -*- coding: utf-8 -*-
"""
状态读取性能优化
提供高性能的状态读取优化策略，包括预取、批量读取、索引优化等
"""

import time
import threading
import logging
import weakref
from typing import Any, Dict, List, Set, Optional, Tuple, Union
from collections import defaultdict, OrderedDict
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class ReadPattern:
    """读取模式分析"""
    key: str
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    access_frequency: float = 0.0  # 每秒访问次数
    sequential_reads: List[str] = field(default_factory=list)  # 顺序读取的键
    
    def update_access(self):
        """更新访问信息"""
        current_time = time.time()
        time_diff = current_time - self.last_accessed
        
        self.access_count += 1
        
        # 计算访问频率（指数移动平均）
        if time_diff > 0:
            current_frequency = 1.0 / time_diff
            self.access_frequency = 0.7 * self.access_frequency + 0.3 * current_frequency
        
        self.last_accessed = current_time


class ReadCache:
    """读取缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 300.0):
        self.max_size = max_size
        self.ttl = ttl
        self._cache: OrderedDict[str, Tuple[Any, float]] = OrderedDict()
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                return None
            
            value, timestamp = self._cache[key]
            
            # 检查TTL
            if time.time() - timestamp > self.ttl:
                del self._cache[key]
                return None
            
            # LRU: 移动到末尾
            self._cache.move_to_end(key)
            return value
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            # 检查容量限制
            if len(self._cache) >= self.max_size:
                # 移除最老的项目
                self._cache.popitem(last=False)
            
            self._cache[key] = (value, time.time())
    
    def invalidate(self, key: str):
        """使缓存失效"""
        with self._lock:
            self._cache.pop(key, None)
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()


class ReadOptimizer:
    """状态读取优化器"""
    
    def __init__(self, state_manager, enable_prefetch: bool = True, 
                 enable_batch_read: bool = True, enable_pattern_analysis: bool = True):
        """
        初始化读取优化器
        
        Args:
            state_manager: 状态管理器实例
            enable_prefetch: 是否启用预取
            enable_batch_read: 是否启用批量读取
            enable_pattern_analysis: 是否启用模式分析
        """
        self.state_manager = state_manager
        self.enable_prefetch = enable_prefetch
        self.enable_batch_read = enable_batch_read
        self.enable_pattern_analysis = enable_pattern_analysis
        
        # 读取缓存
        self.read_cache = ReadCache(max_size=2000, ttl=300.0)
        
        # 模式分析
        self.read_patterns: Dict[str, ReadPattern] = {}
        self.pattern_lock = threading.RLock()
        
        # 预取队列
        self.prefetch_queue: Set[str] = set()
        self.prefetch_lock = threading.Lock()
        
        # 批量读取缓冲区
        self.batch_buffer: Dict[str, List[str]] = defaultdict(list)
        self.batch_lock = threading.Lock()
        
        # 性能统计
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'prefetch_hits': 0,
            'batch_reads': 0,
            'total_reads': 0,
            'avg_read_time': 0.0
        }
        self.stats_lock = threading.Lock()
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="ReadOptimizer")
        
        logger.info(f"状态读取优化器初始化完成 - 预取: {enable_prefetch}, 批量: {enable_batch_read}")
    
    def optimized_get_state(self, key: str, default: Any = None, 
                           use_cache: bool = True, enable_prefetch: bool = None) -> Any:
        """优化的状态获取"""
        start_time = time.time()
        
        try:
            # 更新统计
            with self.stats_lock:
                self.stats['total_reads'] += 1
            
            # 1. 尝试从缓存获取
            if use_cache:
                cached_value = self.read_cache.get(key)
                if cached_value is not None:
                    with self.stats_lock:
                        self.stats['cache_hits'] += 1
                    
                    # 更新模式分析
                    if self.enable_pattern_analysis:
                        self._update_read_pattern(key)
                    
                    logger.debug(f"缓存命中: {key}")
                    return cached_value
                else:
                    with self.stats_lock:
                        self.stats['cache_misses'] += 1
            
            # 2. 从状态管理器获取
            value = self.state_manager._original_get_state(key, default)
            
            # 3. 缓存结果
            if use_cache and value != default:
                self.read_cache.set(key, value)
            
            # 4. 更新模式分析
            if self.enable_pattern_analysis:
                self._update_read_pattern(key)
            
            # 5. 触发预取
            if (enable_prefetch is True or 
                (enable_prefetch is None and self.enable_prefetch)):
                self._trigger_prefetch(key)
            
            return value
            
        finally:
            # 更新性能统计
            read_time = time.time() - start_time
            with self.stats_lock:
                self.stats['avg_read_time'] = (
                    (self.stats['avg_read_time'] * (self.stats['total_reads'] - 1) + read_time) /
                    self.stats['total_reads']
                )
    
    def batch_get_states(self, keys: List[str], default: Any = None) -> Dict[str, Any]:
        """批量获取状态"""
        if not self.enable_batch_read:
            # 如果未启用批量读取，逐个获取
            return {key: self.optimized_get_state(key, default) for key in keys}
        
        start_time = time.time()
        results = {}
        cache_hits = []
        cache_misses = []
        
        # 1. 检查缓存
        for key in keys:
            cached_value = self.read_cache.get(key)
            if cached_value is not None:
                results[key] = cached_value
                cache_hits.append(key)
            else:
                cache_misses.append(key)
        
        # 2. 批量读取未命中的键
        if cache_misses:
            batch_results = self._batch_read_from_state_manager(cache_misses, default)
            results.update(batch_results)
            
            # 缓存批量读取的结果
            for key, value in batch_results.items():
                if value != default:
                    self.read_cache.set(key, value)
        
        # 3. 更新统计
        with self.stats_lock:
            self.stats['cache_hits'] += len(cache_hits)
            self.stats['cache_misses'] += len(cache_misses)
            self.stats['batch_reads'] += 1
            self.stats['total_reads'] += len(keys)
        
        # 4. 更新模式分析
        if self.enable_pattern_analysis:
            for key in keys:
                self._update_read_pattern(key)
        
        logger.debug(f"批量读取完成: {len(keys)}个键, 缓存命中: {len(cache_hits)}")
        return results
    
    def prefetch_keys(self, keys: List[str]):
        """预取指定的键"""
        if not self.enable_prefetch:
            return
        
        with self.prefetch_lock:
            self.prefetch_queue.update(keys)
        
        # 异步执行预取
        self.executor.submit(self._execute_prefetch, keys)
    
    def invalidate_cache(self, key: str):
        """使缓存失效"""
        self.read_cache.invalidate(key)
        logger.debug(f"缓存失效: {key}")
    
    def clear_cache(self):
        """清空所有缓存"""
        self.read_cache.clear()
        with self.pattern_lock:
            self.read_patterns.clear()
        logger.info("读取优化器缓存已清空")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.stats_lock:
            stats = self.stats.copy()
        
        # 计算命中率
        total_cache_requests = stats['cache_hits'] + stats['cache_misses']
        cache_hit_rate = stats['cache_hits'] / total_cache_requests if total_cache_requests > 0 else 0.0
        
        return {
            **stats,
            'cache_hit_rate': cache_hit_rate,
            'cache_miss_rate': 1.0 - cache_hit_rate,
            'cache_size': len(self.read_cache._cache),
            'pattern_count': len(self.read_patterns),
            'prefetch_queue_size': len(self.prefetch_queue)
        }
    
    def get_read_patterns(self) -> Dict[str, Dict[str, Any]]:
        """获取读取模式分析"""
        with self.pattern_lock:
            return {
                key: {
                    'access_count': pattern.access_count,
                    'last_accessed': pattern.last_accessed,
                    'access_frequency': pattern.access_frequency,
                    'sequential_reads': pattern.sequential_reads[-10:]  # 最近10个顺序读取
                }
                for key, pattern in self.read_patterns.items()
            }
    
    def _update_read_pattern(self, key: str):
        """更新读取模式"""
        with self.pattern_lock:
            if key not in self.read_patterns:
                self.read_patterns[key] = ReadPattern(key)
            
            pattern = self.read_patterns[key]
            pattern.update_access()
            
            # 记录顺序读取模式
            if len(pattern.sequential_reads) > 0:
                last_key = pattern.sequential_reads[-1]
                if last_key != key:
                    pattern.sequential_reads.append(key)
                    if len(pattern.sequential_reads) > 20:  # 保持最近20个
                        pattern.sequential_reads = pattern.sequential_reads[-20:]
            else:
                pattern.sequential_reads.append(key)
    
    def _trigger_prefetch(self, key: str):
        """触发预取"""
        if not self.enable_prefetch:
            return
        
        # 基于模式分析预测下一个可能访问的键
        predicted_keys = self._predict_next_keys(key)
        
        if predicted_keys:
            self.executor.submit(self._execute_prefetch, predicted_keys)
    
    def _predict_next_keys(self, current_key: str) -> List[str]:
        """预测下一个可能访问的键"""
        with self.pattern_lock:
            if current_key not in self.read_patterns:
                return []
            
            pattern = self.read_patterns[current_key]
            
            # 基于顺序读取模式预测
            if len(pattern.sequential_reads) >= 2:
                # 查找当前键在序列中的位置
                try:
                    current_index = pattern.sequential_reads.index(current_key)
                    if current_index < len(pattern.sequential_reads) - 1:
                        next_key = pattern.sequential_reads[current_index + 1]
                        return [next_key]
                except ValueError:
                    pass
            
            # 基于访问频率预测相关键
            related_keys = []
            for other_key, other_pattern in self.read_patterns.items():
                if (other_key != current_key and 
                    other_pattern.access_frequency > 0.1 and  # 频繁访问
                    current_key in other_pattern.sequential_reads):
                    related_keys.append(other_key)
            
            return related_keys[:3]  # 最多预取3个键
    
    def _execute_prefetch(self, keys: List[str]):
        """执行预取"""
        prefetched = 0
        
        for key in keys:
            # 检查是否已在缓存中
            if self.read_cache.get(key) is not None:
                continue
            
            try:
                # 从状态管理器获取
                value = self.state_manager._original_get_state(key, None)
                if value is not None:
                    self.read_cache.set(key, value)
                    prefetched += 1
                    
                    with self.stats_lock:
                        self.stats['prefetch_hits'] += 1
                        
            except Exception as e:
                logger.debug(f"预取失败: {key} - {e}")
        
        if prefetched > 0:
            logger.debug(f"预取完成: {prefetched}个键")
    
    def _batch_read_from_state_manager(self, keys: List[str], default: Any) -> Dict[str, Any]:
        """从状态管理器批量读取"""
        results = {}
        
        # 如果状态管理器支持批量读取，使用批量接口
        if hasattr(self.state_manager, 'batch_get_state'):
            try:
                results = self.state_manager.batch_get_state(keys, default)
            except Exception as e:
                logger.warning(f"批量读取失败，回退到逐个读取: {e}")
                # 回退到逐个读取
                for key in keys:
                    results[key] = self.state_manager._original_get_state(key, default)
        else:
            # 并行读取
            with ThreadPoolExecutor(max_workers=min(len(keys), 8)) as executor:
                future_to_key = {
                    executor.submit(self.state_manager._original_get_state, key, default): key
                    for key in keys
                }
                
                for future in as_completed(future_to_key):
                    key = future_to_key[future]
                    try:
                        results[key] = future.result()
                    except Exception as e:
                        logger.error(f"读取状态失败: {key} - {e}")
                        results[key] = default
        
        return results
    
    def shutdown(self):
        """关闭优化器"""
        self.executor.shutdown(wait=True)
        self.clear_cache()
        logger.info("状态读取优化器已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except Exception:
            pass


def integrate_read_optimization(state_manager) -> ReadOptimizer:
    """将读取优化集成到状态管理器中"""
    optimizer = ReadOptimizer(state_manager)
    
    # 保存原始方法
    if not hasattr(state_manager, '_original_get_state'):
        state_manager._original_get_state = state_manager.get_state
    
    # 替换为优化版本
    state_manager.get_state = optimizer.optimized_get_state
    state_manager.batch_get_states = optimizer.batch_get_states
    state_manager._read_optimizer = optimizer
    
    logger.info("状态读取优化已集成到状态管理器")
    return optimizer


# 导出的公共接口
__all__ = [
    'ReadOptimizer',
    'ReadCache',
    'ReadPattern',
    'integrate_read_optimization'
]
