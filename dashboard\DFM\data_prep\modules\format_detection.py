"""
格式检测模块

负责检测和解析不同来源的Excel表格格式
支持同花顺、Wind、Mysteel等不同数据源的格式
"""

import pandas as pd
from typing import Dict, Any, Optional
from .config_constants import debug_print

def detect_sheet_format(excel_file, sheet_name: str) -> Dict[str, Any]:
    """
    检测任意表格的格式以处理不同的数据库版本和数据源。
    返回包含格式信息和读取参数的字典。

    支持格式:
    - 同花顺格式: Row0(废弃), Row1(指标名称), Row2(频率), Row3(单位), Row4(指标ID), Row5+(数据)
    - Wind格式: Row0(废弃), Row1(指标名称), Row2+(数据)
    - Mysteel格式: Row0(废弃), Row1(指标名称), Row2(频度), Row3(指标描述), Row4+(数据)
    - 旧格式: Row0(指标名称), Row1+(数据或元数据)
    
    Args:
        excel_file: Excel文件对象
        sheet_name: 表格名称
        
    Returns:
        Dict[str, Any]: 包含格式信息的字典
            - format: 格式类型
            - skiprows: 需要跳过的行
            - header: 标题行位置
            - data_start_row: 数据开始行
            - source: 数据源
    """
    try:
        # 读取前几行来分析格式
        df_sample = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=8, header=None)

        if df_sample.shape[0] < 2:
            debug_print(f"      [格式检测] Sheet '{sheet_name}' 行数不足，使用默认格式")
            return {
                'format': 'default',
                'skiprows': None,
                'header': 0,
                'data_start_row': 1,
                'source': 'unknown'
            }

        # 检查格式模式
        row_0 = df_sample.iloc[0, 0] if df_sample.shape[0] > 0 else None
        row_1 = df_sample.iloc[1, 0] if df_sample.shape[1] > 0 else None
        row_2 = df_sample.iloc[2, 0] if df_sample.shape[0] > 2 else None
        row_3 = df_sample.iloc[3, 0] if df_sample.shape[0] > 3 else None
        row_4 = df_sample.iloc[4, 0] if df_sample.shape[0] > 4 else None
        row_4_col1 = df_sample.iloc[4, 1] if df_sample.shape[0] > 4 and df_sample.shape[1] > 1 else None

        # 同花顺新格式检测 (更精确的检测条件)
        # 检查完整的5行格式：指标名称+频率+单位+指标ID+数据
        if (row_1 == '指标名称' and row_2 == '频率' and row_3 == '单位' and row_4 == '指标ID' and
            str(row_4_col1) not in [None, '', 'nan'] and (str(row_4_col1).startswith('M') or str(row_4_col1).startswith('S'))):  # 支持M和S开头的指标ID
            print(f"      [格式检测] 检测到同花顺完整新格式")
            return {
                'format': 'tonghuashun_new',
                'skiprows': [0, 2, 3, 4],  # Skip: 废弃行, 频率, 单位, 指标ID
                'header': 0,               # Row 1 becomes header
                'data_start_row': 5,
                'source': 'tonghuashun'
            }

        # Wind新格式检测 (检查row_0是否为数字0或NaN)
        elif (row_1 == '指标名称' and (row_0 == 0 or pd.isna(row_0)) and
              row_2 != '频率' and row_2 != '频度' and
              not pd.isna(row_2)):  # row_2是实际数据，不是元数据
            print(f"      [格式检测] 检测到Wind新格式")
            return {
                'format': 'wind_new',
                'skiprows': [0],           # Skip: 废弃行
                'header': 0,               # Row 1 becomes header
                'data_start_row': 2,
                'source': 'wind'
            }

        # Mysteel新格式检测 (检查是否以"钢联数据"开头)
        elif (row_0 == '钢联数据' and row_1 == '指标名称' and row_2 == '频度'):
            print(f"      [格式检测] 检测到Mysteel新格式")
            return {
                'format': 'mysteel_new',
                'skiprows': [0, 2, 3],     # Skip: 废弃行, 频度, 指标描述
                'header': 0,               # Row 1 becomes header
                'data_start_row': 4,
                'source': 'mysteel'
            }

        # 旧格式检测
        elif row_0 == '指标名称':
            print(f"      [格式检测] 检测到旧格式")
            # 检测是否有指标ID行需要跳过
            if (df_sample.shape[0] > 1 and
                str(df_sample.iloc[1, 0]).startswith('S') and len(str(df_sample.iloc[1, 0])) > 5):
                return {
                    'format': 'old_with_id',
                    'skiprows': [1],       # Skip: 指标ID行
                    'header': 0,
                    'data_start_row': 2,
                    'source': 'old'
                }
            else:
                return {
                    'format': 'old_direct',
                    'skiprows': None,
                    'header': 0,
                    'data_start_row': 1,
                    'source': 'old'
                }

        # 如果都不匹配，使用默认策略
        else:
            print(f"      [格式检测] 未识别的格式，使用默认策略")
            return {
                'format': 'default',
                'skiprows': None,
                'header': 0,
                'data_start_row': 1,
                'source': 'unknown'
            }

    except Exception as e:
        print(f"      [格式检测] 检测格式时出错: {e}，使用默认参数")
        return {
            'format': 'error',
            'skiprows': None,
            'header': 0,
            'data_start_row': 1,
            'source': 'error'
        }

def detect_target_sheet_format(excel_file, sheet_name: str) -> Dict[str, Any]:
    """
    向后兼容的包装函数，用于检测目标表格格式

    Args:
        excel_file: Excel文件对象
        sheet_name: 表格名称

    Returns:
        Dict[str, Any]: 格式信息字典
    """
    return detect_sheet_format(excel_file, sheet_name)

def parse_sheet_info(sheet_name: str, target_sheet_name: str) -> Dict[str, Optional[str]]:
    """
    解析表格名称，提取行业、频率和数据源信息

    处理格式如 '行业_频率_数据来源' 或目标表格

    Args:
        sheet_name: 表格名称
        target_sheet_name: 目标表格名称

    Returns:
        Dict[str, Optional[str]]: 包含以下键的字典
            - industry: 行业信息
            - freq_type: 频率类型 ('daily', 'weekly', 'monthly', 'monthly_target', 'monthly_predictor')
            - source: 数据源
    """
    info: Dict[str, Optional[str]] = {'industry': None, 'freq_type': None, 'source': None}

    if not isinstance(sheet_name, str):
        return info

    # 进行大小写不敏感的比较，检查是否为目标表格
    is_target_sheet = False
    if isinstance(target_sheet_name, str):
        if sheet_name.lower() == target_sheet_name.lower():
            is_target_sheet = True

    if is_target_sheet:
        # 尝试从目标名称提取行业 (可选)
        parts_target = sheet_name.split('_')
        if len(parts_target) > 0:
            # 假设第一个部分是行业相关
            industry_part = parts_target[0].replace('-月度', '').replace('_月度','').strip() # 同时替换 - 和 _
            info['industry'] = industry_part if industry_part else 'Macro' # 默认为 Macro
        else:
            info['industry'] = 'Macro' # 默认
        info['freq_type'] = 'monthly_target'  # 目标表格标记为月度目标
        return info

    # 通用格式解析
    parts = sheet_name.split('_')
    if len(parts) >= 2: # 至少需要 行业_频率
        info['industry'] = parts[0].strip()
        freq_part = parts[1].strip()

        if freq_part == '日度':
            info['freq_type'] = 'daily'
        elif freq_part == '周度':
            info['freq_type'] = 'weekly'
        elif freq_part == '月度':
            # 其他月度预测变量表格
            info['freq_type'] = 'monthly_predictor'
        # 可以根据需要添加其他频率

        if len(parts) >= 3:
            info['source'] = '_'.join(parts[2:]).strip() # 允许来源包含下划线

    # 如果未能解析出行业，给个默认值
    if info['industry'] is None and info['freq_type'] is not None:
         info['industry'] = "Uncategorized"

    return info
