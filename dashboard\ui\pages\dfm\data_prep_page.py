# -*- coding: utf-8 -*-
"""
DFM数据预处理页面组件

完全重构版本，与dfm_old_ui/data_prep_ui.py保持完全一致
"""

import streamlit as st
import pandas as pd
import numpy as np
import os
import sys
import io
import json
import time
from datetime import datetime, date
from typing import Optional, Dict, Any

# 添加路径以导入统一状态管理
current_dir = os.path.dirname(os.path.abspath(__file__))
dashboard_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..', '..'))
if dashboard_root not in sys.path:
    sys.path.insert(0, dashboard_root)

# 导入统一状态管理
from dashboard.state_management.refactor import get_global_dfm_refactor
import logging

# 配置日志记录器
logger = logging.getLogger(__name__)


def get_dfm_refactor():
    """获取DFM重构模块实例（使用全局单例）"""
    try:
        dfm_refactor = get_global_dfm_refactor()
        if dfm_refactor is None:
            raise RuntimeError("全局DFM重构器不可用")
        return dfm_refactor
    except Exception as e:
        print(f"[DFM Data Prep] Error getting DFM refactor: {e}")
        raise RuntimeError(f"DFM重构器获取失败: {e}")


def get_dfm_state(key, default=None):
    """获取DFM状态值（使用统一状态管理）"""
    try:
        dfm_refactor = get_dfm_refactor()
        if dfm_refactor:
            # 只从data_prep命名空间获取，使用标准键名
            value = dfm_refactor.get_dfm_state('data_prep', key, default)
            # 🔥 添加调试信息，特别是文件相关的键（仅在调试模式下输出）
            from dashboard.ui.utils.debug_helpers import debug_log
            if key in ['dfm_training_data_file', 'dfm_uploaded_excel_file_path']:
                if value is not None:
                    if key == 'dfm_training_data_file' and hasattr(value, 'name'):
                        debug_log(f"状态读取 - 键: {key}, 文件名: {value.name}", "DEBUG")
                    else:
                        debug_log(f"状态读取 - 键: {key}, 值: {value}", "DEBUG")
                else:
                    debug_log(f"状态读取 - 键: {key}, 值: None (使用默认值: {default})", "DEBUG")
            return value
        else:
            debug_log(f"警告 - DFM统一状态管理器不可用，键: {key}", "WARNING")
            return default

    except Exception as e:
        debug_log(f"错误 - 获取DFM状态失败，键: {key}, 错误: {e}", "ERROR")
        return default


def set_dfm_state(key, value):
    """设置DFM状态值（使用统一状态管理）"""
    try:
        dfm_refactor = get_dfm_refactor()
        if dfm_refactor:
            success = dfm_refactor.set_dfm_state('data_prep', key, value)
            # 🔥 添加调试信息，特别是文件相关的键（仅在调试模式下输出）
            from dashboard.ui.utils.debug_helpers import debug_log
            if key in ['dfm_training_data_file', 'dfm_uploaded_excel_file_path']:
                if key == 'dfm_training_data_file' and hasattr(value, 'name'):
                    debug_log(f"状态设置 - 键: {key}, 文件名: {value.name}, 成功: {success}", "DEBUG")
                else:
                    debug_log(f"状态设置 - 键: {key}, 值: {value}, 成功: {success}", "DEBUG")
            return success
        else:
            debug_log(f"警告 - DFM统一状态管理器不可用，无法设置键: {key}", "WARNING")
            return False
    except Exception as e:
        debug_log(f"错误 - 设置DFM状态失败，键: {key}, 错误: {e}", "ERROR")
        return False


def sync_dates_to_train_model():
    """将数据准备tab的日期设置同步到模型训练tab"""
    try:
        dfm_refactor = get_dfm_refactor()

        # 获取数据准备tab的日期设置
        data_start = dfm_refactor.get_dfm_state('data_prep', 'dfm_param_data_start_date', None)
        data_end = dfm_refactor.get_dfm_state('data_prep', 'dfm_param_data_end_date', None)

        # 同步到模型训练tab
        if data_start:
            dfm_refactor.set_dfm_state('train_model', 'dfm_training_start_date', data_start)

        if data_end:
            dfm_refactor.set_dfm_state('train_model', 'dfm_validation_end_date', data_end)

        return True
    except Exception as e:
        return False

def get_dfm_param(key, default=None):
    """获取DFM参数的便捷函数"""
    dfm_refactor = get_dfm_refactor()
    if dfm_refactor:
        # 尝试从多个可能的位置获取
        value = dfm_refactor.get_dfm_state('data_prep', key, None)
        if value is None:
            value = dfm_refactor.get_state(key, None)
        if value is None:
            value = dfm_refactor.get_state(f'dfm_{key}', default)
        return value
    else:
        return default


def set_dfm_param(key, value):
    """设置DFM参数的便捷函数"""
    dfm_refactor = get_dfm_refactor()
    if dfm_refactor:
        success = dfm_refactor.set_dfm_state('data_prep', key, value)
        return success
    else:
        return False


def render_dfm_data_prep_tab(st):
    """Renders the DFM Model Data Preparation tab."""

    # 初始化文件存储
    if get_dfm_state('dfm_training_data_file') is None:
        set_dfm_state("dfm_training_data_file", None)

    # --- NEW: Initialize data objects ---
    if get_dfm_state('dfm_prepared_data_df') is None:
        set_dfm_state("dfm_prepared_data_df", None)
    if get_dfm_state('dfm_transform_log_obj') is None:
        set_dfm_state("dfm_transform_log_obj", None)
    if get_dfm_state('dfm_industry_map_obj') is None:
        set_dfm_state("dfm_industry_map_obj", None)
    if get_dfm_state('dfm_removed_vars_log_obj') is None:
        set_dfm_state("dfm_removed_vars_log_obj", None)
    if get_dfm_state('dfm_var_type_map_obj') is None:
        set_dfm_state("dfm_var_type_map_obj", None)

    # 初始化导出相关的状态 - 使用统一状态管理器
    if get_dfm_state('dfm_export_base_name') is None:
        set_dfm_state("dfm_export_base_name", "dfm_prepared_output")
    if get_dfm_state('dfm_processed_outputs') is None:
        set_dfm_state("dfm_processed_outputs", None)
    # --- END NEW ---

    # 检查侧边栏是否已经上传了文件
    print("🔥 [文件检查] 开始检查侧边栏上传的文件状态...")
    existing_file = get_dfm_state('dfm_training_data_file')
    existing_file_path = get_dfm_state('dfm_uploaded_excel_file_path')

    print(f"🔥 [文件检查] existing_file: {existing_file is not None}")
    print(f"🔥 [文件检查] existing_file_path: {existing_file_path}")

    if existing_file and existing_file_path:
        # 使用已存在的文件，不显示额外信息
        print(f"🔥 [文件检查] 找到已上传文件: {existing_file_path}")
        uploaded_file = existing_file
    else:
        # 如果侧边栏没有上传文件，显示简洁提示
        print("🔥 [文件检查] 未找到已上传文件，显示警告")
        st.warning("⚠️ 请先在侧边栏上传数据文件")
        uploaded_file = None

    if uploaded_file is not None:
        # 检查是否是新文件上传（避免重复处理）
        current_file = get_dfm_state('dfm_training_data_file')
        file_changed = (
            current_file is None or
            current_file.name != uploaded_file.name or
            get_dfm_state('dfm_file_processed', False) == False
        )

        # 如果是从备用上传器上传的新文件，需要保存到状态管理
        if file_changed and uploaded_file != existing_file:
            set_dfm_state("dfm_training_data_file", uploaded_file)
            # 保存Excel文件路径用于训练模块
            set_dfm_state("dfm_uploaded_excel_file_path", uploaded_file.name)
            set_dfm_state("dfm_use_full_data_preparation", True)

            print(f"[UI] 检测到新文件上传: {uploaded_file.name}，标记需要重新检测...")
            set_dfm_state("dfm_file_processed", False)  # 重置处理标记
            set_dfm_state("dfm_date_detection_needed", True)  # 标记需要日期检测

            # 标记文件已处理
            set_dfm_state("dfm_file_processed", True)


    else:
        st.info("请上传训练数据集。")

    # 根据数据文件的实际日期范围进行检测
    def detect_data_date_range(uploaded_file):
        """从上传的文件中检测数据的真实日期范围"""
        try:
            if uploaded_file is None:
                return None, None

            # 读取文件
            file_bytes = uploaded_file.getvalue()
            excel_file = io.BytesIO(file_bytes)

            all_dates_found = []

            # 获取所有工作表名称
            try:
                xl_file = pd.ExcelFile(excel_file)
                sheet_names = xl_file.sheet_names
                print(f"检测到工作表: {sheet_names}")
            except:
                sheet_names = [0]  # 回退到第一个工作表

            # 检查每个工作表寻找真实的日期数据
            for sheet_name in sheet_names:
                try:
                    excel_file.seek(0)  # 重置文件指针

                    # 跳过明显的元数据工作表
                    if any(keyword in str(sheet_name).lower() for keyword in ['指标体系', 'mapping', 'meta', 'info']):
                        print(f"跳过元数据工作表: {sheet_name}")
                        continue

                    # 读取工作表
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, index_col=0)

                    if len(df) < 5:  # 跳过数据太少的工作表
                        continue

                    # 检查索引中的日期
                    datetime_indices = []
                    for idx in df.index:
                        # 检查是否是日期时间类型
                        if pd.api.types.is_datetime64_any_dtype(pd.Series([idx])):
                            datetime_indices.append(idx)
                        elif isinstance(idx, pd.Timestamp):
                            datetime_indices.append(idx)
                        elif hasattr(idx, 'year'):  # datetime对象
                            datetime_indices.append(idx)

                    if len(datetime_indices) > 10:  # 至少要有10个日期才认为是时间序列
                        dates_series = pd.to_datetime(datetime_indices)
                        all_dates_found.extend(dates_series.tolist())

                except Exception as e:
                    continue

            # 汇总所有真实日期，返回实际的数据范围
            if all_dates_found:
                all_dates = pd.to_datetime(all_dates_found)
                actual_start = all_dates.min().date()
                actual_end = all_dates.max().date()

                return actual_start, actual_end
            else:
                return None, None

        except Exception as e:
            return None, None

    # 增强缓存机制，避免重复检测
    # 检测上传文件的日期范围（只在文件变化时执行）
    current_file = get_dfm_state('dfm_training_data_file')
    if current_file:
        file_hash = hash(current_file.getvalue())
        cache_key = f"date_range_{current_file.name}_{file_hash}"
    else:
        cache_key = "date_range_none"

    # 优化：使用dfm_refactor实例进行缓存操作
    dfm_refactor = get_dfm_refactor()

    # 检查缓存是否存在且有效
    cached_result = dfm_refactor.get_dfm_state('data_prep', cache_key, None)
    cache_valid = (
        cached_result is not None and
        not get_dfm_state('dfm_date_detection_needed')
    )

    if not cache_valid:
        # 需要重新检测
        if current_file:
            print(f"[UI] 执行日期检测: {current_file.name}")
            with st.spinner("🔍 正在检测数据日期范围..."):
                detected_start, detected_end = detect_data_date_range(current_file)
            # 缓存结果
            dfm_refactor.set_dfm_state('data_prep', cache_key, (detected_start, detected_end))
            set_dfm_state("dfm_date_detection_needed", False)

            # 清理旧的缓存
            try:
                # 获取所有状态键，查找以date_range_开头的旧缓存
                all_keys = dfm_refactor.get_all_keys() if hasattr(dfm_refactor, 'get_all_keys') else []
                old_keys = [k for k in all_keys if k.startswith("date_range_") and k != cache_key]
                for old_key in old_keys:
                    dfm_refactor.delete_state(old_key)
            except Exception as e:
                print(f"[缓存清理] 清理旧缓存时出错: {e}")
        else:
            detected_start, detected_end = None, None
            dfm_refactor.set_dfm_state('data_prep', cache_key, (None, None))
    else:
        # 使用缓存的结果
        detected_start, detected_end = cached_result if cached_result else (None, None)
        if detected_start and detected_end:
            # 静默处理缓存的日期范围，避免重复日志
            pass

    # 设置默认值：优先使用检测到的日期，否则使用硬编码默认值
    default_start_date = detected_start if detected_start else datetime(2020, 1, 1).date()
    default_end_date = detected_end if detected_end else datetime(2025, 4, 30).date()

    param_defaults = {
        'dfm_param_target_variable': '规模以上工业增加值:当月同比',
        'dfm_param_target_sheet_name': '工业增加值同比增速_月度_同花顺',
        'dfm_param_target_freq': 'W-FRI',
        'dfm_param_remove_consecutive_nans': True,
        'dfm_param_consecutive_nan_threshold': 10,
        'dfm_param_type_mapping_sheet': '指标体系',
        'dfm_param_data_start_date': default_start_date,
        'dfm_param_data_end_date': default_end_date
    }

    # 只在首次初始化或文件更新时设置默认值
    # 优化：批量获取参数以减少重复调用
    dfm_refactor = get_dfm_refactor()
    for key, default_value in param_defaults.items():
        current_value = dfm_refactor.get_dfm_state('data_prep', key, None)
        if current_value is None:
            dfm_refactor.set_dfm_state('data_prep', key, default_value)
        elif key in ['dfm_param_data_start_date', 'dfm_param_data_end_date'] and detected_start and detected_end:
            # 如果检测到新的日期范围，更新日期设置
            if key == 'dfm_param_data_start_date':
                dfm_refactor.set_dfm_state('data_prep', key, default_start_date)
            elif key == 'dfm_param_data_end_date':
                dfm_refactor.set_dfm_state('data_prep', key, default_end_date)

    # 显示检测结果
    if detected_start and detected_end:
        st.success(f"✅ 已自动检测文件日期范围: {detected_start} 到 {detected_end}")
    elif current_file:
        st.warning("⚠️ 无法自动检测文件日期范围，使用默认值。请手动调整日期设置。")

    # 🔥 优化：只调用一次映射数据加载（避免重复调用）
    if current_file:
        _auto_load_mapping_data_if_needed(current_file)

    # 优化：批量获取参数值以减少重复调用
    dfm_refactor = get_dfm_refactor()
    param_values = {
        'data_start_date': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_data_start_date', None),
        'data_end_date': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_data_end_date', None),
        'target_sheet_name': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_target_sheet_name', None),
        'target_variable': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_target_variable', None)
    }

    row1_col1, row1_col2 = st.columns(2)
    with row1_col1:
        start_date_value = st.date_input(
            "数据开始日期 (系统边界)",
            value=param_values['data_start_date'],
            key="ss_dfm_data_start",
            help="设置系统处理数据的最早日期边界。训练期、验证期必须在此日期之后。"
        )
        set_dfm_state("dfm_param_data_start_date", start_date_value)

        # 自动同步到模型训练tab
        if start_date_value != param_values['data_start_date']:
            sync_dates_to_train_model()
    with row1_col2:
        set_dfm_state("dfm_param_data_end_date", st.date_input(
            "数据结束日期 (系统边界)",
            value=param_values['data_end_date'],
            key="ss_dfm_data_end",
            help="设置系统处理数据的最晚日期边界。训练期、验证期必须在此日期之前。"
        ))

    row2_col1, row2_col2 = st.columns(2)
    with row2_col1:
        set_dfm_state("dfm_param_target_sheet_name", st.text_input(
            "目标工作表名称 (Target Sheet Name)",
            value=param_values['target_sheet_name'],
            key="ss_dfm_target_sheet"
        ))
    with row2_col2:
        set_dfm_state("dfm_param_target_variable", st.text_input(
            "目标变量 (Target Variable)",
            value=param_values['target_variable'],
            key="ss_dfm_target_var"
        ))

    # 继续批量获取剩余参数值
    param_values.update({
        'consecutive_nan_threshold': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_consecutive_nan_threshold', None),
        'remove_consecutive_nans': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_remove_consecutive_nans', None),
        'target_freq': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_target_freq', None),
        'type_mapping_sheet': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_type_mapping_sheet', None)
    })

    row3_col1, row3_col2 = st.columns(2)
    with row3_col1:
        set_dfm_state("dfm_param_consecutive_nan_threshold", st.number_input(
            "连续 NaN 阈值 (Consecutive NaN Threshold)",
            min_value=0,
            value=param_values['consecutive_nan_threshold'],
            step=1,
            key="ss_dfm_nan_thresh"
        ))
    with row3_col2:
        set_dfm_state("dfm_param_remove_consecutive_nans", st.checkbox(
            "移除过多连续 NaN 的变量",
            value=param_values['remove_consecutive_nans'],
            key="ss_dfm_remove_nans",
            help="移除列中连续缺失值数量超过阈值的变量"
        ))

    row4_col1, row4_col2 = st.columns(2)
    with row4_col1:
        set_dfm_state("dfm_param_target_freq", st.text_input(
            "目标频率 (Target Frequency)",
            value=param_values['target_freq'],
            help="例如: W-FRI, D, M, Q",
            key="ss_dfm_target_freq"
        ))
    with row4_col2:
        set_dfm_state("dfm_param_type_mapping_sheet", st.text_input(
            "指标映射表名称 (Type Mapping Sheet)",
            value=param_values['type_mapping_sheet'],
            key="ss_dfm_type_map_sheet"
        ))

    st.markdown("--- ") # Separator before the new section
    st.markdown("#### 数据预处理与导出")

    left_col, right_col = st.columns([1, 2]) # Left col for inputs, Right col for outputs/messages

    with left_col:
        # 获取导出基础名称参数
        export_base_name = dfm_refactor.get_dfm_state('data_prep', 'dfm_export_base_name', None)
        set_dfm_state("dfm_export_base_name", st.text_input(
            "导出文件基础名称 (Export Base Filename)",
            value=export_base_name,
            key="ss_dfm_export_basename"
        ))

        run_button_clicked = st.button("运行数据预处理并导出", key="ss_dfm_run_preprocessing")

    with right_col:
        if run_button_clicked:
            set_dfm_state("dfm_processed_outputs", None)  # Clear previous downloadable results
            # --- NEW: Clear previous direct data objects ---
            set_dfm_state("dfm_prepared_data_df", None)
            set_dfm_state("dfm_transform_log_obj", None)
            set_dfm_state("dfm_industry_map_obj", None)
            set_dfm_state("dfm_removed_vars_log_obj", None)
            set_dfm_state("dfm_var_type_map_obj", None)
            # --- END NEW ---

            current_file = get_dfm_state('dfm_training_data_file')
            if current_file is None:
                st.error("错误：请先上传训练数据集！")
            # 优化：批量获取所有需要的参数
            processing_params = {
                'export_base_name': dfm_refactor.get_dfm_state('data_prep', 'dfm_export_base_name', None),
                'data_start_date': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_data_start_date', None),
                'data_end_date': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_data_end_date', None),
                'target_freq': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_target_freq', None),
                'target_sheet_name': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_target_sheet_name', None),
                'target_variable': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_target_variable', None),
                'remove_consecutive_nans': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_remove_consecutive_nans', None),
                'consecutive_nan_threshold': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_consecutive_nan_threshold', None),
                'type_mapping_sheet': dfm_refactor.get_dfm_state('data_prep', 'dfm_param_type_mapping_sheet', None)
            }

            if not processing_params['export_base_name']:
                st.error("错误：请指定有效的文件基础名称！")
            else:
                try:
                    # 🔥 优化：添加详细的进度指示器
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    try:
                        status_text.text("🔄 正在准备数据...")
                        progress_bar.progress(10)

                        uploaded_file_bytes = current_file.getvalue()
                        excel_file_like_object = io.BytesIO(uploaded_file_bytes)

                        # 使用批量获取的参数
                        start_date = processing_params['data_start_date']
                        start_date_str = start_date.strftime('%Y-%m-%d') if start_date else None

                        end_date = processing_params['data_end_date']
                        end_date_str = end_date.strftime('%Y-%m-%d') if end_date else None

                        status_text.text("📊 正在读取数据文件...")
                        progress_bar.progress(20)

                        # 🔧 修复：只有在启用移除连续NaN功能时才传递阈值
                        nan_threshold_int = None
                        remove_consecutive_nans = processing_params['remove_consecutive_nans']
                        if remove_consecutive_nans:
                            nan_threshold = processing_params['consecutive_nan_threshold']
                            if not pd.isna(nan_threshold):
                                try:
                                    nan_threshold_int = int(nan_threshold)
                                except ValueError:
                                    st.warning(f"连续NaN阈值 '{nan_threshold}' 不是一个有效的整数。将忽略此阈值。")
                                    nan_threshold_int = None

                        # 数据预处理参数
                        status_text.text("🔧 正在执行数据预处理...")
                        progress_bar.progress(30)

                        # 调用真正的数据预处理函数
                        from dashboard.DFM.data_prep.data_preparation import prepare_data

                        try:
                            results = prepare_data(
                                excel_path=excel_file_like_object,
                                target_freq=processing_params['target_freq'],
                                target_sheet_name=processing_params['target_sheet_name'],
                                target_variable_name=processing_params['target_variable'],
                                consecutive_nan_threshold=nan_threshold_int,
                                data_start_date=str(processing_params['data_start_date']),
                                data_end_date=str(processing_params['data_end_date']),
                                reference_sheet_name=processing_params['type_mapping_sheet']
                            )

                            status_text.text("✅ 数据预处理完成，正在生成结果...")
                            progress_bar.progress(70)

                            # 解包结果
                            if results and len(results) >= 4:
                                prepared_data, industry_map, transform_log, removed_variables_detailed_log = results
                            else:
                                prepared_data = None
                                industry_map = {}
                                transform_log = {}
                                removed_variables_detailed_log = []
                                st.warning("数据预处理返回的结果格式不正确")

                        except Exception as prep_e:
                            st.error(f"数据预处理失败: {prep_e}")
                            import traceback
                            st.text_area("预处理错误详情:", traceback.format_exc(), height=150)
                            prepared_data = None
                            industry_map = {}
                            transform_log = {}
                            removed_variables_detailed_log = []

                        if prepared_data is not None:
                            status_text.text("📋 正在处理结果数据...")
                            progress_bar.progress(80)

                            # 保存数据对象到统一状态管理器
                            set_dfm_state("dfm_prepared_data_df", prepared_data)
                            set_dfm_state("dfm_transform_log_obj", transform_log)
                            set_dfm_state("dfm_industry_map_obj", industry_map)
                            set_dfm_state("dfm_removed_vars_log_obj", removed_variables_detailed_log)
                            set_dfm_state("dfm_var_type_map_obj", {})

                            st.success("数据预处理完成！结果已准备就绪，可用于模型训练模块。")
                            st.info(f"📊 预处理后数据形状: {prepared_data.shape}")

                            # Prepare for download (existing logic)
                            export_base_name = processing_params['export_base_name']
                            processed_outputs = {
                                'base_name': export_base_name,
                                'data': None, 'industry_map': None, 'transform_log': None, 'removed_vars_log': None
                            }

                            if prepared_data is not None:
                                processed_outputs['data'] = prepared_data.to_csv(index=True, index_label='Date', encoding='utf-8-sig').encode('utf-8-sig')

                            if industry_map:
                                try:
                                    df_industry_map = pd.DataFrame(list(industry_map.items()), columns=['Indicator', 'Industry'])
                                    processed_outputs['industry_map'] = df_industry_map.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
                                except Exception as e_im:
                                    st.warning(f"行业映射转换到CSV时出错: {e_im}")
                                    processed_outputs['industry_map'] = None

                            # 保存处理结果到统一状态管理器
                            set_dfm_state("dfm_processed_outputs", processed_outputs)

                        else:
                            progress_bar.progress(100)
                            status_text.text("❌ 处理失败")
                            st.error("数据预处理失败或未返回数据。请检查控制台日志获取更多信息。")
                            set_dfm_state("dfm_processed_outputs", None)

                        # 🔥 优化：完成进度指示器
                        if 'progress_bar' in locals():
                            progress_bar.progress(100)
                            status_text.text("🎉 处理完成！")
                            time.sleep(1)
                            progress_bar.empty()
                            status_text.empty()

                    except Exception as e:
                        st.error(f"运行数据预处理时发生错误: {e}")
                        import traceback
                        st.text_area("详细错误信息:", traceback.format_exc(), height=200)
                        set_dfm_state("dfm_processed_outputs", None)

                except Exception as outer_e:
                    st.error(f"数据预处理过程中发生未预期的错误: {outer_e}")
                    import traceback
                    st.text_area("详细错误信息:", traceback.format_exc(), height=200)

        # Render download buttons if data is available
        processed_outputs = get_dfm_state("dfm_processed_outputs")
        if processed_outputs:
            outputs = processed_outputs
            base_name = outputs['base_name']

            # 创建三列布局用于水平排列下载按钮
            col1, col2, col3 = st.columns(3)

            with col1:
                if outputs.get('data'):
                    st.download_button(
                        label="下载处理后的数据",
                        data=outputs['data'],
                        file_name=f"{base_name}_data_v3.csv",
                        mime='text/csv',
                        key='download_data_csv'
                    )

            with col2:
                if outputs.get('industry_map'):
                    st.download_button(
                        label="下载行业映射",
                        data=outputs['industry_map'],
                        file_name=f"{base_name}_industry_map_v3.csv",
                        mime='text/csv',
                        key='download_industry_map_csv'
                    )

            with col3:
                if outputs.get('transform_log'):
                    st.download_button(
                        label="下载转换日志",
                        data=outputs['transform_log'],
                        file_name=f"{base_name}_transform_log_v3.csv",
                        mime='text/csv',
                        key='download_transform_log_csv'
                    )

            if outputs.get('removed_vars_log'):
                st.download_button(
                    label=f"下载移除变量日志 ({base_name}_removed_log_v3.csv)",
                    data=outputs['removed_vars_log'],
                    file_name=f"{base_name}_removed_log_v3.csv",
                    mime='text/csv',
                    key='download_removed_log_csv'
                )


def _auto_load_mapping_data_if_needed(current_file):
    """
    自动加载映射数据（如果需要）- 优化版本

    Args:
        current_file: 当前文件对象
    """
    # 会话级缓存，避免重复加载
    cache_key = f"mapping_loaded_{current_file.name if current_file else 'none'}"
    if st.session_state.get(cache_key, False):
        return

    try:
        # 检查是否已经加载了映射数据
        existing_industry_map = get_dfm_state('dfm_industry_map_obj', None)
        existing_type_map = get_dfm_state('dfm_var_type_map_obj', None)

        # 如果映射数据已存在且不为空，则不需要重新加载
        if (existing_industry_map and len(existing_industry_map) > 0 and
            existing_type_map and len(existing_type_map) > 0):
            st.session_state[cache_key] = True
            return

        if current_file is None:
            return

        # 获取映射表名称
        mapping_sheet_name = get_dfm_state('dfm_param_type_mapping_sheet', '指标体系')

        # 加载映射数据
        from dashboard.DFM.data_prep.data_preparation import load_mappings

        var_type_map, var_industry_map_loaded = load_mappings(
            excel_path=current_file,
            sheet_name=mapping_sheet_name,
            indicator_col='高频指标',
            type_col='类型',
            industry_col='行业'
        )

        # 保存映射数据
        final_industry_map = var_industry_map_loaded if var_industry_map_loaded else {}
        final_type_map = var_type_map if var_type_map else {}

        set_dfm_state("dfm_var_type_map_obj", final_type_map)
        set_dfm_state("dfm_industry_map_obj", final_industry_map)

        # 标记为已加载，避免重复加载
        st.session_state[cache_key] = True

    except Exception as e:
        # 静默处理映射数据加载失败
        pass


def render_dfm_data_prep_page(st_module: Any) -> Dict[str, Any]:
    """
    渲染DFM数据预处理页面

    Args:
        st_module: Streamlit模块

    Returns:
        Dict[str, Any]: 渲染结果
    """
    try:
        # 调用主要的UI渲染函数
        render_dfm_data_prep_tab(st_module)

        return {
            'status': 'success',
            'page': 'data_prep',
            'components': ['file_upload', 'parameter_config', 'data_processing']
        }

    except Exception as e:
        st_module.error(f"数据预处理页面渲染失败: {str(e)}")
        return {
            'status': 'error',
            'page': 'data_prep',
            'error': str(e)
        }


