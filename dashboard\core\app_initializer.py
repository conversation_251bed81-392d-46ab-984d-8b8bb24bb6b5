# -*- coding: utf-8 -*-
"""
应用初始化器
优化启动流程，减少启动时间
"""

import streamlit as st
import os
import sys
import warnings
import logging
import shutil
import glob
from pathlib import Path
from typing import Dict, Any
import time

from .lazy_loader import get_cached_lazy_loader

# 延迟导入统一状态管理系统避免循环导入

logger = logging.getLogger(__name__)

class AppInitializer:
    """应用初始化器"""
    
    def __init__(self):
        self.initialization_time = 0
        self.initialized_components = set()
    
    def clear_cache_and_logs(self):
        """清理所有缓存、日志、__pycache__和旧结果文件"""
        start_time = time.time()
        
        try:
            # 获取项目根目录
            current_dir = Path(__file__).parent.parent
            project_root = current_dir.parent
            
            # 定义需要清理的目录和文件模式
            cleanup_targets = [
                # 缓存目录
                project_root / "cache",
                current_dir / "cache", 
                # 日志目录
                project_root / "logs",
                current_dir / "logs",
                # 其他可能的临时文件
                project_root / "*.tmp",
                project_root / "*.log",
                current_dir / "*.tmp",
                current_dir / "*.log"
            ]
            
            cleaned_items = []
            
            # 清理常规目录和文件
            for target in cleanup_targets:
                try:
                    if target.exists():
                        if target.is_dir():
                            # 清理目录内容但保留目录结构
                            for item in target.iterdir():
                                if item.is_file():
                                    item.unlink()
                                    cleaned_items.append(f"文件: {item}")
                                elif item.is_dir():
                                    shutil.rmtree(item)
                                    cleaned_items.append(f"目录: {item}")
                        elif target.is_file():
                            target.unlink()
                            cleaned_items.append(f"文件: {target}")
                    else:
                        # 处理glob模式的文件
                        if "*" in str(target):
                            pattern_files = glob.glob(str(target))
                            for file_path in pattern_files:
                                os.remove(file_path)
                                cleaned_items.append(f"模式文件: {file_path}")
                                
                except Exception as e:
                    logger.warning(f"清理 {target} 时出错: {e}")
            
            # 递归清理所有 __pycache__ 目录
            self._clean_pycache_directories(project_root, cleaned_items)
            
            setup_time = time.time() - start_time
            
            if cleaned_items:
                logger.info(f"✅ 缓存、日志和__pycache__清理完成，共清理 {len(cleaned_items)} 项，耗时 {setup_time:.3f}s")
                for item in cleaned_items[:5]:  # 只显示前5项
                    logger.debug(f"  清理项: {item}")
                if len(cleaned_items) > 5:
                    logger.debug(f"  ... 还有 {len(cleaned_items) - 5} 项")
            else:
                logger.info(f"✅ 没有发现需要清理的缓存或日志文件，耗时 {setup_time:.3f}s")
                
            self.initialized_components.add('cleanup')
            
        except Exception as e:
            logger.error(f"❌ 缓存和日志清理失败: {e}")
            # 不阻断应用启动，继续执行
    
    def _clean_pycache_directories(self, root_path, cleaned_items):
        """递归清理所有__pycache__目录"""
        try:
            for item in root_path.rglob("__pycache__"):
                if item.is_dir():
                    try:
                        shutil.rmtree(item)
                        cleaned_items.append(f"__pycache__目录: {item}")
                    except Exception as e:
                        logger.warning(f"清理__pycache__目录 {item} 时出错: {e}")
        except Exception as e:
            logger.warning(f"搜索__pycache__目录时出错: {e}")
    
    def setup_environment(self):
        """设置环境变量和警告抑制"""
        start_time = time.time()

        # 设置环境变量
        env_vars = {
            'STREAMLIT_LOGGER_LEVEL': 'CRITICAL',
            'STREAMLIT_CLIENT_TOOLBAR_MODE': 'minimal',
            'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false',
            'STREAMLIT_CLIENT_SHOW_ERROR_DETAILS': 'false',
            'PYTHONWARNINGS': 'ignore',
            'STREAMLIT_SILENT_IMPORTS': 'true',
            'STREAMLIT_SUPPRESS_WARNINGS': 'true'
        }

        for key, value in env_vars.items():
            os.environ[key] = value

        # 抑制Streamlit特定的警告（保留统一状态管理器的日志配置）
        warnings.filterwarnings("ignore")

        # 只抑制Streamlit相关的日志器，不影响应用日志
        streamlit_loggers = [
            "streamlit", "streamlit.runtime",
            "streamlit.runtime.scriptrunner_utils",
            "streamlit.runtime.scriptrunner_utils.script_run_context",
            "streamlit.runtime.caching",
            "streamlit.runtime.caching.cache_data_api",
            "streamlit.runtime.state",
            "streamlit.runtime.state.session_state_proxy",
            "streamlit.web",
            "streamlit.web.server",
            "streamlit.web.bootstrap"
        ]

        for logger_name in streamlit_loggers:
            logger_obj = logging.getLogger(logger_name)
            logger_obj.setLevel(logging.CRITICAL)
            logger_obj.disabled = True
            logger_obj.propagate = False

        # 特别处理 Streamlit 的 script_run_context 警告
        class StreamlitWarningFilter(logging.Filter):
            def filter(self, record):
                # 过滤掉 ScriptRunContext 相关的警告
                if hasattr(record, 'msg') and record.msg:
                    msg = str(record.msg)
                    if 'ScriptRunContext' in msg or 'missing ScriptRunContext' in msg:
                        return False
                    if 'Thread \'MainThread\': missing ScriptRunContext' in msg:
                        return False
                    if 'can be ignored when running in bare mode' in msg:
                        return False
                return True

        # 为所有 Streamlit 相关的日志器添加过滤器
        warning_filter = StreamlitWarningFilter()
        for logger_name in streamlit_loggers:
            logger_obj = logging.getLogger(logger_name)
            logger_obj.addFilter(warning_filter)

        setup_time = time.time() - start_time
        logger.debug(f"Environment setup completed in {setup_time:.3f}s")
        self.initialized_components.add('environment')
    
    def setup_paths(self):
        """设置项目路径"""
        start_time = time.time()
        
        current_dir = Path(__file__).parent.parent
        project_root = current_dir.parent
        
        # 添加项目根目录到sys.path
        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))
        
        setup_time = time.time() - start_time
        logger.debug(f"Path setup completed in {setup_time:.3f}s")
        self.initialized_components.add('paths')
    
    def configure_streamlit(self):
        """配置Streamlit页面"""
        start_time = time.time()

        try:
            # 获取统一状态管理器（单例模式，无需存储在session_state）
            from dashboard.state_management import get_unified_manager
            state_manager = get_unified_manager()
            already_configured = state_manager.get_state('core.streamlit_configured', False)

            if not already_configured:
                # 页面配置已在主脚本中完成，这里只记录状态
                state_manager.set_state('core.streamlit_configured', True)
                logger.info("Streamlit页面配置状态记录完成")

        except Exception as e:
            logger.error(f"Failed to initialize streamlit: {e}")

        setup_time = time.time() - start_time
        logger.debug(f"Streamlit configuration completed in {setup_time:.3f}s")
        self.initialized_components.add('streamlit')
    
    def load_styles(self):
        """加载应用样式 - 使用新的UI初始化器"""
        start_time = time.time()

        try:
            # 使用新的UI初始化器
            from dashboard.ui.utils.style_initializer import get_ui_initializer
            ui_initializer = get_ui_initializer()
            ui_initializer.load_styles()
            logger.info("使用UI初始化器加载样式成功")
        except Exception as e:
            logger.warning(f"Failed to load styles via UI initializer: {e}")
            # 回退到基本样式
            self._load_default_styles()

        setup_time = time.time() - start_time
        logger.debug(f"Styles loaded in {setup_time:.3f}s")
        self.initialized_components.add('styles')

    def _handle_style_loading_failure(self, error: Exception):
        """处理样式加载失败"""
        self.logger.error(f"样式加载失败: {error}")
        # 不再加载fallback样式，而是记录错误并继续
        # 应用程序应该能够在没有自定义样式的情况下正常运行
    
    def initialize_state_manager(self):
        """初始化状态管理器"""
        start_time = time.time()

        try:
            # 获取统一状态管理器（单例模式，无需存储在session_state）
            from dashboard.state_management import get_unified_manager
            state_manager = get_unified_manager()
            logger.info("使用统一状态管理器实例")
            already_initialized = state_manager.get_state('core.unified_state_manager_initialized', False)

            if not already_initialized:
                state_manager.set_state('core.unified_state_manager_initialized', True)
                logger.info("统一状态管理器初始化完成")
            else:
                logger.debug("统一状态管理器已经初始化过")

        except Exception as e:
            logger.error(f"Failed to initialize state_manager: {e}")

        setup_time = time.time() - start_time
        logger.debug(f"State manager setup completed in {setup_time:.3f}s")
        self.initialized_components.add('state_manager')
    
    def initialize_lazy_loader(self):
        """初始化懒加载器"""
        start_time = time.time()
        
        try:
            lazy_loader = get_cached_lazy_loader()
            # 预加载关键模块
            lazy_loader.preload_critical_modules()
        except Exception as e:
            logger.error(f"Failed to initialize lazy_loader: {e}")
        
        setup_time = time.time() - start_time
        logger.debug(f"Lazy loader initialized in {setup_time:.3f}s")
        self.initialized_components.add('lazy_loader')
    
    def full_initialize(self) -> Dict[str, Any]:
        """完整初始化应用"""
        total_start_time = time.time()
        
        # 按顺序执行初始化步骤 - 首先清理缓存和日志
        initialization_steps = [
            ('cleanup', self.clear_cache_and_logs),  # 新增：第一步清理
            ('environment', self.setup_environment),
            ('paths', self.setup_paths),
            ('streamlit', self.configure_streamlit),
            ('styles', self.load_styles),
            ('state_manager', self.initialize_state_manager),
            ('lazy_loader', self.initialize_lazy_loader)
        ]
        
        step_times = {}
        
        for step_name, step_func in initialization_steps:
            if step_name not in self.initialized_components:
                step_start = time.time()
                try:
                    step_func()
                    step_times[step_name] = time.time() - step_start
                except Exception as e:
                    logger.error(f"Failed to initialize {step_name}: {e}")
                    step_times[step_name] = -1
        
        self.initialization_time = time.time() - total_start_time
        
        return {
            'total_time': self.initialization_time,
            'step_times': step_times,
            'initialized_components': list(self.initialized_components)
        }
    
    def get_initialization_stats(self) -> Dict[str, Any]:
        """获取初始化统计信息"""
        return {
            'initialization_time': self.initialization_time,
            'initialized_components': list(self.initialized_components),
            'component_count': len(self.initialized_components)
        }

    def get_initialization_summary(self):
        """获取初始化摘要"""
        summary = {
            'total_components': len(self.initialized_components),
            'components': list(self.initialized_components),
            'status': 'complete' if self.is_complete() else 'partial'
        }
        
        # 确保状态管理器可用（单例模式，无需存储在session_state）
        from dashboard.state_management import get_unified_manager
        # 使用get_unified_manager()确保状态管理器可用

# 全局初始化器实例
_app_initializer = None

def get_app_initializer() -> AppInitializer:
    """获取全局应用初始化器实例"""
    global _app_initializer
    if _app_initializer is None:
        _app_initializer = AppInitializer()
    return _app_initializer

def initialize_app() -> Dict[str, Any]:
    """应用初始化函数"""
    initializer = AppInitializer()
    return initializer.full_initialize()
