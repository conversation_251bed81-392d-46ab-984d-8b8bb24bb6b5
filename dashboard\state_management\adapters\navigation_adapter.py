# -*- coding: utf-8 -*-
"""
Navigation模块适配器
提供导航模块的统一状态管理接口
"""

import threading
import time
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

# 导入核心基础设施
from ..core.module_manager import ModuleManager
from ..core.data_flow_controller import DataFlowController
from ..core.state_synchronizer import StateSynchronizer
from ..core.performance_monitor import PerformanceMonitor
from ..core.error_handler import ErrorHandler
from ..core.config_manager import ConfigManager
from ..core.logging_monitor import LoggingMonitor, MetricType


class NavigationMode(Enum):
    """导航模式枚举"""
    SIDEBAR = "sidebar"
    TABS = "tabs"
    BREADCRUMB = "breadcrumb"
    MENU = "menu"
    WIZARD = "wizard"


class NavigationState(Enum):
    """导航状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISABLED = "disabled"
    HIDDEN = "hidden"
    LOADING = "loading"


class NavigationEvent(Enum):
    """导航事件枚举"""
    NAVIGATE = "navigate"
    BACK = "back"
    FORWARD = "forward"
    REFRESH = "refresh"
    CLOSE = "close"
    EXPAND = "expand"
    COLLAPSE = "collapse"


class ThemeMode(Enum):
    """主题模式枚举"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


@dataclass
class NavigationItem:
    """导航项"""
    item_id: str
    label: str
    path: str
    icon: Optional[str] = None
    parent_id: Optional[str] = None
    children: List[str] = field(default_factory=list)
    state: NavigationState = NavigationState.INACTIVE
    metadata: Dict[str, Any] = field(default_factory=dict)
    order: int = 0
    permissions: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    @property
    def is_active(self) -> bool:
        """检查是否为活跃状态"""
        return self.state == NavigationState.ACTIVE
    
    @property
    def is_disabled(self) -> bool:
        """检查是否被禁用"""
        return self.state == NavigationState.DISABLED
    
    @property
    def is_visible(self) -> bool:
        """检查是否可见"""
        return self.state != NavigationState.HIDDEN
    
    @property
    def has_children(self) -> bool:
        """检查是否有子项"""
        return len(self.children) > 0


@dataclass
class UserPreference:
    """用户偏好设置"""
    user_id: str
    theme_mode: ThemeMode = ThemeMode.AUTO
    navigation_mode: NavigationMode = NavigationMode.SIDEBAR
    sidebar_collapsed: bool = False
    favorite_items: List[str] = field(default_factory=list)
    recent_items: List[str] = field(default_factory=list)
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    last_visited_path: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class NavigationHistory:
    """导航历史记录"""
    session_id: str
    path: str
    timestamp: datetime = field(default_factory=datetime.now)
    event_type: NavigationEvent = NavigationEvent.NAVIGATE
    metadata: Dict[str, Any] = field(default_factory=dict)
    duration: Optional[float] = None


@dataclass
class NavigationStats:
    """导航统计信息"""
    total_items: int = 0
    active_items: int = 0
    disabled_items: int = 0
    hidden_items: int = 0
    total_navigations: int = 0
    unique_paths_visited: int = 0
    average_session_duration: float = 0.0
    most_visited_paths: List[str] = field(default_factory=list)
    last_activity: Optional[datetime] = None


class NavigationAdapter:
    """Navigation模块适配器 - 统一管理导航模块的状态和用户偏好"""
    
    def __init__(
        self,
        state_manager=None,  # 添加state_manager参数以兼容调用
        module_manager: ModuleManager = None,
        data_flow_controller: DataFlowController = None,
        state_synchronizer: StateSynchronizer = None,
        performance_monitor: PerformanceMonitor = None,
        error_handler: ErrorHandler = None,
        config_manager: ConfigManager = None,
        logging_monitor: LoggingMonitor = None
    ):
        """
        初始化Navigation适配器

        Args:
            state_manager: 统一状态管理器（可选）
            module_manager: 模块管理器
            data_flow_controller: 数据流控制器
            state_synchronizer: 状态同步器
            performance_monitor: 性能监控器
            error_handler: 错误处理器
            config_manager: 配置管理器
            logging_monitor: 日志监控器
        """
        self.state_manager = state_manager  # 存储state_manager引用
        self.module_manager = module_manager
        self.data_flow_controller = data_flow_controller
        self.state_synchronizer = state_synchronizer
        self.performance_monitor = performance_monitor
        self.error_handler = error_handler
        self.config_manager = config_manager
        self.logging_monitor = logging_monitor
        
        # 状态键映射
        self.state_key_mapping = {
            # 导航状态
            'navigation': {
                'current_path': 'nav.current_path',
                'active_item': 'nav.active_item',
                'navigation_mode': 'nav.navigation_mode',
                'sidebar_collapsed': 'nav.sidebar_collapsed',
                'breadcrumb_items': 'nav.breadcrumb_items',
                'navigation_history': 'nav.navigation_history'
            },
            # 用户偏好
            'preferences': {
                'theme_mode': 'nav.prefs.theme_mode',
                'navigation_mode': 'nav.prefs.navigation_mode',
                'sidebar_collapsed': 'nav.prefs.sidebar_collapsed',
                'favorite_items': 'nav.prefs.favorite_items',
                'recent_items': 'nav.prefs.recent_items',
                'custom_settings': 'nav.prefs.custom_settings',
                'last_visited_path': 'nav.prefs.last_visited_path'
            },
            # UI状态
            'ui': {
                'loading_state': 'nav.ui.loading_state',
                'error_message': 'nav.ui.error_message',
                'notification': 'nav.ui.notification',
                'modal_state': 'nav.ui.modal_state',
                'tooltip_state': 'nav.ui.tooltip_state'
            }
        }
        
        # 导航项管理
        self.navigation_items: Dict[str, NavigationItem] = {}
        self.navigation_tree: Dict[str, List[str]] = defaultdict(list)  # parent_id -> children_ids
        
        # 用户偏好管理
        self.user_preferences: Dict[str, UserPreference] = {}
        
        # 导航历史
        self.navigation_history: deque = deque(maxlen=1000)
        self.session_histories: Dict[str, List[NavigationHistory]] = defaultdict(list)
        
        # 状态存储
        self.state_storage: Dict[str, Any] = {}
        
        # 事件回调
        self.event_callbacks: Dict[NavigationEvent, List[Callable]] = defaultdict(list)
        
        # 统计信息
        self.stats = NavigationStats()
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 注册到模块管理器
        if self.module_manager:
            self.module_manager.register_module(name="navigation_adapter")
        
        # 记录初始化日志
        if self.logging_monitor:
            logger = self.logging_monitor.get_logger("navigation_adapter")
            logger.info("NavigationAdapter initialized successfully")
    
    def get_state(self, category: str, key: str, default: Any = None) -> Any:
        """获取导航状态"""
        try:
            # 尝试使用映射键
            unified_key = None
            if category in self.state_key_mapping:
                mapping = self.state_key_mapping[category]
                if key in mapping:
                    unified_key = mapping[key]
            
            # 如果没有映射，使用原始键结构
            if unified_key is None:
                unified_key = f"nav.{category}.{key}"
            
            # 从状态存储获取状态
            return self.state_storage.get(unified_key, default)
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "get_state",
                "category": category,
                "key": key
            })
            return default
    
    def set_state(self, category: str, key: str, value: Any) -> bool:
        """设置导航状态"""
        try:
            # 尝试使用映射键
            unified_key = None
            if category in self.state_key_mapping:
                mapping = self.state_key_mapping[category]
                if key in mapping:
                    unified_key = mapping[key]
            
            # 如果没有映射，使用原始键结构
            if unified_key is None:
                unified_key = f"nav.{category}.{key}"
            
            # 设置到状态存储
            with self._lock:
                self.state_storage[unified_key] = value
            
            # 触发状态同步
            if self.state_synchronizer:
                self.state_synchronizer.trigger_sync(unified_key)
            
            # 记录性能指标
            if self.performance_monitor:
                self.performance_monitor.record_metric(
                    "navigation_state_update",
                    1,
                    {"category": category, "key": key}
                )
            
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "set_state",
                "category": category,
                "key": key
            })
            return False
    
    def add_navigation_item(
        self,
        item_id: str,
        label: str,
        path: str,
        icon: str = None,
        parent_id: str = None,
        order: int = 0,
        permissions: List[str] = None
    ) -> bool:
        """添加导航项"""
        try:
            with self._lock:
                # 检查是否已存在
                if item_id in self.navigation_items:
                    return False
                
                # 创建导航项
                nav_item = NavigationItem(
                    item_id=item_id,
                    label=label,
                    path=path,
                    icon=icon,
                    parent_id=parent_id,
                    order=order,
                    permissions=permissions or []
                )
                
                self.navigation_items[item_id] = nav_item
                
                # 更新导航树
                if parent_id:
                    self.navigation_tree[parent_id].append(item_id)
                    # 更新父项的children列表
                    if parent_id in self.navigation_items:
                        self.navigation_items[parent_id].children.append(item_id)
                else:
                    self.navigation_tree["root"].append(item_id)
                
                # 按order排序
                if parent_id:
                    self.navigation_tree[parent_id].sort(
                        key=lambda x: self.navigation_items[x].order
                    )
                else:
                    self.navigation_tree["root"].sort(
                        key=lambda x: self.navigation_items[x].order
                    )
                
                # 更新统计信息
                self.stats.total_items += 1
                
                # 记录指标
                if self.logging_monitor:
                    self.logging_monitor.record_metric(
                        "navigation_item_added",
                        1,
                        MetricType.COUNTER,
                        tags={"parent_id": parent_id or "root"}
                    )
                
                return True
                
        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "add_navigation_item",
                "item_id": item_id
            })
            return False

    def remove_navigation_item(self, item_id: str) -> bool:
        """移除导航项"""
        try:
            with self._lock:
                if item_id not in self.navigation_items:
                    return False

                nav_item = self.navigation_items[item_id]

                # 递归移除所有子项
                for child_id in nav_item.children.copy():
                    self.remove_navigation_item(child_id)

                # 从父项中移除
                if nav_item.parent_id:
                    if nav_item.parent_id in self.navigation_tree:
                        if item_id in self.navigation_tree[nav_item.parent_id]:
                            self.navigation_tree[nav_item.parent_id].remove(item_id)

                    if nav_item.parent_id in self.navigation_items:
                        if item_id in self.navigation_items[nav_item.parent_id].children:
                            self.navigation_items[nav_item.parent_id].children.remove(item_id)
                else:
                    if item_id in self.navigation_tree["root"]:
                        self.navigation_tree["root"].remove(item_id)

                # 移除导航项
                del self.navigation_items[item_id]
                if item_id in self.navigation_tree:
                    del self.navigation_tree[item_id]

                # 更新统计信息
                self.stats.total_items -= 1

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "remove_navigation_item",
                "item_id": item_id
            })
            return False

    def update_navigation_item(
        self,
        item_id: str,
        label: str = None,
        path: str = None,
        icon: str = None,
        state: NavigationState = None,
        order: int = None
    ) -> bool:
        """更新导航项"""
        try:
            with self._lock:
                if item_id not in self.navigation_items:
                    return False

                nav_item = self.navigation_items[item_id]

                # 更新属性
                if label is not None:
                    nav_item.label = label
                if path is not None:
                    nav_item.path = path
                if icon is not None:
                    nav_item.icon = icon
                if state is not None:
                    nav_item.state = state
                if order is not None:
                    nav_item.order = order
                    # 重新排序
                    if nav_item.parent_id:
                        self.navigation_tree[nav_item.parent_id].sort(
                            key=lambda x: self.navigation_items[x].order
                        )
                    else:
                        self.navigation_tree["root"].sort(
                            key=lambda x: self.navigation_items[x].order
                        )

                nav_item.updated_at = datetime.now()

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "update_navigation_item",
                "item_id": item_id
            })
            return False

    def get_navigation_item(self, item_id: str) -> Optional[NavigationItem]:
        """获取导航项"""
        return self.navigation_items.get(item_id)

    def get_navigation_tree(self, parent_id: str = "root") -> List[Dict[str, Any]]:
        """获取导航树"""
        try:
            def build_tree_node(item_id: str) -> Dict[str, Any]:
                if item_id not in self.navigation_items:
                    return None

                nav_item = self.navigation_items[item_id]
                node = {
                    'item_id': nav_item.item_id,
                    'label': nav_item.label,
                    'path': nav_item.path,
                    'icon': nav_item.icon,
                    'state': nav_item.state.value,
                    'order': nav_item.order,
                    'children': []
                }

                # 递归构建子节点
                for child_id in self.navigation_tree.get(item_id, []):
                    child_node = build_tree_node(child_id)
                    if child_node:
                        node['children'].append(child_node)

                return node

            tree = []
            for item_id in self.navigation_tree.get(parent_id, []):
                node = build_tree_node(item_id)
                if node:
                    tree.append(node)

            return tree

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "get_navigation_tree",
                "parent_id": parent_id
            })
            return []

    def navigate_to(self, path: str, session_id: str = "default") -> bool:
        """导航到指定路径"""
        try:
            with self._lock:
                # 记录导航历史
                history_entry = NavigationHistory(
                    session_id=session_id,
                    path=path,
                    event_type=NavigationEvent.NAVIGATE
                )

                self.navigation_history.append(history_entry)
                self.session_histories[session_id].append(history_entry)

                # 更新当前路径
                self.set_state('navigation', 'current_path', path)

                # 更新活跃导航项
                active_item = self._find_item_by_path(path)
                if active_item:
                    # 设置新的活跃项
                    active_item.state = NavigationState.ACTIVE
                    self.set_state('navigation', 'active_item', active_item.item_id)

                    # 取消其他项的活跃状态
                    for item_id, nav_item in self.navigation_items.items():
                        if item_id != active_item.item_id and nav_item.state == NavigationState.ACTIVE:
                            nav_item.state = NavigationState.INACTIVE

                # 更新统计信息
                self.stats.total_navigations += 1
                self.stats.last_activity = datetime.now()

                # 触发导航事件回调
                self._trigger_event_callbacks(NavigationEvent.NAVIGATE, {
                    'path': path,
                    'session_id': session_id,
                    'timestamp': datetime.now()
                })

                # 记录指标
                if self.logging_monitor:
                    self.logging_monitor.record_metric(
                        "navigation_event",
                        1,
                        MetricType.COUNTER,
                        tags={"event": "navigate", "path": path}
                    )

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "navigate_to",
                "path": path,
                "session_id": session_id
            })
            return False

    def _find_item_by_path(self, path: str) -> Optional[NavigationItem]:
        """根据路径查找导航项"""
        for nav_item in self.navigation_items.values():
            if nav_item.path == path:
                return nav_item
        return None

    def get_user_preferences(self, user_id: str) -> Optional[UserPreference]:
        """获取用户偏好"""
        return self.user_preferences.get(user_id)

    def set_user_preferences(
        self,
        user_id: str,
        theme_mode: ThemeMode = None,
        navigation_mode: NavigationMode = None,
        sidebar_collapsed: bool = None,
        custom_settings: Dict[str, Any] = None
    ) -> bool:
        """设置用户偏好"""
        try:
            with self._lock:
                if user_id not in self.user_preferences:
                    self.user_preferences[user_id] = UserPreference(user_id=user_id)

                prefs = self.user_preferences[user_id]

                # 更新偏好设置
                if theme_mode is not None:
                    prefs.theme_mode = theme_mode
                    self.set_state('preferences', 'theme_mode', theme_mode.value)

                if navigation_mode is not None:
                    prefs.navigation_mode = navigation_mode
                    self.set_state('preferences', 'navigation_mode', navigation_mode.value)

                if sidebar_collapsed is not None:
                    prefs.sidebar_collapsed = sidebar_collapsed
                    self.set_state('preferences', 'sidebar_collapsed', sidebar_collapsed)

                if custom_settings is not None:
                    prefs.custom_settings.update(custom_settings)
                    self.set_state('preferences', 'custom_settings', prefs.custom_settings)

                prefs.updated_at = datetime.now()

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "set_user_preferences",
                "user_id": user_id
            })
            return False

    def add_to_favorites(self, user_id: str, item_id: str) -> bool:
        """添加到收藏夹"""
        try:
            with self._lock:
                if user_id not in self.user_preferences:
                    self.user_preferences[user_id] = UserPreference(user_id=user_id)

                prefs = self.user_preferences[user_id]

                if item_id not in prefs.favorite_items:
                    prefs.favorite_items.append(item_id)
                    prefs.updated_at = datetime.now()
                    self.set_state('preferences', 'favorite_items', prefs.favorite_items)
                    return True

                return False

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "add_to_favorites",
                "user_id": user_id,
                "item_id": item_id
            })
            return False

    def remove_from_favorites(self, user_id: str, item_id: str) -> bool:
        """从收藏夹移除"""
        try:
            with self._lock:
                if user_id not in self.user_preferences:
                    return False

                prefs = self.user_preferences[user_id]

                if item_id in prefs.favorite_items:
                    prefs.favorite_items.remove(item_id)
                    prefs.updated_at = datetime.now()
                    self.set_state('preferences', 'favorite_items', prefs.favorite_items)
                    return True

                return False

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "remove_from_favorites",
                "user_id": user_id,
                "item_id": item_id
            })
            return False

    def add_to_recent(self, user_id: str, item_id: str, max_recent: int = 10) -> bool:
        """添加到最近访问"""
        try:
            with self._lock:
                if user_id not in self.user_preferences:
                    self.user_preferences[user_id] = UserPreference(user_id=user_id)

                prefs = self.user_preferences[user_id]

                # 如果已存在，先移除
                if item_id in prefs.recent_items:
                    prefs.recent_items.remove(item_id)

                # 添加到开头
                prefs.recent_items.insert(0, item_id)

                # 限制数量
                if len(prefs.recent_items) > max_recent:
                    prefs.recent_items = prefs.recent_items[:max_recent]

                prefs.updated_at = datetime.now()
                self.set_state('preferences', 'recent_items', prefs.recent_items)

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "add_to_recent",
                "user_id": user_id,
                "item_id": item_id
            })
            return False

    def get_navigation_history(self, session_id: str = None, limit: int = 100) -> List[NavigationHistory]:
        """获取导航历史"""
        try:
            if session_id:
                history = self.session_histories.get(session_id, [])
                return history[-limit:] if limit > 0 else history
            else:
                history = list(self.navigation_history)
                return history[-limit:] if limit > 0 else history

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "get_navigation_history",
                "session_id": session_id
            })
            return []

    def add_event_callback(self, event: NavigationEvent, callback: Callable):
        """添加事件回调"""
        self.event_callbacks[event].append(callback)

    def remove_event_callback(self, event: NavigationEvent, callback: Callable):
        """移除事件回调"""
        if event in self.event_callbacks:
            try:
                self.event_callbacks[event].remove(callback)
            except ValueError:
                pass

    def _trigger_event_callbacks(self, event: NavigationEvent, data: Dict[str, Any]):
        """触发事件回调"""
        try:
            for callback in self.event_callbacks.get(event, []):
                try:
                    callback(event, data)
                except Exception as e:
                    self.error_handler.handle_error(e, {
                        "module": "navigation_adapter",
                        "function": "_trigger_event_callbacks",
                        "event": event.value,
                        "callback_error": True
                    })

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "_trigger_event_callbacks",
                "event": event.value
            })

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            # 更新统计信息
            active_count = sum(1 for item in self.navigation_items.values()
                             if item.state == NavigationState.ACTIVE)
            disabled_count = sum(1 for item in self.navigation_items.values()
                               if item.state == NavigationState.DISABLED)
            hidden_count = sum(1 for item in self.navigation_items.values()
                             if item.state == NavigationState.HIDDEN)

            self.stats.active_items = active_count
            self.stats.disabled_items = disabled_count
            self.stats.hidden_items = hidden_count

            # 计算唯一访问路径数
            unique_paths = set()
            for history in self.navigation_history:
                unique_paths.add(history.path)
            self.stats.unique_paths_visited = len(unique_paths)

            # 计算最常访问的路径
            path_counts = defaultdict(int)
            for history in self.navigation_history:
                path_counts[history.path] += 1

            most_visited = sorted(path_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            self.stats.most_visited_paths = [path for path, count in most_visited]

            return {
                'total_items': self.stats.total_items,
                'active_items': self.stats.active_items,
                'disabled_items': self.stats.disabled_items,
                'hidden_items': self.stats.hidden_items,
                'total_navigations': self.stats.total_navigations,
                'unique_paths_visited': self.stats.unique_paths_visited,
                'average_session_duration': self.stats.average_session_duration,
                'most_visited_paths': self.stats.most_visited_paths,
                'last_activity': self.stats.last_activity,
                'total_users': len(self.user_preferences),
                'total_sessions': len(self.session_histories),
                'navigation_history_size': len(self.navigation_history)
            }

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "get_statistics"
            })
            return {}

    def clear_history(self, session_id: str = None):
        """清空导航历史"""
        try:
            with self._lock:
                if session_id:
                    if session_id in self.session_histories:
                        self.session_histories[session_id].clear()
                else:
                    self.navigation_history.clear()
                    self.session_histories.clear()

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "navigation_adapter",
                "function": "clear_history",
                "session_id": session_id
            })


# 助手函数
def create_navigation_item(
    item_id: str,
    label: str,
    path: str,
    icon: str = None,
    parent_id: str = None,
    order: int = 0,
    permissions: List[str] = None
) -> Dict[str, Any]:
    """创建导航项配置的助手函数"""
    return {
        'item_id': item_id,
        'label': label,
        'path': path,
        'icon': icon,
        'parent_id': parent_id,
        'order': order,
        'permissions': permissions or []
    }


def create_sidebar_navigation(
    items: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """创建侧边栏导航的助手函数"""
    navigation_items = []

    for item in items:
        nav_item = create_navigation_item(
            item_id=item.get('id'),
            label=item.get('label'),
            path=item.get('path'),
            icon=item.get('icon'),
            parent_id=item.get('parent_id'),
            order=item.get('order', 0),
            permissions=item.get('permissions')
        )
        navigation_items.append(nav_item)

    return navigation_items


def create_breadcrumb_navigation(
    current_path: str,
    navigation_items: Dict[str, NavigationItem]
) -> List[Dict[str, Any]]:
    """创建面包屑导航的助手函数"""
    breadcrumbs = []

    # 查找当前路径对应的导航项
    current_item = None
    for item in navigation_items.values():
        if item.path == current_path:
            current_item = item
            break

    if not current_item:
        return breadcrumbs

    # 构建面包屑路径
    path_items = []
    item = current_item

    while item:
        path_items.insert(0, {
            'item_id': item.item_id,
            'label': item.label,
            'path': item.path,
            'icon': item.icon
        })

        # 查找父项
        if item.parent_id:
            item = navigation_items.get(item.parent_id)
        else:
            item = None

    return path_items


def create_user_preference_config(
    user_id: str,
    theme_mode: str = "auto",
    navigation_mode: str = "sidebar",
    sidebar_collapsed: bool = False,
    custom_settings: Dict[str, Any] = None
) -> Dict[str, Any]:
    """创建用户偏好配置的助手函数"""
    return {
        'user_id': user_id,
        'theme_mode': theme_mode,
        'navigation_mode': navigation_mode,
        'sidebar_collapsed': sidebar_collapsed,
        'custom_settings': custom_settings or {}
    }
