"""
配置和常量模块

包含数据准备过程中使用的所有配置参数和常量定义
"""

from typing import Any

# 调试模式控制
DEBUG_MODE = False  # 设置为False以禁用调试输出

# 测试模式控制
USE_REDUCED_VARIABLES_FOR_TESTING = False  # 关闭测试模式，使用所有变量
CREATE_REDUCED_TEST_SET = False  # 改回 False 以使用完整数据集
REDUCED_SET_SUFFIX = "_REDUCED" if CREATE_REDUCED_TEST_SET else ""  # 根据开关调整后缀

def debug_print(*args, **kwargs):
    """条件化的调试打印函数"""
    if DEBUG_MODE:
        print(*args, **kwargs)

class DataPrepConfig:
    """数据准备配置类"""
    
    def __init__(self):
        # 默认配置参数
        self.debug_mode = DEBUG_MODE
        self.use_reduced_variables = USE_REDUCED_VARIABLES_FOR_TESTING
        self.create_reduced_test_set = CREATE_REDUCED_TEST_SET
        self.reduced_set_suffix = REDUCED_SET_SUFFIX
        
        # ADF检验参数
        self.adf_p_threshold = 0.05
        
        # 数据处理参数
        self.default_consecutive_nan_threshold = 10
        self.default_target_freq = 'W-FRI'
        
        # 默认列名
        self.default_reference_sheet_name = '指标体系'
        self.default_reference_column_name = '高频指标'
        self.default_indicator_col = '高频指标'
        self.default_type_col = '类型'
        self.default_industry_col = '行业'
    
    def set_debug_mode(self, enabled: bool):
        """设置调试模式"""
        self.debug_mode = enabled
        global DEBUG_MODE
        DEBUG_MODE = enabled
    
    def get_config_dict(self) -> dict:
        """获取配置字典"""
        return {
            'debug_mode': self.debug_mode,
            'use_reduced_variables': self.use_reduced_variables,
            'create_reduced_test_set': self.create_reduced_test_set,
            'adf_p_threshold': self.adf_p_threshold,
            'default_consecutive_nan_threshold': self.default_consecutive_nan_threshold,
            'default_target_freq': self.default_target_freq
        }

# 全局配置实例
_global_config = DataPrepConfig()

def get_global_config() -> DataPrepConfig:
    """获取全局配置实例"""
    return _global_config

def set_global_debug_mode(enabled: bool):
    """设置全局调试模式"""
    _global_config.set_debug_mode(enabled)

# 导出的常量和函数
__all__ = [
    'DEBUG_MODE',
    'USE_REDUCED_VARIABLES_FOR_TESTING', 
    'CREATE_REDUCED_TEST_SET',
    'REDUCED_SET_SUFFIX',
    'debug_print',
    'DataPrepConfig',
    'get_global_config',
    'set_global_debug_mode'
]
