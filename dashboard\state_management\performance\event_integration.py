# -*- coding: utf-8 -*-
"""
事件驱动状态管理集成
将状态变更事件系统集成到状态管理器中，实现事件驱动的状态管理
"""

import logging
import threading
import time
from typing import Any, Dict, List, Set, Optional, Callable, Union
from functools import wraps

from ..events import (
    StateEventSystem, 
    StateEvent, 
    EventType, 
    EventListener,
    get_global_event_system
)

logger = logging.getLogger(__name__)


class StateChangeListener:
    """状态变更监听器"""
    
    def __init__(self, callback: Callable[[str, Any, Any], None], 
                 key_patterns: Optional[List[str]] = None):
        """
        初始化状态变更监听器
        
        Args:
            callback: 回调函数，参数为(key, old_value, new_value)
            key_patterns: 监听的键模式
        """
        self.callback = callback
        self.key_patterns = key_patterns or ['*']
        self.event_listener = None
    
    def _handle_event(self, event: StateEvent):
        """处理状态事件"""
        if event.event_type == EventType.STATE_CHANGED:
            self.callback(event.key, event.old_value, event.new_value)


class EventDrivenStateManager:
    """事件驱动状态管理器装饰器"""
    
    def __init__(self, state_manager, event_system: Optional[StateEventSystem] = None):
        """
        初始化事件驱动状态管理器
        
        Args:
            state_manager: 原始状态管理器
            event_system: 事件系统实例
        """
        self.state_manager = state_manager
        self.event_system = event_system or get_global_event_system()
        
        # 状态变更监听器
        self._change_listeners: List[StateChangeListener] = []
        self._listeners_lock = threading.RLock()
        
        # 依赖关系缓存
        self._dependency_cache: Dict[str, Set[str]] = {}
        self._cache_lock = threading.RLock()
        
        # 性能统计
        self.event_stats = {
            'events_emitted': 0,
            'listeners_called': 0,
            'dependency_updates': 0,
            'avg_event_time': 0.0
        }
        self.stats_lock = threading.Lock()
        
        # 集成到状态管理器
        self._integrate_with_state_manager()
        
        logger.info("事件驱动状态管理器初始化完成")
    
    def add_change_listener(self, callback: Callable[[str, Any, Any], None],
                           key_patterns: Optional[List[str]] = None) -> StateChangeListener:
        """
        添加状态变更监听器
        
        Args:
            callback: 回调函数
            key_patterns: 监听的键模式
            
        Returns:
            StateChangeListener: 监听器实例
        """
        listener = StateChangeListener(callback, key_patterns)
        
        # 添加到事件系统
        listener.event_listener = self.event_system.add_listener(
            listener._handle_event,
            event_types={EventType.STATE_CHANGED},
            key_patterns=key_patterns
        )
        
        with self._listeners_lock:
            self._change_listeners.append(listener)
        
        logger.debug(f"添加状态变更监听器: {key_patterns}")
        return listener
    
    def remove_change_listener(self, listener: StateChangeListener):
        """移除状态变更监听器"""
        with self._listeners_lock:
            if listener in self._change_listeners:
                self._change_listeners.remove(listener)
                
                # 从事件系统移除
                if listener.event_listener:
                    self.event_system.remove_listener(listener.event_listener)
                
                logger.debug("移除状态变更监听器")
    
    def add_dependency(self, key: str, depends_on: str):
        """添加状态依赖关系"""
        self.event_system.dependency_manager.add_dependency(key, depends_on)
        
        # 更新缓存
        with self._cache_lock:
            if key not in self._dependency_cache:
                self._dependency_cache[key] = set()
            self._dependency_cache[key].add(depends_on)
        
        logger.debug(f"添加状态依赖: {key} -> {depends_on}")
    
    def remove_dependency(self, key: str, depends_on: str):
        """移除状态依赖关系"""
        self.event_system.dependency_manager.remove_dependency(key, depends_on)
        
        # 更新缓存
        with self._cache_lock:
            if key in self._dependency_cache:
                self._dependency_cache[key].discard(depends_on)
        
        logger.debug(f"移除状态依赖: {key} -> {depends_on}")
    
    def get_dependencies(self, key: str) -> Set[str]:
        """获取状态的依赖关系"""
        return self.event_system.dependency_manager.get_dependencies(key)
    
    def get_dependents(self, key: str) -> Set[str]:
        """获取依赖此状态的其他状态"""
        return self.event_system.dependency_manager.get_dependents(key)
    
    def invalidate_dependents(self, key: str):
        """使依赖此状态的其他状态失效"""
        dependents = self.get_dependents(key)
        
        for dependent_key in dependents:
            # 发布依赖变更事件
            self.event_system.emit_event(StateEvent(
                event_type=EventType.DEPENDENCY_CHANGED,
                key=dependent_key,
                metadata={'trigger_key': key}
            ))
        
        with self.stats_lock:
            self.event_stats['dependency_updates'] += len(dependents)
        
        if dependents:
            logger.debug(f"使{len(dependents)}个依赖状态失效: {key}")
    
    def get_event_stats(self) -> Dict[str, Any]:
        """获取事件统计信息"""
        with self.stats_lock:
            stats = self.event_stats.copy()
        
        # 添加事件系统统计
        event_history = self.event_system.get_event_history(limit=100)
        listener_stats = self.event_system.get_listener_stats()
        
        stats.update({
            'recent_events': len(event_history),
            'total_listeners': listener_stats['total_listeners'],
            'change_listeners': len(self._change_listeners)
        })
        
        return stats
    
    def _integrate_with_state_manager(self):
        """集成到状态管理器"""
        # 保存原始方法
        if not hasattr(self.state_manager, '_original_get_state'):
            self.state_manager._original_get_state = self.state_manager.get_state
        if not hasattr(self.state_manager, '_original_set_state'):
            self.state_manager._original_set_state = self.state_manager.set_state
        if not hasattr(self.state_manager, '_original_delete_state'):
            if hasattr(self.state_manager, 'delete_state'):
                self.state_manager._original_delete_state = self.state_manager.delete_state
        
        # 包装方法以发布事件
        self.state_manager.get_state = self._wrap_get_state(self.state_manager._original_get_state)
        self.state_manager.set_state = self._wrap_set_state(self.state_manager._original_set_state)
        
        if hasattr(self.state_manager, 'delete_state'):
            self.state_manager.delete_state = self._wrap_delete_state(self.state_manager._original_delete_state)
        
        # 添加事件相关方法
        self.state_manager.add_change_listener = self.add_change_listener
        self.state_manager.remove_change_listener = self.remove_change_listener
        self.state_manager.add_dependency = self.add_dependency
        self.state_manager.remove_dependency = self.remove_dependency
        self.state_manager.get_dependencies = self.get_dependencies
        self.state_manager.get_dependents = self.get_dependents
        self.state_manager.invalidate_dependents = self.invalidate_dependents
        self.state_manager.get_event_stats = self.get_event_stats
        
        # 保存事件管理器引用
        self.state_manager._event_manager = self
    
    def _wrap_get_state(self, original_get_state):
        """包装get_state方法以发布访问事件"""
        @wraps(original_get_state)
        def wrapped_get_state(key: str, default: Any = None, emit_event: bool = False, **kwargs):
            result = original_get_state(key, default, **kwargs)
            
            # 发布访问事件（可选）
            if emit_event:
                self.event_system.emit_state_accessed(key, result)
            
            return result
        
        return wrapped_get_state
    
    def _wrap_set_state(self, original_set_state):
        """包装set_state方法以发布变更事件"""
        @wraps(original_set_state)
        def wrapped_set_state(key: str, value: Any, emit_event: bool = True, **kwargs):
            start_time = time.time()
            
            # 获取旧值
            old_value = None
            if emit_event:
                try:
                    old_value = self.state_manager._original_get_state(key, None)
                except Exception:
                    old_value = None
            
            # 执行原始设置
            result = original_set_state(key, value, **kwargs)
            
            # 发布事件
            if emit_event and result:
                if old_value is None:
                    # 新创建的状态
                    self.event_system.emit_state_created(key, value)
                else:
                    # 状态变更
                    self.event_system.emit_state_changed(key, old_value, value)
                
                # 使依赖状态失效
                self.invalidate_dependents(key)
                
                # 更新统计
                event_time = time.time() - start_time
                with self.stats_lock:
                    self.event_stats['events_emitted'] += 1
                    self.event_stats['avg_event_time'] = (
                        (self.event_stats['avg_event_time'] * (self.event_stats['events_emitted'] - 1) + event_time) /
                        self.event_stats['events_emitted']
                    )
            
            return result
        
        return wrapped_set_state
    
    def _wrap_delete_state(self, original_delete_state):
        """包装delete_state方法以发布删除事件"""
        @wraps(original_delete_state)
        def wrapped_delete_state(key: str, emit_event: bool = True, **kwargs):
            # 获取旧值
            old_value = None
            if emit_event:
                try:
                    old_value = self.state_manager._original_get_state(key, None)
                except Exception:
                    old_value = None
            
            # 执行原始删除
            result = original_delete_state(key, **kwargs)
            
            # 发布删除事件
            if emit_event and result and old_value is not None:
                self.event_system.emit_state_deleted(key, old_value)
                
                # 清除依赖关系
                self.event_system.dependency_manager.clear_dependencies(key)
                
                # 更新缓存
                with self._cache_lock:
                    self._dependency_cache.pop(key, None)
                
                # 更新统计
                with self.stats_lock:
                    self.event_stats['events_emitted'] += 1
            
            return result
        
        return wrapped_delete_state


def integrate_event_system(state_manager, event_system: Optional[StateEventSystem] = None) -> EventDrivenStateManager:
    """
    将事件系统集成到状态管理器中
    
    Args:
        state_manager: 状态管理器实例
        event_system: 事件系统实例
        
    Returns:
        EventDrivenStateManager: 事件驱动状态管理器
    """
    event_manager = EventDrivenStateManager(state_manager, event_system)
    logger.info("事件系统已集成到状态管理器")
    return event_manager


# 便捷装饰器
def event_driven(event_system: Optional[StateEventSystem] = None):
    """事件驱动装饰器"""
    def decorator(state_manager_class):
        original_init = state_manager_class.__init__
        
        @wraps(original_init)
        def new_init(self, *args, **kwargs):
            original_init(self, *args, **kwargs)
            integrate_event_system(self, event_system)
        
        state_manager_class.__init__ = new_init
        return state_manager_class
    
    return decorator


# 导出的公共接口
__all__ = [
    'EventDrivenStateManager',
    'StateChangeListener',
    'integrate_event_system',
    'event_driven'
]
