# -*- coding: utf-8 -*-
"""
透视表组件
提供数据透视表功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import logging

from .base import PreprocessingComponent

logger = logging.getLogger(__name__)


class PivotTableComponent(PreprocessingComponent):
    """透视表组件"""
    
    def __init__(self):
        super().__init__("pivot_table", "透视表")
    
    def render_processing_interface(self, st_obj, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """渲染透视表处理界面"""
        
        st_obj.markdown("#### 透视表配置")
        
        # 获取所有列
        all_cols = data.columns.tolist()
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = data.select_dtypes(include=['object', 'category']).columns.tolist()
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            # 行索引
            index_cols = st_obj.multiselect(
                "选择行索引:",
                options=all_cols,
                key=f"{self.component_name}_index_cols"
            )
            
            # 值字段
            value_cols = st_obj.multiselect(
                "选择值字段:",
                options=numeric_cols,
                default=numeric_cols[:2] if len(numeric_cols) >= 2 else numeric_cols,
                key=f"{self.component_name}_value_cols"
            )
        
        with col2:
            # 列索引
            column_cols = st_obj.multiselect(
                "选择列索引:",
                options=all_cols,
                key=f"{self.component_name}_column_cols"
            )
            
            # 聚合函数
            agg_func = st_obj.selectbox(
                "聚合函数:",
                options=["mean", "sum", "count", "min", "max", "std"],
                format_func=lambda x: {"mean": "平均值", "sum": "求和", "count": "计数", 
                                     "min": "最小值", "max": "最大值", "std": "标准差"}[x],
                key=f"{self.component_name}_agg_func"
            )
        
        # 高级选项
        with st_obj.expander("高级选项"):
            fill_value = st_obj.number_input(
                "填充值 (用于缺失值):",
                value=0.0,
                key=f"{self.component_name}_fill_value"
            )
            
            margins = st_obj.checkbox(
                "显示边际总计",
                key=f"{self.component_name}_margins"
            )
            
            dropna = st_obj.checkbox(
                "删除包含NaN的行",
                value=True,
                key=f"{self.component_name}_dropna"
            )
        
        if st_obj.button("生成透视表", key=f"{self.component_name}_generate_pivot"):
            
            if not value_cols:
                st_obj.error("请至少选择一个值字段")
                return data
            
            if not index_cols and not column_cols:
                st_obj.error("请至少选择行索引或列索引")
                return data
            
            try:
                # 生成透视表
                pivot_table = pd.pivot_table(
                    data,
                    values=value_cols,
                    index=index_cols if index_cols else None,
                    columns=column_cols if column_cols else None,
                    aggfunc=agg_func,
                    fill_value=fill_value,
                    margins=margins,
                    dropna=dropna
                )
                
                # 如果是多级索引，展平列名
                if isinstance(pivot_table.columns, pd.MultiIndex):
                    pivot_table.columns = ['_'.join(map(str, col)).strip() for col in pivot_table.columns.values]
                
                # 重置索引
                pivot_table = pivot_table.reset_index()
                
                # 记录操作
                self.add_to_operation_history(
                    "透视表生成",
                    f"生成透视表，行索引: {index_cols}, 列索引: {column_cols}, 值字段: {value_cols}",
                    {
                        "index": index_cols,
                        "columns": column_cols,
                        "values": value_cols,
                        "aggfunc": agg_func
                    }
                )
                
                st_obj.success(f"透视表生成完成！形状: {pivot_table.shape}")
                
                # 显示透视表预览
                st_obj.markdown("**透视表预览:**")
                st_obj.dataframe(pivot_table, use_container_width=True)
                
                return pivot_table
                
            except Exception as e:
                st_obj.error(f"透视表生成失败: {str(e)}")
                return data
        
        return data


__all__ = ['PivotTableComponent']
