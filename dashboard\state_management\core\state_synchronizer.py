# -*- coding: utf-8 -*-
"""
状态同步器
管理模块间状态同步、一致性保证和冲突解决
"""

import logging
from typing import Dict, List, Optional, Any, Callable, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
import copy
import time
from .state_metadata import StateScope, DataType


class SyncStrategy(Enum):
    """同步策略枚举"""
    IMMEDIATE = "immediate"      # 立即同步
    BATCHED = "batched"         # 批量同步
    SCHEDULED = "scheduled"     # 定时同步
    ON_DEMAND = "on_demand"     # 按需同步


class ConflictResolution(Enum):
    """冲突解决策略枚举"""
    LAST_WRITE_WINS = "last_write_wins"    # 最后写入获胜
    FIRST_WRITE_WINS = "first_write_wins"  # 首次写入获胜
    MERGE = "merge"                        # 合并策略
    MANUAL = "manual"                      # 手动解决
    REJECT = "reject"                      # 拒绝冲突


class SyncStatus(Enum):
    """同步状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"


@dataclass
class SyncRule:
    """同步规则"""
    source_key: str
    target_keys: List[str]
    strategy: SyncStrategy = SyncStrategy.IMMEDIATE
    conflict_resolution: ConflictResolution = ConflictResolution.LAST_WRITE_WINS
    transform_func: Optional[Callable[[Any], Any]] = None
    condition_func: Optional[Callable[[], bool]] = None
    batch_size: int = 10
    sync_interval_seconds: int = 60
    enabled: bool = True
    description: str = ""


@dataclass
class SyncOperation:
    """同步操作记录"""
    operation_id: str
    source_key: str
    target_key: str
    strategy: SyncStrategy
    status: SyncStatus = SyncStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    data_size: int = 0
    execution_time_ms: float = 0.0
    conflict_details: Optional[Dict[str, Any]] = None


@dataclass
class ConflictRecord:
    """冲突记录"""
    conflict_id: str
    source_key: str
    target_key: str
    source_value: Any
    target_value: Any
    source_timestamp: datetime
    target_timestamp: datetime
    resolution_strategy: ConflictResolution
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    resolved_value: Optional[Any] = None
    resolver: Optional[str] = None


class StateSynchronizer:
    """状态同步器 - 管理模块间状态同步"""
    
    def __init__(self, unified_manager=None, module_manager=None):
        """
        初始化状态同步器
        
        Args:
            unified_manager: 统一状态管理器实例
            module_manager: 模块管理器实例
        """
        self.unified_manager = unified_manager
        self.module_manager = module_manager
        self.logger = logging.getLogger(__name__)
        
        # 同步规则
        self._sync_rules: Dict[str, SyncRule] = {}
        
        # 操作历史
        self._operation_history: List[SyncOperation] = []
        
        # 活跃的同步操作
        self._active_operations: Dict[str, SyncOperation] = {}
        
        # 冲突记录
        self._conflicts: Dict[str, ConflictRecord] = {}
        
        # 批量同步队列
        self._batch_queue: Dict[str, List[Tuple[str, Any]]] = {}
        
        # 定时同步任务
        self._scheduled_tasks: Dict[str, datetime] = {}
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._total_syncs = 0
        self._successful_syncs = 0
        self._failed_syncs = 0
        self._conflicts_detected = 0
        self._conflicts_resolved = 0
        
        # 同步器状态
        self._is_running = False
        self._last_batch_sync = datetime.now()
        self._last_scheduled_sync = datetime.now()
        
        # 状态处理器
        self._state_handlers: Dict[str, Callable[[Any, Any], None]] = {}
        
        self.logger.info("StateSynchronizer initialized")
    
    def register_sync_rule(self, 
                          rule_id: str,
                          source_key: str,
                          target_keys: List[str],
                          strategy: SyncStrategy = SyncStrategy.IMMEDIATE,
                          conflict_resolution: ConflictResolution = ConflictResolution.LAST_WRITE_WINS,
                          transform_func: Optional[Callable[[Any], Any]] = None,
                          condition_func: Optional[Callable[[], bool]] = None,
                          batch_size: int = 10,
                          sync_interval_seconds: int = 60,
                          description: str = "") -> bool:
        """
        注册同步规则
        
        Args:
            rule_id: 规则唯一标识
            source_key: 源状态键
            target_keys: 目标状态键列表
            strategy: 同步策略
            conflict_resolution: 冲突解决策略
            transform_func: 数据转换函数
            condition_func: 同步条件函数
            batch_size: 批量同步大小
            sync_interval_seconds: 同步间隔（秒）
            description: 规则描述
            
        Returns:
            bool: 注册是否成功
        """
        with self._lock:
            try:
                # 检查规则是否已存在
                if rule_id in self._sync_rules:
                    self.logger.warning(f"Sync rule {rule_id} already exists")
                    return False
                
                # 创建同步规则
                rule = SyncRule(
                    source_key=source_key,
                    target_keys=target_keys,
                    strategy=strategy,
                    conflict_resolution=conflict_resolution,
                    transform_func=transform_func,
                    condition_func=condition_func,
                    batch_size=batch_size,
                    sync_interval_seconds=sync_interval_seconds,
                    description=description
                )
                
                # 注册规则
                self._sync_rules[rule_id] = rule
                
                # 初始化批量队列
                if strategy == SyncStrategy.BATCHED:
                    self._batch_queue[rule_id] = []
                
                # 初始化定时任务
                if strategy == SyncStrategy.SCHEDULED:
                    self._scheduled_tasks[rule_id] = datetime.now() + timedelta(seconds=sync_interval_seconds)
                
                self.logger.info(f"Sync rule {rule_id} registered successfully")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to register sync rule {rule_id}: {e}")
                return False

    def register_state_handler(self, state_key: str, handler: Callable[[Any, Any], None]) -> bool:
        """
        注册状态变化处理器
        
        Args:
            state_key: 状态键
            handler: 处理函数，接收 (old_value, new_value) 参数
            
        Returns:
            bool: 注册是否成功
        """
        try:
            self._state_handlers[state_key] = handler
            self.logger.info(f"State handler registered for key: {state_key}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register state handler for {state_key}: {e}")
            return False

    def trigger_state_handlers(self, state_key: str, old_value: Any, new_value: Any):
        """
        触发状态变化处理器
        
        Args:
            state_key: 状态键
            old_value: 旧值
            new_value: 新值
        """
        try:
            if state_key in self._state_handlers:
                handler = self._state_handlers[state_key]
                handler(old_value, new_value)
        except Exception as e:
            self.logger.error(f"Error in state handler for {state_key}: {e}")
    
    def trigger_sync(self, source_key: str, force: bool = False) -> List[str]:
        """
        触发与指定键相关的同步操作
        
        Args:
            source_key: 源状态键
            force: 是否强制同步（忽略条件检查）
            
        Returns:
            List[str]: 成功执行的同步操作ID列表
        """
        executed_operations = []
        
        with self._lock:
            try:
                # 查找相关的同步规则
                for rule_id, rule in self._sync_rules.items():
                    if rule.source_key == source_key and rule.enabled:
                        
                        # 检查同步条件
                        if not force and rule.condition_func:
                            if not rule.condition_func():
                                self.logger.debug(f"Sync condition not met for rule {rule_id}")
                                continue
                        
                        # 根据策略执行同步
                        if rule.strategy == SyncStrategy.IMMEDIATE:
                            operation_ids = self._execute_immediate_sync(rule_id, rule)
                            executed_operations.extend(operation_ids)
                        
                        elif rule.strategy == SyncStrategy.BATCHED:
                            self._add_to_batch_queue(rule_id, rule, source_key)
                        
                        elif rule.strategy == SyncStrategy.ON_DEMAND:
                            # 按需同步只在明确请求时执行
                            if force:
                                operation_ids = self._execute_immediate_sync(rule_id, rule)
                                executed_operations.extend(operation_ids)
                
                self.logger.debug(f"Triggered {len(executed_operations)} sync operations for key {source_key}")
                
            except Exception as e:
                self.logger.error(f"Failed to trigger sync for key {source_key}: {e}")
        
        return executed_operations
    
    def _execute_immediate_sync(self, rule_id: str, rule: SyncRule) -> List[str]:
        """执行立即同步"""
        operation_ids = []
        
        try:
            # 获取源数据
            if not self.unified_manager.has_state(rule.source_key):
                self.logger.warning(f"Source key {rule.source_key} not found for rule {rule_id}")
                return operation_ids
            
            source_data = self.unified_manager.get_state(rule.source_key)
            
            # 应用转换函数
            if rule.transform_func:
                try:
                    source_data = rule.transform_func(source_data)
                except Exception as e:
                    self.logger.error(f"Transform function failed for rule {rule_id}: {e}")
                    return operation_ids
            
            # 同步到所有目标键
            for target_key in rule.target_keys:
                operation_id = f"{rule_id}_{target_key}_{int(time.time() * 1000)}"
                
                # 创建同步操作记录
                operation = SyncOperation(
                    operation_id=operation_id,
                    source_key=rule.source_key,
                    target_key=target_key,
                    strategy=rule.strategy,
                    status=SyncStatus.IN_PROGRESS,
                    started_at=datetime.now(),
                    data_size=len(str(source_data))
                )
                
                self._active_operations[operation_id] = operation
                
                # 执行同步
                success = self._sync_to_target(operation, source_data, rule.conflict_resolution)
                
                if success:
                    operation.status = SyncStatus.COMPLETED
                    operation.completed_at = datetime.now()
                    operation.execution_time_ms = (
                        operation.completed_at - operation.started_at
                    ).total_seconds() * 1000
                    
                    self._successful_syncs += 1
                    operation_ids.append(operation_id)
                else:
                    operation.status = SyncStatus.FAILED
                    operation.completed_at = datetime.now()
                    self._failed_syncs += 1
                
                # 完成操作
                self._complete_operation(operation)
        
        except Exception as e:
            self.logger.error(f"Failed to execute immediate sync for rule {rule_id}: {e}")
        
        return operation_ids

    def _sync_to_target(self, operation: SyncOperation, data: Any, conflict_resolution: ConflictResolution) -> bool:
        """同步数据到目标键"""
        try:
            target_key = operation.target_key

            # 检查目标键是否存在
            if self.unified_manager.has_state(target_key):
                # 检测冲突
                existing_data = self.unified_manager.get_state(target_key)
                existing_metadata = self.unified_manager.get_state_metadata(target_key)

                # 简单的冲突检测：比较数据内容
                if str(existing_data) != str(data):
                    conflict_id = f"conflict_{target_key}_{int(time.time() * 1000)}"

                    conflict = ConflictRecord(
                        conflict_id=conflict_id,
                        source_key=operation.source_key,
                        target_key=target_key,
                        source_value=data,
                        target_value=existing_data,
                        source_timestamp=datetime.now(),
                        target_timestamp=existing_metadata.last_modified if existing_metadata else datetime.now(),
                        resolution_strategy=conflict_resolution
                    )

                    self._conflicts[conflict_id] = conflict
                    self._conflicts_detected += 1

                    # 根据冲突解决策略处理
                    resolved_value = self._resolve_conflict(conflict)
                    if resolved_value is not None:
                        data = resolved_value
                        conflict.resolved = True
                        conflict.resolved_at = datetime.now()
                        conflict.resolved_value = resolved_value
                        self._conflicts_resolved += 1
                    else:
                        operation.status = SyncStatus.CONFLICT
                        operation.conflict_details = {
                            'conflict_id': conflict_id,
                            'strategy': conflict_resolution.value
                        }
                        return False

            # 设置目标数据
            success = self.unified_manager.set_state(
                target_key,
                data,
                scope=StateScope.SHARED,
                description=f"Synchronized from {operation.source_key}"
            )

            return success

        except Exception as e:
            operation.error_message = str(e)
            self.logger.error(f"Failed to sync to target {operation.target_key}: {e}")
            return False

    def _resolve_conflict(self, conflict: ConflictRecord) -> Optional[Any]:
        """解决冲突"""
        try:
            if conflict.resolution_strategy == ConflictResolution.LAST_WRITE_WINS:
                # 比较时间戳，选择最新的
                if conflict.source_timestamp >= conflict.target_timestamp:
                    return conflict.source_value
                else:
                    return conflict.target_value

            elif conflict.resolution_strategy == ConflictResolution.FIRST_WRITE_WINS:
                # 选择最早的
                if conflict.source_timestamp <= conflict.target_timestamp:
                    return conflict.source_value
                else:
                    return conflict.target_value

            elif conflict.resolution_strategy == ConflictResolution.MERGE:
                # 简单的合并策略（针对字典类型）
                if isinstance(conflict.source_value, dict) and isinstance(conflict.target_value, dict):
                    merged = conflict.target_value.copy()
                    merged.update(conflict.source_value)
                    return merged
                else:
                    # 非字典类型，回退到最后写入获胜
                    return conflict.source_value

            elif conflict.resolution_strategy == ConflictResolution.REJECT:
                # 拒绝冲突，保持原值
                return conflict.target_value

            elif conflict.resolution_strategy == ConflictResolution.MANUAL:
                # 手动解决，暂时返回None
                return None

        except Exception as e:
            self.logger.error(f"Failed to resolve conflict {conflict.conflict_id}: {e}")

        return None

    def _complete_operation(self, operation: SyncOperation):
        """完成同步操作"""
        # 从活跃操作中移除
        if operation.operation_id in self._active_operations:
            del self._active_operations[operation.operation_id]

        # 添加到历史记录
        self._operation_history.append(operation)
        self._total_syncs += 1

        # 限制历史记录数量
        if len(self._operation_history) > 1000:
            self._operation_history = self._operation_history[-500:]

    def get_sync_rules(self) -> Dict[str, SyncRule]:
        """获取所有同步规则"""
        return self._sync_rules.copy()

    def get_operation_history(self, limit: int = 100) -> List[SyncOperation]:
        """获取操作历史"""
        return self._operation_history[-limit:] if limit > 0 else self._operation_history.copy()

    def get_active_operations(self) -> Dict[str, SyncOperation]:
        """获取活跃的同步操作"""
        return self._active_operations.copy()

    def get_conflicts(self, resolved_only: bool = False) -> Dict[str, ConflictRecord]:
        """获取冲突记录"""
        if resolved_only:
            return {k: v for k, v in self._conflicts.items() if v.resolved}
        return self._conflicts.copy()

    def resolve_conflict_manually(self, conflict_id: str, resolved_value: Any, resolver: str = "manual") -> bool:
        """手动解决冲突"""
        with self._lock:
            try:
                if conflict_id not in self._conflicts:
                    self.logger.error(f"Conflict {conflict_id} not found")
                    return False

                conflict = self._conflicts[conflict_id]
                if conflict.resolved:
                    self.logger.warning(f"Conflict {conflict_id} already resolved")
                    return True

                # 设置解决值
                success = self.unified_manager.set_state(
                    conflict.target_key,
                    resolved_value,
                    scope=StateScope.SHARED,
                    description=f"Manually resolved conflict {conflict_id}"
                )

                if success:
                    conflict.resolved = True
                    conflict.resolved_at = datetime.now()
                    conflict.resolved_value = resolved_value
                    conflict.resolver = resolver
                    self._conflicts_resolved += 1

                    self.logger.info(f"Conflict {conflict_id} resolved manually")
                    return True
                else:
                    self.logger.error(f"Failed to set resolved value for conflict {conflict_id}")
                    return False

            except Exception as e:
                self.logger.error(f"Failed to resolve conflict {conflict_id} manually: {e}")
                return False

    def get_stats(self) -> Dict[str, Any]:
        """获取状态同步器统计信息"""
        return {
            'total_rules': len(self._sync_rules),
            'active_operations': len(self._active_operations),
            'total_syncs': self._total_syncs,
            'successful_syncs': self._successful_syncs,
            'failed_syncs': self._failed_syncs,
            'success_rate': (
                self._successful_syncs / self._total_syncs
                if self._total_syncs > 0 else 0.0
            ),
            'conflicts_detected': self._conflicts_detected,
            'conflicts_resolved': self._conflicts_resolved,
            'conflict_resolution_rate': (
                self._conflicts_resolved / self._conflicts_detected
                if self._conflicts_detected > 0 else 0.0
            ),
            'operation_history_size': len(self._operation_history),
            'pending_batches': sum(len(queue) for queue in self._batch_queue.values()),
            'last_batch_sync': self._last_batch_sync,
            'last_scheduled_sync': self._last_scheduled_sync
        }
