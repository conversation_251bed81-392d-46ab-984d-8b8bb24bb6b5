# -*- coding: utf-8 -*-
"""
数据探索统一状态管理器
重构版本：使用统一状态管理适配器，完全移除直接的session_state访问
"""

import logging
from typing import Any, Optional, Dict, List
import pandas as pd
from datetime import datetime

# 导入数据探索适配器
try:
    from ...state_management.adapters.data_exploration_adapter import get_data_exploration_adapter
except ImportError:
    # 备用导入方式
    try:
        from dashboard.state_management.adapters.data_exploration_adapter import get_data_exploration_adapter
    except ImportError:
        logger = logging.getLogger(__name__)
        logger.error("无法导入DataExplorationAdapter")
        get_data_exploration_adapter = None


class DataExplorationManager:
    """数据探索统一状态管理器 - 重构版本"""

    # 模块常量（保持向后兼容）
    MODULE_STATIONARITY = "stationarity"
    MODULE_TIME_LAG_CORR = "time_lag_corr"
    MODULE_LEAD_LAG = "lead_lag"

    def __init__(self):
        """初始化数据探索管理器"""
        self.logger = logging.getLogger(__name__)

        # 获取数据探索适配器实例
        try:
            if get_data_exploration_adapter:
                self.adapter = get_data_exploration_adapter()
                self.logger.info("DataExplorationManager initialized with DataExplorationAdapter")
            else:
                self.adapter = None
                self.logger.error("DataExplorationAdapter not available")
        except Exception as e:
            self.logger.error(f"Failed to initialize DataExplorationAdapter: {e}")
            self.adapter = None
    
    def set_current_module(self, module_name: str) -> bool:
        """
        设置当前激活模块

        Args:
            module_name: 模块名称 (stationarity, time_lag_corr, lead_lag)

        Returns:
            bool: 设置是否成功
        """
        if not self.adapter:
            self.logger.error("Adapter not available")
            return False

        try:
            success = self.adapter.set_current_module(module_name)
            if success:
                self.logger.info(f"Current module set to: {module_name}")
            else:
                self.logger.error(f"Failed to set current module: {module_name}")
            return success
        except Exception as e:
            self.logger.error(f"Error setting current module {module_name}: {e}")
            return False
    
    def get_current_module(self) -> Optional[str]:
        """
        获取当前激活模块

        Returns:
            str: 当前模块名称，如果未设置则返回None
        """
        if not self.adapter:
            self.logger.error("Adapter not available")
            return None

        try:
            module = self.adapter.get_current_module()
            self.logger.debug(f"Current module retrieved: {module}")
            return module
        except Exception as e:
            self.logger.error(f"Error getting current module: {e}")
            return None

    def clear_current_module(self) -> bool:
        """
        清除当前激活模块

        Returns:
            bool: 清除是否成功
        """
        if not self.adapter:
            self.logger.error("Adapter not available")
            return False

        try:
            success = self.adapter.clear_all_states()
            if success:
                self.logger.info("Current module cleared")
            else:
                self.logger.error("Failed to clear current module")
            return success
        except Exception as e:
            self.logger.error(f"Error clearing current module: {e}")
            return False

    def set_module_data(self, module_name: str, data: pd.DataFrame,
                       file_name: str = None, data_source: str = "upload") -> bool:
        """
        设置模块数据

        Args:
            module_name: 模块名称
            data: 数据DataFrame
            file_name: 文件名
            data_source: 数据源类型 (upload, generated, etc.)

        Returns:
            bool: 设置是否成功
        """
        if not self.adapter:
            self.logger.error("Adapter not available")
            return False

        try:
            success = self.adapter.set_module_data(module_name, data, file_name, data_source)
            if success:
                self.logger.info(f"Data set for module {module_name}: {data.shape if data is not None else 'None'}")
            else:
                self.logger.error(f"Failed to set data for module {module_name}")
            return success

        except Exception as e:
            self.logger.error(f"Error setting data for module {module_name}: {e}")
            return False
    
    def get_module_data(self, module_name: str) -> Optional[pd.DataFrame]:
        """
        获取模块数据

        Args:
            module_name: 模块名称

        Returns:
            pd.DataFrame: 模块数据，如果不存在则返回None
        """
        if not self.adapter:
            self.logger.error("Adapter not available")
            return None

        try:
            data = self.adapter.get_module_data(module_name)

            if data is not None:
                self.logger.debug(f"Data retrieved for module {module_name}: {data.shape}")
            else:
                self.logger.debug(f"No data found for module {module_name}")

            return data
        except Exception as e:
            self.logger.error(f"Error getting data for module {module_name}: {e}")
            return None
    
    def get_module_info(self, module_name: str) -> Dict[str, Any]:
        """
        获取模块完整信息

        Args:
            module_name: 模块名称

        Returns:
            dict: 包含数据、文件名、数据源等信息的字典
        """
        if not self.adapter:
            return {}

        try:
            info = self.adapter.get_module_info(module_name)
            return info
        except Exception as e:
            self.logger.error(f"Error getting module info for {module_name}: {e}")
            return {}
    
    def clear_module_data(self, module_name: str) -> bool:
        """
        清除模块数据

        Args:
            module_name: 模块名称

        Returns:
            bool: 清除是否成功
        """
        if not self.adapter:
            return False

        try:
            success = self.adapter.clear_module_data(module_name)
            if success:
                self.logger.info(f"Data cleared for module {module_name}")
            else:
                self.logger.error(f"Failed to clear data for module {module_name}")

            return success
        except Exception as e:
            self.logger.error(f"Error clearing data for module {module_name}: {e}")
            return False
    
    def get_all_modules_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有模块的状态

        Returns:
            dict: 所有模块的状态信息
        """
        if not self.adapter:
            return {}

        try:
            status = self.adapter.get_all_modules_status()
            return status
        except Exception as e:
            self.logger.error(f"Error getting all modules status: {e}")
            return {}
    
    def reset_all(self) -> bool:
        """
        重置所有状态

        Returns:
            bool: 重置是否成功
        """
        if not self.adapter:
            return False

        try:
            success = self.adapter.clear_all_states()

            # 清除所有模块数据
            modules = [self.MODULE_STATIONARITY, self.MODULE_TIME_LAG_CORR, self.MODULE_LEAD_LAG]
            for module in modules:
                self.clear_module_data(module)

            if success:
                self.logger.info("All data exploration states reset")
            else:
                self.logger.error("Failed to reset all states")
            return success
        except Exception as e:
            self.logger.error(f"Error resetting all states: {e}")
            return False


# 全局实例
_data_exploration_manager = None

def get_data_exploration_manager() -> DataExplorationManager:
    """获取数据探索管理器的全局实例"""
    global _data_exploration_manager
    if _data_exploration_manager is None:
        _data_exploration_manager = DataExplorationManager()
    return _data_exploration_manager
