# -*- coding: utf-8 -*-
"""
状态变更事件系统
提供状态变更监听、事件发布订阅和依赖关系管理
"""

import time
import threading
import logging
import weakref
from typing import Any, Dict, List, Set, Callable, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    STATE_CHANGED = "state_changed"
    STATE_CREATED = "state_created"
    STATE_DELETED = "state_deleted"
    STATE_ACCESSED = "state_accessed"
    DEPENDENCY_CHANGED = "dependency_changed"

    # DFM训练专用事件类型
    TRAINING_STARTED = "training_started"
    TRAINING_PROGRESS = "training_progress"
    TRAINING_COMPLETED = "training_completed"
    TRAINING_FAILED = "training_failed"
    TRAINING_STOPPED = "training_stopped"


@dataclass
class StateEvent:
    """状态事件"""
    event_type: EventType
    key: str
    old_value: Any = None
    new_value: Any = None
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __str__(self):
        return f"StateEvent({self.event_type.value}, {self.key}, {self.timestamp})"


class EventListener:
    """事件监听器基类"""
    
    def __init__(self, callback: Callable[[StateEvent], None], 
                 event_types: Optional[Set[EventType]] = None,
                 key_patterns: Optional[List[str]] = None):
        """
        初始化事件监听器
        
        Args:
            callback: 回调函数
            event_types: 监听的事件类型，None表示监听所有类型
            key_patterns: 监听的键模式，支持通配符
        """
        self.callback = callback
        self.event_types = event_types or set(EventType)
        self.key_patterns = key_patterns or ['*']
        self.created_time = time.time()
        self.call_count = 0
        self.last_called = None
    
    def should_handle(self, event: StateEvent) -> bool:
        """判断是否应该处理此事件"""
        # 检查事件类型
        if event.event_type not in self.event_types:
            return False
        
        # 检查键模式
        if not self._matches_patterns(event.key):
            return False
        
        return True
    
    def handle(self, event: StateEvent):
        """处理事件"""
        try:
            self.callback(event)
            self.call_count += 1
            self.last_called = time.time()
        except Exception as e:
            logger.error(f"事件监听器处理失败: {e}")
    
    def _matches_patterns(self, key: str) -> bool:
        """检查键是否匹配模式"""
        import fnmatch
        
        for pattern in self.key_patterns:
            if fnmatch.fnmatch(key, pattern):
                return True
        return False


class DependencyManager:
    """依赖关系管理器"""
    
    def __init__(self):
        self._dependencies: Dict[str, Set[str]] = defaultdict(set)  # key -> 依赖的keys
        self._dependents: Dict[str, Set[str]] = defaultdict(set)    # key -> 依赖它的keys
        self._lock = threading.RLock()
    
    def add_dependency(self, key: str, depends_on: str):
        """添加依赖关系"""
        with self._lock:
            self._dependencies[key].add(depends_on)
            self._dependents[depends_on].add(key)
            logger.debug(f"添加依赖关系: {key} -> {depends_on}")
    
    def remove_dependency(self, key: str, depends_on: str):
        """移除依赖关系"""
        with self._lock:
            self._dependencies[key].discard(depends_on)
            self._dependents[depends_on].discard(key)
            logger.debug(f"移除依赖关系: {key} -> {depends_on}")
    
    def get_dependencies(self, key: str) -> Set[str]:
        """获取键的所有依赖"""
        with self._lock:
            return self._dependencies[key].copy()
    
    def get_dependents(self, key: str) -> Set[str]:
        """获取依赖此键的所有键"""
        with self._lock:
            return self._dependents[key].copy()
    
    def get_affected_keys(self, changed_key: str) -> Set[str]:
        """获取因键变更而受影响的所有键"""
        with self._lock:
            affected = set()
            to_check = deque([changed_key])
            checked = set()
            
            while to_check:
                current = to_check.popleft()
                if current in checked:
                    continue
                
                checked.add(current)
                dependents = self._dependents[current]
                affected.update(dependents)
                to_check.extend(dependents)
            
            return affected
    
    def clear_dependencies(self, key: str):
        """清除键的所有依赖关系"""
        with self._lock:
            # 移除此键的依赖
            for depends_on in self._dependencies[key]:
                self._dependents[depends_on].discard(key)
            self._dependencies[key].clear()
            
            # 移除依赖此键的关系
            for dependent in self._dependents[key]:
                self._dependencies[dependent].discard(key)
            self._dependents[key].clear()


class StateEventSystem:
    """状态事件系统"""
    
    def __init__(self, max_history: int = 1000, enable_async: bool = True):
        """
        初始化事件系统
        
        Args:
            max_history: 最大事件历史记录数
            enable_async: 是否启用异步事件处理
        """
        self.max_history = max_history
        self.enable_async = enable_async
        
        # 事件监听器
        self._listeners: List[EventListener] = []
        self._listeners_lock = threading.RLock()
        
        # 事件历史
        self._event_history: deque = deque(maxlen=max_history)
        self._history_lock = threading.RLock()
        
        # 依赖管理器
        self.dependency_manager = DependencyManager()
        
        # 异步处理
        if enable_async:
            self._event_queue: deque = deque()
            self._queue_lock = threading.Lock()
            self._worker_thread = threading.Thread(target=self._event_worker, daemon=True)
            self._stop_worker = threading.Event()
            self._worker_thread.start()
        else:
            self._event_queue = None
            self._worker_thread = None
        
        logger.info(f"状态事件系统初始化完成 - 异步: {enable_async}, 历史记录: {max_history}")
    
    def add_listener(self, callback: Callable[[StateEvent], None],
                    event_types: Optional[Set[EventType]] = None,
                    key_patterns: Optional[List[str]] = None) -> EventListener:
        """添加事件监听器"""
        listener = EventListener(callback, event_types, key_patterns)
        
        with self._listeners_lock:
            self._listeners.append(listener)
        
        logger.debug(f"添加事件监听器: {event_types}, {key_patterns}")
        return listener
    
    def remove_listener(self, listener: EventListener):
        """移除事件监听器"""
        with self._listeners_lock:
            if listener in self._listeners:
                self._listeners.remove(listener)
                logger.debug("移除事件监听器")
    
    def emit_event(self, event: StateEvent):
        """发布事件"""
        # 添加到历史记录
        with self._history_lock:
            self._event_history.append(event)
        
        # 处理依赖关系
        if event.event_type in [EventType.STATE_CHANGED, EventType.STATE_CREATED, EventType.STATE_DELETED]:
            self._handle_dependency_changes(event)
        
        # 分发事件
        if self.enable_async and self._event_queue is not None:
            with self._queue_lock:
                self._event_queue.append(event)
        else:
            self._dispatch_event(event)
    
    def emit_state_changed(self, key: str, old_value: Any, new_value: Any, metadata: Dict = None):
        """发布状态变更事件"""
        event = StateEvent(
            event_type=EventType.STATE_CHANGED,
            key=key,
            old_value=old_value,
            new_value=new_value,
            metadata=metadata or {}
        )
        self.emit_event(event)

    def emit_training_started(self, training_config: Dict = None, metadata: Dict = None):
        """发布训练开始事件"""
        event = StateEvent(
            event_type=EventType.TRAINING_STARTED,
            key="dfm_training_status",
            old_value=None,
            new_value="正在训练...",
            metadata={**(metadata or {}), "training_config": training_config}
        )
        self.emit_event(event)

    def emit_training_progress(self, progress: float, message: str = None, metadata: Dict = None):
        """发布训练进度事件"""
        event = StateEvent(
            event_type=EventType.TRAINING_PROGRESS,
            key="dfm_training_progress",
            old_value=None,
            new_value=progress,
            metadata={**(metadata or {}), "message": message}
        )
        self.emit_event(event)

    def emit_training_completed(self, results: Dict = None, metadata: Dict = None):
        """发布训练完成事件"""
        event = StateEvent(
            event_type=EventType.TRAINING_COMPLETED,
            key="dfm_training_status",
            old_value="正在训练...",
            new_value="训练完成",
            metadata={**(metadata or {}), "results": results}
        )
        self.emit_event(event)

    def emit_training_failed(self, error: str, metadata: Dict = None):
        """发布训练失败事件"""
        event = StateEvent(
            event_type=EventType.TRAINING_FAILED,
            key="dfm_training_status",
            old_value="正在训练...",
            new_value="训练失败",
            metadata={**(metadata or {}), "error": error}
        )
        self.emit_event(event)
    
    def emit_state_created(self, key: str, value: Any, metadata: Dict = None):
        """发布状态创建事件"""
        event = StateEvent(
            event_type=EventType.STATE_CREATED,
            key=key,
            new_value=value,
            metadata=metadata or {}
        )
        self.emit_event(event)
    
    def emit_state_deleted(self, key: str, old_value: Any, metadata: Dict = None):
        """发布状态删除事件"""
        event = StateEvent(
            event_type=EventType.STATE_DELETED,
            key=key,
            old_value=old_value,
            metadata=metadata or {}
        )
        self.emit_event(event)
    
    def emit_state_accessed(self, key: str, value: Any, metadata: Dict = None):
        """发布状态访问事件"""
        event = StateEvent(
            event_type=EventType.STATE_ACCESSED,
            key=key,
            new_value=value,
            metadata=metadata or {}
        )
        self.emit_event(event)
    
    def get_event_history(self, limit: Optional[int] = None) -> List[StateEvent]:
        """获取事件历史"""
        with self._history_lock:
            history = list(self._event_history)
            if limit:
                history = history[-limit:]
            return history
    
    def get_listener_stats(self) -> Dict[str, Any]:
        """获取监听器统计信息"""
        with self._listeners_lock:
            return {
                'total_listeners': len(self._listeners),
                'listeners': [
                    {
                        'event_types': [et.value for et in listener.event_types],
                        'key_patterns': listener.key_patterns,
                        'call_count': listener.call_count,
                        'last_called': listener.last_called,
                        'created_time': listener.created_time
                    }
                    for listener in self._listeners
                ]
            }

    def has_pending_events(self) -> bool:
        """
        检查是否有待处理的事件

        Returns:
            bool: 是否有待处理事件
        """
        if not self.enable_async or self._event_queue is None:
            return False

        with self._queue_lock:
            return len(self._event_queue) > 0

    def get_pending_events(self) -> List[StateEvent]:
        """
        获取所有待处理的事件

        Returns:
            List[StateEvent]: 待处理事件列表
        """
        if not self.enable_async or self._event_queue is None:
            return []

        events = []
        with self._queue_lock:
            # 获取所有待处理事件
            events = list(self._event_queue)
            self._event_queue.clear()

        return events


    
    def _dispatch_event(self, event: StateEvent):
        """分发事件到监听器"""
        with self._listeners_lock:
            listeners = self._listeners.copy()
        
        for listener in listeners:
            if listener.should_handle(event):
                try:
                    listener.handle(event)
                except Exception as e:
                    logger.error(f"事件分发失败: {e}")
    
    def _handle_dependency_changes(self, event: StateEvent):
        """处理依赖关系变更"""
        affected_keys = self.dependency_manager.get_affected_keys(event.key)
        
        for affected_key in affected_keys:
            dependency_event = StateEvent(
                event_type=EventType.DEPENDENCY_CHANGED,
                key=affected_key,
                metadata={
                    'trigger_key': event.key,
                    'trigger_event': event.event_type.value
                }
            )
            
            # 直接分发依赖事件，避免递归
            self._dispatch_event(dependency_event)
    
    def _event_worker(self):
        """异步事件处理工作线程"""
        while not self._stop_worker.wait(0.01):  # 10ms检查间隔
            events_to_process = []
            
            # 批量获取事件
            with self._queue_lock:
                while self._event_queue and len(events_to_process) < 100:  # 批量处理最多100个事件
                    events_to_process.append(self._event_queue.popleft())
            
            # 处理事件
            for event in events_to_process:
                self._dispatch_event(event)
    
    def shutdown(self):
        """关闭事件系统"""
        if self._worker_thread:
            self._stop_worker.set()
            self._worker_thread.join(timeout=5.0)
        
        with self._listeners_lock:
            self._listeners.clear()
        
        with self._history_lock:
            self._event_history.clear()
        
        logger.info("状态事件系统已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except Exception:
            pass


# 全局事件系统实例
_global_event_system: Optional[StateEventSystem] = None
_event_system_lock = threading.Lock()


def get_global_event_system() -> StateEventSystem:
    """获取全局事件系统实例"""
    global _global_event_system
    
    if _global_event_system is None:
        with _event_system_lock:
            if _global_event_system is None:
                _global_event_system = StateEventSystem(
                    max_history=2000,
                    enable_async=True
                )
    
    return _global_event_system


def shutdown_global_event_system():
    """关闭全局事件系统"""
    global _global_event_system
    
    if _global_event_system is not None:
        _global_event_system.shutdown()
        _global_event_system = None


# 导出的公共接口
__all__ = [
    'StateEventSystem',
    'StateEvent',
    'EventType',
    'EventListener',
    'DependencyManager',
    'get_global_event_system',
    'shutdown_global_event_system'
]
