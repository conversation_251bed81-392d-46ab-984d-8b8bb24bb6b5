# -*- coding: utf-8 -*-
"""
Preview模块状态管理集成
提供preview模块与新状态管理系统的集成功能
"""

# import streamlit as st  # 改为懒加载，避免在模块级别导入
import pandas as pd
from typing import Any, Optional
import logging

# 导入新的统一状态管理系统 - 强制要求可用
from ..state_management import get_unified_manager
from ..state_management.refactor import PreviewModuleRefactor

logger = logging.getLogger(__name__)

# 全局变量存储管理器实例
_preview_refactor = None

def get_preview_refactor():
    """获取Preview重构模块实例"""
    global _preview_refactor
    if _preview_refactor is None:
        # 直接创建统一状态管理器，不依赖session_state
        unified_manager = get_unified_manager()
        logger.info("✅ 创建统一状态管理器")

        # 创建Preview重构适配器
        _preview_refactor = PreviewModuleRefactor(unified_manager)
        _preview_refactor.on_module_load()

        logger.info("✅ Preview重构适配器初始化成功")

    return _preview_refactor

class PreviewStateManager:
    """Preview模块状态管理器"""

    def __init__(self):
        self.refactor = get_preview_refactor()
        self.use_new_system = self.refactor is not None

        if self.use_new_system:
            logger.info("Using new state management system for preview module")
        else:
            logger.info("Using legacy state management for preview module")
    
    def set_data(self, key: str, value: Any, description: str = "") -> bool:
        """设置数据（只使用统一状态管理）"""
        if self.use_new_system and self.refactor:
            return self.refactor.set_preview_state(key, value)
        else:
            logger.warning(f"统一状态管理器不可用，无法设置: {key}")
            return False

    def get_data(self, key: str, default: Any = None) -> Any:
        """获取数据（只使用统一状态管理）"""
        if self.use_new_system and self.refactor:
            return self.refactor.get_preview_state(key, default)
        else:
            logger.warning(f"统一状态管理器不可用，无法获取: {key}")
            return default

    def has_data(self, key: str) -> bool:
        """检查数据是否存在（只使用统一状态管理）"""
        if self.use_new_system and self.refactor:
            return self.refactor.get_preview_state(key) is not None
        else:
            logger.warning(f"统一状态管理器不可用，无法检查: {key}")
            return False

    def delete_data(self, key: str) -> bool:
        """删除数据（只使用统一状态管理）"""
        if self.use_new_system and self.refactor:
            return self.refactor.delete_state(f"preview.{key}")
        else:
            logger.warning(f"统一状态管理器不可用，无法删除: {key}")
            return False
    
    def clear_all_data(self) -> bool:
        """清除所有数据（只使用统一状态管理）"""
        if self.use_new_system and self.refactor:
            return self.refactor.clear_module_state()
        else:
            logger.warning("统一状态管理器不可用，无法清除数据")
            return False
    
    def load_industrial_data(self, weekly_df: pd.DataFrame, monthly_df: pd.DataFrame,
                           daily_df: pd.DataFrame, source_map: dict,
                           indicator_map: dict, filename: str) -> bool:
        """加载工业数据"""
        if self.use_new_system and self.refactor:
            try:
                # 使用新的状态管理系统设置数据
                self.refactor.set_preview_state('weekly_df', weekly_df)
                self.refactor.set_preview_state('monthly_df', monthly_df)
                self.refactor.set_preview_state('daily_df', daily_df)
                self.refactor.set_preview_state('source_map', source_map)
                self.refactor.set_preview_state('indicator_industry_map', indicator_map)
                self.refactor.set_preview_state('data_loaded_files', filename)

                # 初始化缓存
                self.refactor.set_preview_state('weekly_summary_cache', {})
                self.refactor.set_preview_state('monthly_summary_cache', {})
                self.refactor.set_preview_state('daily_summary_cache', {})

                return True
            except Exception as e:
                logger.error(f"Failed to load industrial data with new system: {e}")
                return False
        else:
            logger.error("统一状态管理器不可用，无法加载工业数据")
            return False
    
    def set_industry_data(self, weekly_industries: list, monthly_industries: list,
                         daily_industries: list, clean_industry_map: dict) -> bool:
        """设置行业数据"""
        if self.use_new_system and self.refactor:
            try:
                self.refactor.set_preview_state('weekly_industries', weekly_industries)
                self.refactor.set_preview_state('monthly_industries', monthly_industries)
                self.refactor.set_preview_state('daily_industries', daily_industries)
                self.refactor.set_preview_state('clean_industry_map', clean_industry_map)
                return True
            except Exception as e:
                logger.error(f"Failed to set industry data with new system: {e}")
                return False
        else:
            logger.error("统一状态管理器不可用，无法设置行业数据")
            return False

    def get_data_summary(self) -> dict:
        """获取数据摘要"""
        if self.use_new_system and self.refactor:
            try:
                weekly_df = self.refactor.get_preview_state('weekly_df', pd.DataFrame())
                monthly_df = self.refactor.get_preview_state('monthly_df', pd.DataFrame())
                daily_df = self.refactor.get_preview_state('daily_df', pd.DataFrame())

                return {
                    'loaded_file': self.refactor.get_preview_state('data_loaded_files'),
                    'weekly_indicators': len(weekly_df.columns) if not weekly_df.empty else 0,
                    'monthly_indicators': len(monthly_df.columns) if not monthly_df.empty else 0,
                    'daily_indicators': len(daily_df.columns) if not daily_df.empty else 0,
                    'weekly_industries': len(self.refactor.get_preview_state('weekly_industries', [])),
                    'monthly_industries': len(self.refactor.get_preview_state('monthly_industries', [])),
                    'daily_industries': len(self.refactor.get_preview_state('daily_industries', [])),
                    'has_data': bool(self.refactor.get_preview_state('data_loaded_files'))
                }
            except Exception as e:
                logger.error(f"Failed to get data summary with new system: {e}")
                return {}
        else:
            logger.error("统一状态管理器不可用，无法获取数据摘要")
            return {}

    def share_data_to_module(self, target_module: str, data_mapping: Optional[dict] = None) -> bool:
        """将数据共享给其他模块"""
        if self.use_new_system and self.refactor:
            # 新系统支持模块间数据共享
            logger.info(f"Data sharing to {target_module} supported in new system")
            return True
        else:
            # 传统方式不支持智能数据共享，返回False
            logger.warning("Data sharing not supported in legacy mode")
            return False

# 全局实例
_preview_state_manager = None

def get_preview_state_manager() -> PreviewStateManager:
    """获取preview状态管理器实例"""
    global _preview_state_manager
    if _preview_state_manager is None:
        _preview_state_manager = PreviewStateManager()
    return _preview_state_manager

# 便捷函数，保持向后兼容
def get_preview_state(key: str, default: Any = None) -> Any:
    """获取预览状态值（兼容函数）"""
    return get_preview_state_manager().get_data(key, default)

def set_preview_state(key: str, value: Any) -> bool:
    """设置预览状态值（兼容函数）"""
    return get_preview_state_manager().set_data(key, value)

def clear_preview_data() -> bool:
    """清理预览数据（兼容函数）"""
    return get_preview_state_manager().clear_all_data()
