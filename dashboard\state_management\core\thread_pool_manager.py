# -*- coding: utf-8 -*-
"""
线程池管理器
提供高效的线程池管理和任务调度功能
"""

import threading
import time
import logging
from typing import Any, Callable, List, Dict, Optional, Union
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from collections import defaultdict


class ThreadPoolManager:
    """线程池管理器"""
    
    def __init__(self, max_workers: Optional[int] = None, thread_name_prefix: str = "StateManager"):
        """
        初始化线程池管理器
        
        Args:
            max_workers: 最大工作线程数，默认为CPU核心数
            thread_name_prefix: 线程名称前缀
        """
        self.logger = logging.getLogger(__name__)
        
        # 线程池配置
        self.max_workers = max_workers or min(32, (threading.active_count() or 1) + 4)
        self.thread_name_prefix = thread_name_prefix
        
        # 创建线程池
        self._executor = ThreadPoolExecutor(
            max_workers=self.max_workers,
            thread_name_prefix=thread_name_prefix
        )
        
        # 统计信息
        self._stats = defaultdict(int)
        self._task_history: List[Dict[str, Any]] = []
        self._active_tasks: Dict[str, Future] = {}
        
        # 锁保护
        self._lock = threading.RLock()
        
        # 状态管理
        self._shutdown = False
        
        self.logger.info(f"ThreadPoolManager initialized with {self.max_workers} workers")
    
    def submit_task(self, func: Callable, *args, **kwargs) -> Future:
        """
        提交单个任务到线程池
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Future对象
            
        Raises:
            RuntimeError: 线程池已关闭
        """
        if self._shutdown:
            raise RuntimeError("ThreadPoolManager has been shut down")
        
        try:
            # 生成任务ID
            task_id = f"task_{int(time.time() * 1000000)}_{id(func)}"
            
            # 包装函数以收集统计信息
            def wrapped_func(*args, **kwargs):
                start_time = time.time()
                thread_name = threading.current_thread().name
                
                try:
                    self._stats['tasks_started'] += 1
                    self.logger.debug(f"Task {task_id} started on {thread_name}")
                    
                    result = func(*args, **kwargs)
                    
                    execution_time = time.time() - start_time
                    self._stats['tasks_completed'] += 1
                    self._stats['total_execution_time'] += execution_time
                    
                    # 记录任务历史
                    with self._lock:
                        self._task_history.append({
                            'task_id': task_id,
                            'function': func.__name__,
                            'thread': thread_name,
                            'start_time': start_time,
                            'execution_time': execution_time,
                            'status': 'completed'
                        })
                        
                        # 限制历史记录数量
                        if len(self._task_history) > 1000:
                            self._task_history = self._task_history[-500:]
                    
                    self.logger.debug(f"Task {task_id} completed in {execution_time:.3f}s")
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    self._stats['tasks_failed'] += 1
                    
                    # 记录失败任务
                    with self._lock:
                        self._task_history.append({
                            'task_id': task_id,
                            'function': func.__name__,
                            'thread': thread_name,
                            'start_time': start_time,
                            'execution_time': execution_time,
                            'status': 'failed',
                            'error': str(e)
                        })
                    
                    self.logger.error(f"Task {task_id} failed after {execution_time:.3f}s: {e}")
                    raise
                finally:
                    # 清理活动任务记录
                    with self._lock:
                        self._active_tasks.pop(task_id, None)
            
            # 提交任务
            future = self._executor.submit(wrapped_func, *args, **kwargs)
            
            # 记录活动任务
            with self._lock:
                self._active_tasks[task_id] = future
            
            self._stats['tasks_submitted'] += 1
            return future
            
        except Exception as e:
            self._stats['submission_errors'] += 1
            self.logger.error(f"Failed to submit task: {e}")
            raise
    
    def submit_batch(self, tasks: List[tuple]) -> List[Future]:
        """
        批量提交任务
        
        Args:
            tasks: 任务列表，每个元素为(func, args, kwargs)元组
            
        Returns:
            Future对象列表
        """
        futures = []
        
        for task in tasks:
            if len(task) == 1:
                func = task[0]
                args, kwargs = (), {}
            elif len(task) == 2:
                func, args = task
                kwargs = {}
            elif len(task) == 3:
                func, args, kwargs = task
            else:
                raise ValueError("Task tuple must be (func,), (func, args), or (func, args, kwargs)")
            
            future = self.submit_task(func, *args, **kwargs)
            futures.append(future)
        
        self.logger.info(f"Submitted batch of {len(tasks)} tasks")
        return futures
    
    def wait_for_completion(self, futures: List[Future], timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        等待任务完成
        
        Args:
            futures: Future对象列表
            timeout: 超时时间（秒）
            
        Returns:
            完成结果统计
        """
        start_time = time.time()
        completed = []
        failed = []
        timed_out = []
        
        try:
            for future in as_completed(futures, timeout=timeout):
                try:
                    result = future.result()
                    completed.append(result)
                except Exception as e:
                    failed.append(str(e))
        
        except TimeoutError:
            # 检查哪些任务超时
            for future in futures:
                if future.done():
                    try:
                        result = future.result()
                        completed.append(result)
                    except Exception as e:
                        failed.append(str(e))
                else:
                    timed_out.append(future)
        
        completion_time = time.time() - start_time
        
        result = {
            'total_tasks': len(futures),
            'completed': len(completed),
            'failed': len(failed),
            'timed_out': len(timed_out),
            'completion_time': completion_time,
            'completed_results': completed,
            'failed_errors': failed
        }
        
        self.logger.info(f"Batch completion: {result['completed']}/{result['total_tasks']} "
                        f"completed in {completion_time:.3f}s")
        
        return result
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """
        获取线程池统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            # 计算平均执行时间
            avg_execution_time = 0.0
            if self._stats['tasks_completed'] > 0:
                avg_execution_time = (self._stats['total_execution_time'] / 
                                    self._stats['tasks_completed'])
            
            stats = {
                'max_workers': self.max_workers,
                'active_tasks': len(self._active_tasks),
                'tasks_submitted': self._stats['tasks_submitted'],
                'tasks_started': self._stats['tasks_started'],
                'tasks_completed': self._stats['tasks_completed'],
                'tasks_failed': self._stats['tasks_failed'],
                'submission_errors': self._stats['submission_errors'],
                'total_execution_time': self._stats['total_execution_time'],
                'average_execution_time': avg_execution_time,
                'task_history_size': len(self._task_history),
                'is_shutdown': self._shutdown
            }
            
            # 添加成功率
            total_finished = stats['tasks_completed'] + stats['tasks_failed']
            if total_finished > 0:
                stats['success_rate'] = stats['tasks_completed'] / total_finished
            else:
                stats['success_rate'] = 0.0
            
            return stats
    
    def get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取任务历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            任务历史记录列表
        """
        with self._lock:
            return self._task_history[-limit:] if self._task_history else []
    
    def cancel_all_tasks(self) -> int:
        """
        取消所有活动任务
        
        Returns:
            取消的任务数量
        """
        cancelled_count = 0
        
        with self._lock:
            active_tasks = list(self._active_tasks.values())
        
        for future in active_tasks:
            if future.cancel():
                cancelled_count += 1
        
        self.logger.warning(f"Cancelled {cancelled_count} active tasks")
        return cancelled_count
    
    def shutdown(self, wait: bool = True, timeout: Optional[float] = None):
        """
        关闭线程池
        
        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
        """
        if self._shutdown:
            return
        
        self._shutdown = True
        
        try:
            if wait:
                self.logger.info("Shutting down thread pool, waiting for tasks to complete...")
                self._executor.shutdown(wait=True, timeout=timeout)
            else:
                self.logger.info("Shutting down thread pool immediately...")
                # 取消所有任务
                self.cancel_all_tasks()
                self._executor.shutdown(wait=False)
            
            self.logger.info("ThreadPoolManager shut down successfully")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()
