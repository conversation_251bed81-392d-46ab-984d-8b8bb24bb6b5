# -*- coding: utf-8 -*-
"""
核心统一状态管理器
提供核心的状态存储和管理功能
"""

import threading
import time
import logging
from typing import Dict, Any, Optional, Set
from datetime import datetime

from .state_synchronizer import StateSynchronizer
from .performance_monitor import PerformanceMonitor
from .error_handler import <PERSON>rror<PERSON>and<PERSON>
from .state_metadata import StateMetadata, StateScope, DataType, infer_data_type


class UnifiedStateManager:
    """核心统一状态管理器 - 负责实际的状态存储和管理"""
    
    def __init__(self):
        """初始化核心状态管理器"""
        # 状态存储
        self._states: Dict[str, Any] = {}
        self._metadata: Dict[str, StateMetadata] = {}
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 核心组件
        self.state_synchronizer = StateSynchronizer()
        self.performance_monitor = PerformanceMonitor()
        self.error_handler = ErrorHandler()
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化标记
        self._initialized_keys: Set[str] = set()
        
        self.logger.info("Core UnifiedStateManager initialized")
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """
        获取状态值
        
        Args:
            key: 状态键
            default: 默认值
            
        Returns:
            状态值或默认值
        """
        start_time = time.time()
        
        try:
            with self._lock:
                value = self._states.get(key, default)
                
                # 更新元数据
                if key in self._metadata:
                    self._metadata[key].last_accessed = datetime.now()
                    self._metadata[key].access_count += 1
                
                self.logger.debug(f"State retrieved: {key} = {type(value).__name__}")
                return value
                
        except Exception as e:
            self.error_handler.handle_error(e, custom_message=f"Failed to get state: {key}")
            return default
        finally:
            # 性能监控
            end_time = time.time()
            self.performance_monitor.record_metric('get_state_duration', end_time - start_time)
    
    def set_state(self, key: str, value: Any, is_initialization: bool = False) -> bool:
        """
        设置状态值
        
        Args:
            key: 状态键
            value: 状态值
            is_initialization: 是否为初始化设置
            
        Returns:
            是否设置成功
        """
        start_time = time.time()
        
        try:
            with self._lock:
                old_value = self._states.get(key)
                self._states[key] = value
                
                # 更新或创建元数据
                if key not in self._metadata:
                    self._metadata[key] = StateMetadata(
                        key=key,
                        scope=StateScope.GLOBAL,  # 默认为全局作用域
                        data_type=infer_data_type(value),
                        created_at=datetime.now(),
                        last_modified=datetime.now(),
                        last_accessed=datetime.now(),
                        access_count=1
                    )
                else:
                    metadata = self._metadata[key]
                    metadata.last_modified = datetime.now()
                    # Note: StateMetadata doesn't have modification_count or previous_value
                    # We'll just update the basic fields
                
                # 标记为已初始化
                if is_initialization:
                    self._initialized_keys.add(key)
                
                # 状态同步（如果不是初始化）
                # Note: StateSynchronizer doesn't have notify_state_change method
                # We'll skip this for now or implement a simple notification
                
                self.logger.debug(f"State set: {key} = {type(value).__name__}")
                return True
                
        except Exception as e:
            self.error_handler.handle_error(e, custom_message=f"Failed to set state: {key}")
            return False
        finally:
            # 性能监控
            end_time = time.time()
            self.performance_monitor.record_metric('set_state_duration', end_time - start_time)
    
    def silent_initialize_state(self, key: str, value: Any) -> bool:
        """
        静默初始化状态（不触发变化检测和重新运行）
        
        Args:
            key: 状态键
            value: 状态值
            
        Returns:
            是否设置成功
        """
        return self.set_state(key, value, is_initialization=True)
    
    def delete_state(self, key: str) -> bool:
        """
        删除状态
        
        Args:
            key: 状态键
            
        Returns:
            是否删除成功
        """
        try:
            with self._lock:
                if key in self._states:
                    old_value = self._states.pop(key)

                    # 删除元数据
                    if key in self._metadata:
                        del self._metadata[key]

                    # 从初始化集合中移除
                    self._initialized_keys.discard(key)

                    # Note: StateSynchronizer doesn't have notify_state_change method
                    # We'll skip this for now

                    self.logger.debug(f"State deleted: {key}")
                    return True
                else:
                    # 只对非临时状态键记录警告，避免日志污染
                    if not self._is_temporary_key(key):
                        self.logger.debug(f"Attempted to delete non-existent state: {key}")
                    return False
                    
        except Exception as e:
            self.error_handler.handle_error(e, custom_message=f"Failed to delete state: {key}")
            return False
    
    def clear_all_states(self) -> bool:
        """
        清空所有状态
        
        Returns:
            是否清空成功
        """
        try:
            with self._lock:
                # 备份当前状态用于通知
                old_states = self._states.copy()
                state_count = len(old_states)

                # 清空所有状态
                self._states.clear()
                self._metadata.clear()
                self._initialized_keys.clear()

                # Note: StateSynchronizer doesn't have notify_state_change method
                # We'll skip this for now

                self.logger.info(f"All states cleared ({state_count} states)")
                return True
                
        except Exception as e:
            self.error_handler.handle_error(e, custom_message="Failed to clear all states")
            return False
    
    def get_all_keys(self) -> list:
        """获取所有状态键"""
        with self._lock:
            return list(self._states.keys())
    
    def get_state_count(self) -> int:
        """获取状态数量"""
        with self._lock:
            return len(self._states)
    
    def is_initialized(self, key: str) -> bool:
        """检查状态是否已初始化"""
        with self._lock:
            return key in self._initialized_keys
    
    def get_metadata(self, key: str) -> Optional[StateMetadata]:
        """获取状态元数据"""
        with self._lock:
            return self._metadata.get(key)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.performance_monitor.get_performance_summary()
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        with self._lock:
            return {
                'status': 'healthy',
                'state_count': len(self._states),
                'metadata_count': len(self._metadata),
                'initialized_count': len(self._initialized_keys),
                'timestamp': datetime.now().isoformat()
            }

    def _is_temporary_key(self, key: str) -> bool:
        """判断是否是临时状态键，避免不必要的警告"""
        temporary_patterns = [
            'ui.button_state_cache',
            'ui.button_state_time',
            'ui.navigation_cache',
            'navigation.cache',
            'temp_',
            '_cache',
            '_temp',
            'loading_',
            'transition_'
        ]

        key_str = str(key).lower()
        return any(pattern in key_str for pattern in temporary_patterns)
