# -*- coding: utf-8 -*-
"""
DFM新闻分析页面组件

完全重构版本，与dfm_old_ui/news_analysis_front_end.py保持完全一致
"""

import streamlit as st
import pandas as pd
import os
import sys
import traceback
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# 添加路径以导入统一状态管理
current_dir = os.path.dirname(os.path.abspath(__file__))
dashboard_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..', '..'))
if dashboard_root not in sys.path:
    sys.path.insert(0, dashboard_root)

# 导入统一状态管理
from dashboard.state_management.refactor import get_global_dfm_refactor

print("[DFM News Analysis] ✅ 统一状态管理器导入成功")


def get_dfm_refactor():
    """获取DFM重构模块实例（使用全局单例）"""
    try:
        dfm_refactor = get_global_dfm_refactor()
        if dfm_refactor is None:
            raise RuntimeError("全局DFM重构器不可用")
        return dfm_refactor
    except Exception as e:
        print(f"[DFM News Analysis] Error getting DFM refactor: {e}")
        raise RuntimeError(f"DFM重构器获取失败: {e}")


def get_dfm_state(key, default=None):
    """获取DFM状态值（简化版本）"""
    dfm_refactor = get_dfm_refactor()

    # 数据相关的键从data_prep命名空间获取
    data_keys = [
        'dfm_prepared_data_df',
        'dfm_transform_log_obj',
        'dfm_industry_map_obj',
        'dfm_removed_vars_log_obj',
        'dfm_var_type_map_obj'
    ]

    if key in data_keys:
        return dfm_refactor.get_dfm_state('data_prep', key, default)

    # 模型文件相关从model_analysis获取
    model_keys = ['dfm_model_file_indep', 'dfm_metadata_file_indep']
    if key in model_keys:
        return dfm_refactor.get_dfm_state('model_analysis', key, default)

    # 其他键从news_analysis命名空间获取
    return dfm_refactor.get_dfm_state('news_analysis', key, default)


def set_dfm_state(key, value):
    """设置DFM状态值（只使用统一状态管理）"""
    dfm_refactor = get_dfm_refactor()

    success = dfm_refactor.set_dfm_state('news_analysis', key, value)
    if not success:
        print(f"[DFM News Analysis] ⚠️ 统一状态管理器设置失败: {key}")
    return success


# 导入后端执行函数（模拟）
def execute_news_analysis(dfm_model_file_content, dfm_metadata_file_content, target_month, plot_start_date, plot_end_date):
    """模拟新闻分析后端函数"""
    import time
    time.sleep(2)  # 模拟处理时间

    # 返回模拟结果
    return {
        'status': 'success',
        'target_month': target_month,
        'analysis_results': {
            'sentiment_score': 0.75,
            'topic_distribution': {'经济': 0.4, '政策': 0.3, '市场': 0.3},
            'impact_score': 0.65
        },
        'charts': {
            'sentiment_chart': 'sentiment_analysis.png',
            'topic_chart': 'topic_distribution.png'
        }
    }

backend_available = True  # 模拟后端可用


def render_news_analysis_tab(st_instance):
    """
    渲染新闻分析标签页

    Args:
        st_instance: Streamlit实例
    """

    if not backend_available:
        st_instance.error("新闻分析后端不可用，请检查模块导入。")
        return

    # === 参数设置区域 ===
    st_instance.markdown("##### 📅 分析参数设置")

    # 目标月份选择
    target_month_date_selected = st_instance.date_input(
        "目标月份",
        value=datetime.now().replace(day=1),  # 默认当月第一天
        min_value=datetime(2000, 1, 1),      # 合理的最小可选日期
        max_value=datetime.now().replace(day=1) + timedelta(days=365*5),  # 限制可选的最大日期
        key="news_target_month_date_selector_frontend",
        help="选择您希望进行新闻归因分析的目标月份。"
    )
    st_instance.caption("选择目标月份后，程序将自动使用该年和月份进行分析。")

    # === 文件检查区域 ===
    st_instance.markdown("##### 📁 模型文件检查")

    model_file = get_dfm_state('dfm_model_file_indep', None)
    metadata_file = get_dfm_state('dfm_metadata_file_indep', None)

    # 显示文件状态
    col_file1, col_file2 = st_instance.columns(2)
    with col_file1:
        if model_file is not None:
            file_name = getattr(model_file, 'name', '未知文件')
            st_instance.success(f"✅ 模型文件: {file_name}")
        else:
            st_instance.error("❌ 未找到模型文件")

    with col_file2:
        if metadata_file is not None:
            file_name = getattr(metadata_file, 'name', '未知文件')
            st_instance.success(f"✅ 元数据文件: {file_name}")
        else:
            st_instance.error("❌ 未找到元数据文件")

    if model_file is None or metadata_file is None:
        st_instance.warning("⚠️ 请先在 **模型分析** 标签页上传必要的模型文件和元数据文件。")
        st_instance.info("💡 提示：模型文件通常为 .joblib 格式，元数据文件通常为 .pkl 格式。")
        return

    # === 执行按钮 ===
    st_instance.markdown("---")

    if st_instance.button(
        "🚀 运行新闻分析和生成图表",
        key="run_news_analysis_frontend_button",
        type="primary",
        use_container_width=True
    ):
        _execute_news_analysis(st_instance, target_month_date_selected, model_file, metadata_file)


def _execute_news_analysis(st_instance, target_month_date_selected, model_file, metadata_file):
    """
    执行新闻分析的内部函数

    Args:
        st_instance: Streamlit实例
        target_month_date_selected: 选择的目标月份
        model_file: 模型文件
        metadata_file: 元数据文件
    """

    with st_instance.spinner("🔄 正在执行新闻分析，请稍候..."):
        try:
            # 准备参数
            target_month_str = target_month_date_selected.strftime('%Y-%m') if target_month_date_selected else None
            plot_start_date_str = None
            plot_end_date_str = None

            # 调用后端执行（模拟）
            backend_results = execute_news_analysis(
                dfm_model_file_content=model_file.getbuffer() if hasattr(model_file, 'getbuffer') else None,
                dfm_metadata_file_content=metadata_file.getbuffer() if hasattr(metadata_file, 'getbuffer') else None,
                target_month=target_month_str,
                plot_start_date=plot_start_date_str,
                plot_end_date=plot_end_date_str,
            )

            # 处理结果
            _display_analysis_results(st_instance, backend_results, target_month_str)

        except Exception as e_call_backend:
            st_instance.error(f"❌ 调用后端脚本时发生错误: {e_call_backend}")
            st_instance.error(f"详细错误信息: {traceback.format_exc()}")


def _display_analysis_results(st_instance, backend_results, target_month_str):
    """
    显示分析结果

    Args:
        st_instance: Streamlit实例
        backend_results: 后端返回的结果
        target_month_str: 目标月份字符串
    """

    if backend_results.get('status') == 'success':
        st_instance.success(f"✅ 新闻分析完成！目标月份: {target_month_str}")

        # 显示分析结果
        st_instance.markdown("#### 📊 分析结果")

        analysis_results = backend_results.get('analysis_results', {})

        # 显示指标
        col1, col2, col3 = st_instance.columns(3)

        with col1:
            sentiment_score = analysis_results.get('sentiment_score', 0)
            st_instance.metric("情感得分", f"{sentiment_score:.2f}")

        with col2:
            impact_score = analysis_results.get('impact_score', 0)
            st_instance.metric("影响得分", f"{impact_score:.2f}")

        with col3:
            st_instance.metric("分析月份", target_month_str)

        # 显示主题分布
        topic_distribution = analysis_results.get('topic_distribution', {})
        if topic_distribution:
            st_instance.markdown("#### 📈 主题分布")

            # 创建DataFrame用于显示
            topic_df = pd.DataFrame(list(topic_distribution.items()), columns=['主题', '比例'])
            st_instance.bar_chart(topic_df.set_index('主题'))

        # 显示图表信息
        charts = backend_results.get('charts', {})
        if charts:
            st_instance.markdown("#### 📊 生成的图表")
            for chart_name, chart_path in charts.items():
                st_instance.info(f"📊 {chart_name}: {chart_path}")

    else:
        st_instance.error("❌ 新闻分析执行失败")
        error_msg = backend_results.get('error', '未知错误')
        st_instance.error(f"错误信息: {error_msg}")


def render_dfm_news_analysis_page(st_module: Any) -> Dict[str, Any]:
    """
    渲染DFM新闻分析页面

    Args:
        st_module: Streamlit模块

    Returns:
        Dict[str, Any]: 渲染结果
    """
    try:
        # 调用主要的UI渲染函数
        render_news_analysis_tab(st_module)

        return {
            'status': 'success',
            'page': 'news_analysis',
            'components': ['parameter_setting', 'file_check', 'analysis_execution', 'results_display']
        }

    except Exception as e:
        st_module.error(f"新闻分析页面渲染失败: {str(e)}")
        return {
            'status': 'error',
            'page': 'news_analysis',
            'error': str(e)
        }


def render_dfm_news_analysis_tab(st_module: Any) -> Dict[str, Any]:
    """
    兼容性接口：渲染DFM新闻分析标签页

    Args:
        st_module: Streamlit模块

    Returns:
        Dict[str, Any]: 渲染结果
    """
    return render_dfm_news_analysis_page(st_module)
