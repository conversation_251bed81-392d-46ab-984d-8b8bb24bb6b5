#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据追加与合并组件
提供数据追加和合并功能的UI组件
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging

from ..base import UIComponent

logger = logging.getLogger(__name__)

class DataAppendMergeComponent(UIComponent):
    """数据追加与合并组件"""
    
    def __init__(self):
        self.component_id = "data_append_merge"
        self.component_name = "数据追加与合并"

    def render(self, st_obj, **kwargs) -> None:
        """渲染组件"""
        st_obj.markdown("### 数据追加与合并")
        st_obj.info("📋 数据追加与合并功能正在优化中，即将推出更强大的功能")

    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return [
            f"{self.component_id}_operation_type",
            f"{self.component_id}_append_selection",
            f"{self.component_id}_append_method",
            f"{self.component_id}_handle_duplicates",
            f"{self.component_id}_left_dataset",
            f"{self.component_id}_right_dataset",
            f"{self.component_id}_merge_how",
            f"{self.component_id}_left_key",
            f"{self.component_id}_right_key"
        ]
    
    def render_input_section(self, st_obj, data_list: List[pd.DataFrame] = None, data_names: List[str] = None) -> Dict[str, Any]:
        """渲染数据追加与合并输入界面"""
        st_obj.markdown("#### 数据追加与合并配置")
        
        if not data_list:
            st_obj.warning("请先上传数据文件")
            return {'error': '没有可用数据'}
        
        # 操作类型选择
        operation_type = st_obj.radio(
            "选择操作类型",
            ["数据追加", "数据合并"],
            key=f"{self.component_id}_operation_type"
        )
        
        result = {
            'operation_type': operation_type,
            'data_list': data_list,
            'data_names': data_names or [f"数据{i+1}" for i in range(len(data_list))]
        }
        
        if operation_type == "数据追加":
            append_result = self.render_append_options(st_obj, data_list, result['data_names'])
            result.update(append_result)
        else:
            merge_result = self.render_merge_options(st_obj, data_list, result['data_names'])
            result.update(merge_result)
        
        return result
    
    def render_append_options(self, st_obj, data_list: List[pd.DataFrame], data_names: List[str]) -> Dict[str, Any]:
        """渲染数据追加选项"""
        st_obj.markdown("##### 数据追加设置")
        
        # 选择要追加的数据集
        selected_indices = st_obj.multiselect(
            "选择要追加的数据集（按选择顺序追加）",
            range(len(data_list)),
            format_func=lambda x: data_names[x],
            key=f"{self.component_id}_append_selection"
        )
        
        if len(selected_indices) < 2:
            st_obj.warning("请至少选择两个数据集进行追加")
            return {'selected_indices': selected_indices}
        
        # 追加方式
        append_method = st_obj.selectbox(
            "追加方式",
            ["按行追加", "按列追加"],
            key=f"{self.component_id}_append_method"
        )
        
        # 处理重复列
        handle_duplicates = st_obj.checkbox(
            "自动处理重复列名",
            value=True,
            key=f"{self.component_id}_handle_duplicates"
        )
        
        return {
            'selected_indices': selected_indices,
            'append_method': append_method,
            'handle_duplicates': handle_duplicates
        }
    
    def render_merge_options(self, st_obj, data_list: List[pd.DataFrame], data_names: List[str]) -> Dict[str, Any]:
        """渲染数据合并选项"""
        st_obj.markdown("##### 数据合并设置")
        
        if len(data_list) < 2:
            st_obj.warning("需要至少两个数据集进行合并")
            return {'error': '数据集不足'}
        
        # 选择主数据集
        left_index = st_obj.selectbox(
            "选择主数据集（左表）",
            range(len(data_list)),
            format_func=lambda x: data_names[x],
            key=f"{self.component_id}_left_dataset"
        )
        
        # 选择合并数据集
        right_index = st_obj.selectbox(
            "选择合并数据集（右表）",
            range(len(data_list)),
            format_func=lambda x: data_names[x],
            key=f"{self.component_id}_right_dataset"
        )
        
        if left_index == right_index:
            st_obj.warning("请选择不同的数据集进行合并")
            return {'error': '数据集重复'}
        
        # 合并方式
        merge_how = st_obj.selectbox(
            "合并方式",
            ["inner", "outer", "left", "right"],
            key=f"{self.component_id}_merge_how"
        )
        
        # 合并键
        left_data = data_list[left_index]
        right_data = data_list[right_index]
        
        left_columns = left_data.columns.tolist()
        right_columns = right_data.columns.tolist()
        
        left_key = st_obj.selectbox(
            "选择主数据集的合并键",
            left_columns,
            key=f"{self.component_id}_left_key"
        )
        
        right_key = st_obj.selectbox(
            "选择合并数据集的合并键",
            right_columns,
            key=f"{self.component_id}_right_key"
        )
        
        return {
            'left_index': left_index,
            'right_index': right_index,
            'merge_how': merge_how,
            'left_key': left_key,
            'right_key': right_key
        }
    
    def perform_append(self, data_list: List[pd.DataFrame], selected_indices: List[int], 
                      append_method: str = "按行追加", handle_duplicates: bool = True) -> Optional[pd.DataFrame]:
        """执行数据追加"""
        try:
            selected_data = [data_list[i] for i in selected_indices]
            
            if append_method == "按行追加":
                # 按行追加（纵向连接）
                result = pd.concat(selected_data, axis=0, ignore_index=True)
            else:
                # 按列追加（横向连接）
                result = pd.concat(selected_data, axis=1)
                
                if handle_duplicates:
                    # 处理重复列名
                    result.columns = pd.io.common.dedup_names(result.columns, is_potential_multiindex=False)
            
            logger.info(f"数据追加成功，结果形状: {result.shape}")
            return result
            
        except Exception as e:
            logger.error(f"数据追加失败: {e}")
            return None
    
    def perform_merge(self, left_data: pd.DataFrame, right_data: pd.DataFrame,
                     left_key: str, right_key: str, how: str = "inner") -> Optional[pd.DataFrame]:
        """执行数据合并"""
        try:
            result = pd.merge(
                left_data, 
                right_data, 
                left_on=left_key, 
                right_on=right_key, 
                how=how
            )
            
            logger.info(f"数据合并成功，结果形状: {result.shape}")
            return result
            
        except Exception as e:
            logger.error(f"数据合并失败: {e}")
            return None


__all__ = ['DataAppendMergeComponent']
