# -*- coding: utf-8 -*-
"""
统一状态管理接口
提供统一的状态管理入口，封装底层复杂性，确保状态管理的一致性
"""

import logging
import threading
import time
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime

# 导入现有的统一状态管理器
from .unified_state_manager import UnifiedStateManager


class StateManagerInterface:
    """
    统一状态管理接口
    
    这个类作为所有状态管理操作的统一入口点，封装了UnifiedStateManager
    和Streamlit session_state的复杂性，提供简洁、一致的API。
    
    特性：
    - 统一的状态管理API
    - 自动状态迁移和同步
    - 向后兼容性支持
    - 性能优化和缓存
    - 错误处理和降级
    """
    
    _instance: Optional['StateManagerInterface'] = None
    _lock = threading.RLock()
    
    def __new__(cls, *args, **kwargs) -> 'StateManagerInterface':
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """
        初始化统一状态管理接口

        只使用统一状态管理器，不再支持降级模式
        """
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return

        self.logger = logging.getLogger(__name__)
        
        # 初始化统一状态管理器
        self.unified_manager = self._get_unified_manager()
        
        # 状态键映射和规范化
        self.key_mappings = {}
        self.key_prefixes = {
            'dashboard': 'dashboard.',
            'preview': 'preview.',
            'dfm': 'dfm.',
            'tools': 'tools.',
            'ui': 'ui.',
            'navigation': 'navigation.'
        }
        
        # 性能监控
        self.operation_stats = {
            'get_operations': 0,
            'set_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'fallback_uses': 0
        }
        
        # 迁移状态跟踪
        self.migrated_keys = set()
        self.migration_errors = []
        
        self._initialized = True
        self.logger.info("StateManagerInterface initialized successfully")
    
    def _get_unified_manager(self) -> Optional[UnifiedStateManager]:
        """获取统一状态管理器实例"""
        try:
            from . import get_unified_manager
            manager = get_unified_manager()
            if manager and manager.isInitialized():
                return manager
            else:
                self.logger.warning("UnifiedStateManager not available or not initialized")
                return None
        except Exception as e:
            self.logger.error(f"Failed to get unified manager: {e}")
            return None
    
    # 移除session_state降级支持
    
    def _normalize_key(self, key: str) -> str:
        """标准化状态键名"""
        # 如果键已经有正确的前缀，直接返回
        for prefix in self.key_prefixes.values():
            if key.startswith(prefix):
                return key
        
        # 检查是否有映射
        if key in self.key_mappings:
            return self.key_mappings[key]
        
        # 如果没有前缀，尝试推断模块
        if '.' not in key:
            # 简单的启发式推断
            if any(word in key.lower() for word in ['dashboard', 'main', 'sidebar', 'render']):
                return f"dashboard.{key}"
            elif any(word in key.lower() for word in ['preview', 'daily', 'monthly', 'weekly', 'industrial']):
                return f"preview.{key}"
            elif any(word in key.lower() for word in ['dfm', 'model', 'factor']):
                return f"dfm.{key}"
            elif any(word in key.lower() for word in ['tools', 'compute', 'process']):
                return f"tools.{key}"
            elif any(word in key.lower() for word in ['ui', 'component', 'widget']):
                return f"ui.{key}"
            elif any(word in key.lower() for word in ['nav', 'navigation', 'route']):
                return f"navigation.{key}"
        
        return key
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """
        获取状态值
        
        Args:
            key: 状态键名
            default: 默认值
            
        Returns:
            状态值
        """
        self.operation_stats['get_operations'] += 1
        normalized_key = self._normalize_key(key)
        
        try:
            # 只从统一状态管理器获取
            if self.unified_manager:
                value = self.unified_manager.get_state(normalized_key, default)
                if value is not None:
                    self.operation_stats['cache_hits'] += 1
                    return value

            # 如果统一管理器不可用，记录错误并返回默认值
            self.logger.error(f"Unified manager not available for key: {key}")
            self.operation_stats['cache_misses'] += 1
            return default
            
        except Exception as e:
            self.logger.error(f"Failed to get state for key {key}: {e}")
            # 降级到session_state
            if self.enable_session_state_fallback:
                session_state = self._get_session_state()
                if session_state:
                    self.operation_stats['fallback_uses'] += 1
                    return session_state.get(key, default)
            return default
    
    def set_state(self, key: str, value: Any, is_initialization: bool = False) -> bool:
        """
        设置状态值
        
        Args:
            key: 状态键名
            value: 状态值
            is_initialization: 是否为初始化设置
            
        Returns:
            是否设置成功
        """
        self.operation_stats['set_operations'] += 1
        normalized_key = self._normalize_key(key)
        
        try:
            # 只设置到统一状态管理器
            if self.unified_manager:
                return self.unified_manager.set_state(normalized_key, value, is_initialization)

            # 如果统一管理器不可用，记录错误并返回False
            self.logger.error(f"Unified manager not available for setting key: {key}")
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to set state for key {key}: {e}")
            # 降级到session_state
            if self.enable_session_state_fallback:
                session_state = self._get_session_state()
                if session_state:
                    session_state[key] = value
                    self.operation_stats['fallback_uses'] += 1
                    return True
            return False
    
    def clear_state(self, key: str = None) -> bool:
        """
        清除状态
        
        Args:
            key: 状态键名，如果为None则清除所有状态
            
        Returns:
            是否清除成功
        """
        try:
            success = False
            
            if key:
                normalized_key = self._normalize_key(key)
                
                # 从统一状态管理器清除
                if self.unified_manager:
                    success = self.unified_manager.clear_state(normalized_key)
                
                # 从session_state清除
                if self.migration_mode and self.enable_session_state_fallback:
                    session_state = self._get_session_state()
                    if session_state and key in session_state:
                        del session_state[key]
                        success = True
            else:
                # 清除所有状态
                if self.unified_manager:
                    success = self.unified_manager.clear_state()
                
                if self.migration_mode and self.enable_session_state_fallback:
                    session_state = self._get_session_state()
                    if session_state:
                        session_state.clear()
                        success = True
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to clear state for key {key}: {e}")
            return False
    
    def _migrate_key(self, old_key: str, new_key: str, value: Any) -> bool:
        """
        迁移状态键
        
        Args:
            old_key: 原始键名
            new_key: 新键名
            value: 状态值
            
        Returns:
            是否迁移成功
        """
        try:
            if old_key in self.migrated_keys:
                return True
                
            if self.unified_manager:
                success = self.unified_manager.set_state(new_key, value)
                if success:
                    self.migrated_keys.add(old_key)
                    self.key_mappings[old_key] = new_key
                    self.logger.debug(f"Migrated key: {old_key} -> {new_key}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to migrate key {old_key}: {e}")
            self.migration_errors.append((old_key, str(e)))
            return False
    
    def get_all_keys(self) -> List[str]:
        """获取所有状态键"""
        try:
            # 只从统一状态管理器获取
            if self.unified_manager:
                return self.unified_manager.get_all_keys()
            else:
                self.logger.error("Unified manager not available for getting all keys")
                return []
            
        except Exception as e:
            self.logger.error(f"Failed to get all keys: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'operation_stats': self.operation_stats.copy(),
            'migration_stats': {
                'migrated_keys_count': len(self.migrated_keys),
                'migration_errors_count': len(self.migration_errors)
            },
            'system_stats': {
                'unified_manager_available': self.unified_manager is not None,
                'total_keys': len(self.get_all_keys())
            },
            'timestamp': datetime.now()
        }
        
        # 添加统一状态管理器的统计信息
        if self.unified_manager:
            try:
                unified_stats = self.unified_manager.get_system_statistics()
                stats['unified_manager_stats'] = unified_stats
            except Exception as e:
                self.logger.error(f"Failed to get unified manager stats: {e}")
        
        return stats
    
    # 移除迁移模式相关方法，不再支持迁移模式


# 全局实例
_state_interface = None

def get_state_interface() -> StateManagerInterface:
    """获取状态管理接口实例（单例）"""
    global _state_interface
    if _state_interface is None:
        _state_interface = StateManagerInterface()
    return _state_interface


# 便捷函数
def get_state(key: str, default: Any = None) -> Any:
    """便捷函数：获取状态"""
    return get_state_interface().get_state(key, default)


def set_state(key: str, value: Any, is_initialization: bool = False) -> bool:
    """便捷函数：设置状态"""
    return get_state_interface().set_state(key, value, is_initialization)


def clear_state(key: str = None) -> bool:
    """便捷函数：清除状态"""
    return get_state_interface().clear_state(key)


__all__ = [
    'StateManagerInterface',
    'get_state_interface',
    'get_state',
    'set_state',
    'clear_state'
]
