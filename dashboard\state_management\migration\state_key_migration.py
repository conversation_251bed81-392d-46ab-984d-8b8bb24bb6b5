# -*- coding: utf-8 -*-
"""
状态键迁移工具
用于将现有状态数据从旧键结构迁移到新键结构
"""

import logging
from typing import Dict, Any, List, Tuple
from datetime import datetime
import json
import pickle
import os

# 导入统一状态管理器
from dashboard.state_management import get_unified_manager


class StateKeyMigration:
    """状态键迁移工具类"""
    
    def __init__(self):
        self.unified_manager = get_unified_manager()
        self.logger = logging.getLogger(__name__)
        
        # 定义旧键到新键的映射
        self.migration_mapping = {
            # 时间序列预处理模块迁移
            'tools.ts_pretreat.uploaded_file_name': 'tools.pretreat.clean.uploaded_file_name',
            'tools.ts_pretreat.uploaded_file_content': 'tools.pretreat.clean.uploaded_file_content',
            'tools.ts_pretreat.data_raw': 'tools.pretreat.clean.data_raw',
            'tools.ts_pretreat.data_processed': 'tools.pretreat.clean.data_processed',
            'tools.ts_pretreat.data_final': 'tools.pretreat.clean.data_final',
            'tools.ts_pretreat.error_msg': 'tools.pretreat.clean.error_msg',
            'tools.ts_pretreat.processing_applied': 'tools.pretreat.clean.processing_applied',
            'tools.ts_pretreat.staged_data': 'tools.pretreat.clean.staged_data',
            
            # 时间序列属性分析模块迁移
            'tools.ts_property.input_data': 'tools.property.general.input_data',
            'tools.ts_property.analysis_results': 'tools.property.general.analysis_results',
            'tools.ts_property.selected_columns': 'tools.property.general.selected_columns',
            'tools.ts_property.parameters': 'tools.property.general.parameters',
            
            # 数据比较模块迁移
            'tools.data_comparison.m1_uploader_key_suffix': 'tools.comparison.upload.m1_uploader_key_suffix',
            'tools.data_comparison.m2_comparison_results': 'tools.comparison.results.m2_comparison_results',
            'tools.data_comparison.m3_comparison_results': 'tools.comparison.results.m3_comparison_results',
        }
        
        # 备份目录
        self.backup_dir = "storage/state_backup"
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def backup_current_state(self) -> str:
        """备份当前状态数据"""
        try:
            backup_filename = f"state_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # 获取所有状态键
            all_keys = self.unified_manager.get_all_keys()
            backup_data = {}
            
            for key in all_keys:
                try:
                    value = self.unified_manager.get_state(key)
                    # 序列化复杂对象
                    if hasattr(value, 'to_dict'):
                        backup_data[key] = value.to_dict()
                    elif hasattr(value, '__dict__'):
                        backup_data[key] = str(value)  # 转换为字符串表示
                    else:
                        backup_data[key] = value
                except Exception as e:
                    self.logger.warning(f"无法备份键 {key}: {e}")
                    backup_data[key] = f"<备份失败: {str(e)}>"
            
            # 保存备份
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"状态备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"状态备份失败: {e}")
            raise
    
    def validate_migration_mapping(self) -> List[str]:
        """验证迁移映射的有效性"""
        issues = []
        
        # 检查是否有重复的目标键
        target_keys = list(self.migration_mapping.values())
        duplicates = [key for key in set(target_keys) if target_keys.count(key) > 1]
        if duplicates:
            issues.append(f"发现重复的目标键: {duplicates}")
        
        # 检查键命名约定
        for old_key, new_key in self.migration_mapping.items():
            if not new_key.startswith('tools.'):
                issues.append(f"新键 {new_key} 不符合命名约定")
            
            # 检查键的层级结构
            parts = new_key.split('.')
            if len(parts) < 4:  # tools.{module}.{submodule}.{key}
                issues.append(f"新键 {new_key} 层级结构不完整")
        
        return issues
    
    def migrate_state_keys(self, dry_run: bool = True) -> Dict[str, Any]:
        """执行状态键迁移"""
        migration_report = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': dry_run,
            'migrated_keys': [],
            'failed_keys': [],
            'skipped_keys': [],
            'total_processed': 0
        }
        
        try:
            # 验证迁移映射
            validation_issues = self.validate_migration_mapping()
            if validation_issues:
                migration_report['validation_issues'] = validation_issues
                if not dry_run:
                    raise ValueError(f"迁移映射验证失败: {validation_issues}")
            
            # 如果不是试运行，先备份
            if not dry_run:
                backup_path = self.backup_current_state()
                migration_report['backup_path'] = backup_path
            
            # 执行迁移
            for old_key, new_key in self.migration_mapping.items():
                migration_report['total_processed'] += 1
                
                try:
                    # 检查旧键是否存在
                    old_value = self.unified_manager.get_state(old_key)
                    if old_value is None:
                        migration_report['skipped_keys'].append({
                            'key': old_key,
                            'reason': '旧键不存在或值为None'
                        })
                        continue
                    
                    # 检查新键是否已存在
                    existing_value = self.unified_manager.get_state(new_key)
                    if existing_value is not None:
                        migration_report['skipped_keys'].append({
                            'key': old_key,
                            'reason': f'新键 {new_key} 已存在'
                        })
                        continue
                    
                    if not dry_run:
                        # 设置新键
                        success = self.unified_manager.set_state(new_key, old_value)
                        if success:
                            # 删除旧键
                            self.unified_manager.clear_state(old_key)
                            migration_report['migrated_keys'].append({
                                'old_key': old_key,
                                'new_key': new_key,
                                'value_type': type(old_value).__name__
                            })
                        else:
                            migration_report['failed_keys'].append({
                                'key': old_key,
                                'reason': '设置新键失败'
                            })
                    else:
                        # 试运行模式
                        migration_report['migrated_keys'].append({
                            'old_key': old_key,
                            'new_key': new_key,
                            'value_type': type(old_value).__name__,
                            'dry_run': True
                        })
                
                except Exception as e:
                    migration_report['failed_keys'].append({
                        'key': old_key,
                        'reason': str(e)
                    })
                    self.logger.error(f"迁移键 {old_key} 失败: {e}")
            
            # 生成迁移报告
            self.logger.info(f"迁移完成 - 成功: {len(migration_report['migrated_keys'])}, "
                           f"失败: {len(migration_report['failed_keys'])}, "
                           f"跳过: {len(migration_report['skipped_keys'])}")
            
            return migration_report
            
        except Exception as e:
            migration_report['error'] = str(e)
            self.logger.error(f"状态键迁移失败: {e}")
            raise
    
    def rollback_migration(self, backup_path: str) -> bool:
        """从备份回滚迁移"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            # 读取备份数据
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # 清除当前状态
            current_keys = self.unified_manager.get_all_keys()
            for key in current_keys:
                if key.startswith('tools.'):
                    self.unified_manager.clear_state(key)
            
            # 恢复备份数据
            for key, value in backup_data.items():
                if not key.startswith('<备份失败:'):
                    self.unified_manager.set_state(key, value)
            
            self.logger.info(f"从备份 {backup_path} 成功回滚")
            return True
            
        except Exception as e:
            self.logger.error(f"回滚失败: {e}")
            return False


def run_migration(dry_run: bool = True) -> Dict[str, Any]:
    """运行状态键迁移的便捷函数"""
    migrator = StateKeyMigration()
    return migrator.migrate_state_keys(dry_run=dry_run)


if __name__ == "__main__":
    # 命令行执行迁移
    import argparse
    
    parser = argparse.ArgumentParser(description='状态键迁移工具')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，不实际修改数据')
    parser.add_argument('--rollback', type=str, help='从指定备份文件回滚')
    
    args = parser.parse_args()
    
    migrator = StateKeyMigration()
    
    if args.rollback:
        success = migrator.rollback_migration(args.rollback)
        print(f"回滚{'成功' if success else '失败'}")
    else:
        report = migrator.migrate_state_keys(dry_run=args.dry_run)
        print(json.dumps(report, ensure_ascii=False, indent=2))
