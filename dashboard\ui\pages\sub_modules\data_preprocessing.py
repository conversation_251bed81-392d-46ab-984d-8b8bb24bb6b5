# -*- coding: utf-8 -*-
"""
数据预处理欢迎页面组件
"""

import streamlit as st
import time
from typing import List
from ...components.base import UIComponent
from ...constants import UIConstants


class DataPreprocessingWelcomePage(UIComponent):
    """数据预处理欢迎页面"""
    
    def __init__(self):
        self.constants = UIConstants
        self.sub_module_config = self.constants.SUB_MODULES["数据预处理"]
    
    def render(self, st_obj, **kwargs) -> None:
        """渲染数据预处理页面"""
        # 处理导航事件
        self._handle_navigation(st_obj)

        # 显示标题和介绍
        self._render_header(st_obj)

        # 显示功能卡片
        self._render_preprocessing_tools(st_obj)
    
    def _render_header(self, st_obj):
        """渲染页面头部"""
        st_obj.markdown(f"""
        <div style="text-align: center; padding: 3rem 0 2rem 0;">
            <div style="font-size: 4em; margin-bottom: 1rem;">{self.sub_module_config['icon']}</div>
            <h1 style="color: #333; margin-bottom: 1rem; font-weight: 700;">数据预处理</h1>
            <p style="font-size: 1.3em; color: #666; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                {self.sub_module_config['description']}
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def _render_preprocessing_tools(self, st_obj):
        """渲染预处理工具选择区域"""
        st_obj.markdown("### 选择预处理工具")

        # 获取功能配置
        functions = self.sub_module_config.get('functions', [])

        if len(functions) >= 4:
            # 创建2x2网格布局
            col1, col2 = st_obj.columns(2)

            with col1:
                # 数据清洗
                if st_obj.button(
                    f"{functions[0]['icon']} {functions[0]['name']}",
                    key="nav_data_cleaning",
                    use_container_width=True,
                    help=functions[0]['description']
                ):
                    self._activate_preprocessing_function(st_obj, 0)

                # 数据追加与合并
                if st_obj.button(
                    f"{functions[2]['icon']} {functions[2]['name']}",
                    key="nav_data_merge",
                    use_container_width=True,
                    help=functions[2]['description']
                ):
                    self._activate_preprocessing_function(st_obj, 2)

            with col2:
                # 变量计算
                if st_obj.button(
                    f"{functions[1]['icon']} {functions[1]['name']}",
                    key="nav_variable_calculation",
                    use_container_width=True,
                    help=functions[1]['description']
                ):
                    self._activate_preprocessing_function(st_obj, 1)

                # 数据比较
                if st_obj.button(
                    f"{functions[3]['icon']} {functions[3]['name']}",
                    key="nav_data_comparison",
                    use_container_width=True,
                    help=functions[3]['description']
                ):
                    self._activate_preprocessing_function(st_obj, 3)

        # 添加使用提示
        st_obj.markdown("""
        <div style="margin-top: 2rem; padding: 1rem; background-color: #f8f9fa; border-radius: 0.5rem;">
            <h4 style="color: #495057; margin-bottom: 0.5rem;">💡 使用提示</h4>
            <ul style="color: #6c757d; margin-bottom: 0;">
                <li><strong>数据清洗</strong>：处理缺失值、异常值，确保数据质量</li>
                <li><strong>变量计算</strong>：创建新变量，进行数据转换和特征工程</li>
                <li><strong>数据追加与合并</strong>：合并多个数据源，追加新的时间序列数据</li>
                <li><strong>数据比较</strong>：比较不同数据集的差异和变化</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    def _activate_preprocessing_function(self, st_obj, function_index):
        """激活指定的预处理功能"""
        try:
            # 获取统一状态管理器
            from dashboard.state_management import get_unified_manager
            unified_manager = get_unified_manager()
            if not unified_manager:
                st_obj.error("统一状态管理器不可用")
                return

            # 根据功能索引设置对应的标签页状态
            # 0: 数据清洗, 1: 变量计算, 2: 数据追加与合并, 3: 数据比较
            tab_names = ["数据清洗", "变量计算", "数据追加与合并", "数据比较"]
            tab_name = tab_names[function_index]

            # 获取数据预处理的状态键配置
            state_keys = self.constants.STATE_KEYS["data_preprocessing"]
            tab_flags = state_keys["tab_flags"]
            timestamps = state_keys["timestamps"]

            # 使用统一状态管理器设置状态
            if unified_manager:
                # 清除所有标志
                for flag_key in tab_flags.values():
                    unified_manager.set_state(f'ui.tabs.{flag_key}', False)

                # 设置当前标签页标志
                tab_keys = list(tab_flags.keys())
                current_tab_key = tab_keys[function_index]
                unified_manager.set_state(f'ui.tabs.{tab_flags[current_tab_key]}', True)
                unified_manager.set_state(f'ui.tabs.{timestamps[current_tab_key]}', time.time())

                # 设置活跃标签页状态
                unified_manager.set_state(f'ui.tabs.{state_keys["active_tab"]}', tab_name)
            else:
                # 降级到session_state
                # 清除所有标志
                for flag_key in tab_flags.values():
                    st_obj.session_state[flag_key] = False

                # 设置当前标签页标志
                tab_keys = list(tab_flags.keys())
                current_tab_key = tab_keys[function_index]
                st_obj.session_state[tab_flags[current_tab_key]] = True
                st_obj.session_state[timestamps[current_tab_key]] = time.time()

                # 设置活跃标签页状态
                st_obj.session_state[state_keys["active_tab"]] = tab_name

            print(f"[DataPreprocessing] 激活预处理功能: {tab_name}, 索引: {function_index}")
            print(f"[DataPreprocessing] 设置标志: {tab_flags[current_tab_key]} = True")

            # 强制重新渲染
            st_obj.rerun()

        except Exception as e:
            st_obj.error(f"激活预处理功能失败: {e}")
            print(f"[DataPreprocessing] 激活预处理功能失败: {e}")
            import traceback
            print(traceback.format_exc())

    def _navigate_to_function(self, st_obj, function_name: str):
        """导航到具体功能（向后兼容方法）"""
        # 将功能名称映射到索引
        function_mapping = {
            '数据清洗': 0,
            '变量计算': 1,
            '数据追加与合并': 2,
            '数据比较': 3
        }

        if function_name in function_mapping:
            self._activate_preprocessing_function(st_obj, function_mapping[function_name])
        else:
            st_obj.error(f"未知的预处理功能: {function_name}")
    
    def _handle_navigation(self, st_obj):
        """处理导航事件"""
        try:
            from dashboard.state_management import get_unified_manager
            state_manager = get_unified_manager()

            if state_manager:
                # 使用统一状态管理器检查导航请求
                function_name = state_manager.get_state('navigation.navigate_to_function')
                if function_name:
                    state_manager.clear_state('navigation.navigate_to_function')
                    self._navigate_to_function(st_obj, function_name)
            else:
                # 降级到session_state
                if 'navigate_to_function' in st_obj.session_state:
                    function_name = st_obj.session_state.pop('navigate_to_function')
                    self._navigate_to_function(st_obj, function_name)
        except ImportError:
            # 降级到session_state
            if 'navigate_to_function' in st_obj.session_state:
                function_name = st_obj.session_state.pop('navigate_to_function')
                self._navigate_to_function(st_obj, function_name)
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return ['navigate_to_function']
