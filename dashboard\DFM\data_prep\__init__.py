# -*- coding: utf-8 -*-
"""
DFM数据准备模块包

这个包提供DFM数据准备的完整功能，包括：
- 数据加载和预处理
- 数据对齐和清理
- 平稳性处理
- 映射管理
"""

__version__ = "1.0.0"
__author__ = "DFM Data Preparation Team"

# 导入主要接口函数
try:
    from .data_preparation import (
        prepare_data,
        prepare_data_from_ui_input,
        load_mappings,
        apply_stationarity_transforms
    )
    
    # 导入模块化组件的主要接口
    from .modules import (
        prepare_data as modules_prepare_data,
        prepare_data_from_ui_input as modules_prepare_data_from_ui_input
    )
    
    __all__ = [
        'prepare_data',
        'prepare_data_from_ui_input', 
        'load_mappings',
        'apply_stationarity_transforms',
        'modules_prepare_data',
        'modules_prepare_data_from_ui_input'
    ]
    
except ImportError as e:
    print(f"[DFM Data Prep] Warning: Some modules could not be imported: {e}")
    __all__ = []

# 提供向后兼容性
def get_data_preparation_module():
    """获取数据准备模块的主要接口"""
    try:
        from . import data_preparation
        return data_preparation
    except ImportError:
        return None

def get_modules_interface():
    """获取模块化数据准备接口"""
    try:
        from . import modules
        return modules
    except ImportError:
        return None
