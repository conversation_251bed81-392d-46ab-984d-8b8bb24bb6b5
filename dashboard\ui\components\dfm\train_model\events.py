"""
DFM训练事件系统

该模块定义了训练过程中的各种事件类型，用于实现事件驱动的状态更新机制。
"""

import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional, List
from dataclasses import dataclass, field


@dataclass
class BaseEvent(ABC):
    """
    事件基类

    所有训练事件的基础类，包含通用的事件属性
    """
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    event_type: str = field(init=False, default="")

    def __post_init__(self):
        """设置事件类型"""
        self.event_type = self.__class__.__name__
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """将事件转换为字典格式"""
        pass
    
    def __str__(self) -> str:
        return f"{self.event_type}(id={self.event_id[:8]}, time={self.timestamp.strftime('%H:%M:%S')})"


@dataclass
class TrainingStartedEvent(BaseEvent):
    """
    训练开始事件

    当训练任务开始时发布此事件
    """
    training_config: Dict[str, Any] = field(default_factory=dict)
    training_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp.isoformat(),
            'training_config': self.training_config,
            'training_id': self.training_id
        }


@dataclass
class TrainingProgressEvent(BaseEvent):
    """
    训练进度事件
    
    训练过程中的进度更新事件
    """
    progress_percentage: Optional[float] = None
    current_step: Optional[str] = None
    total_steps: Optional[int] = None
    current_step_number: Optional[int] = None
    additional_info: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp.isoformat(),
            'progress_percentage': self.progress_percentage,
            'current_step': self.current_step,
            'total_steps': self.total_steps,
            'current_step_number': self.current_step_number,
            'additional_info': self.additional_info
        }


@dataclass
class TrainingLogEvent(BaseEvent):
    """
    训练日志事件

    训练过程中的日志消息事件
    """
    message: str = ""
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR
    source: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp.isoformat(),
            'message': self.message,
            'log_level': self.log_level,
            'source': self.source
        }


@dataclass
class TrainingCompletedEvent(BaseEvent):
    """
    训练完成事件
    
    训练成功完成时发布此事件，包含结果文件路径
    """
    result_files: Dict[str, str] = field(default_factory=dict)
    training_duration: Optional[float] = None
    model_metrics: Dict[str, Any] = field(default_factory=dict)
    training_summary: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp.isoformat(),
            'result_files': self.result_files,
            'training_duration': self.training_duration,
            'model_metrics': self.model_metrics,
            'training_summary': self.training_summary
        }


@dataclass
class TrainingFailedEvent(BaseEvent):
    """
    训练失败事件

    训练失败时发布此事件
    """
    error_message: str = ""
    error_type: Optional[str] = None
    stack_trace: Optional[str] = None
    failed_step: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp.isoformat(),
            'error_message': self.error_message,
            'error_type': self.error_type,
            'stack_trace': self.stack_trace,
            'failed_step': self.failed_step
        }


@dataclass
class TrainingCancelledEvent(BaseEvent):
    """
    训练取消事件
    
    训练被用户取消时发布此事件
    """
    cancellation_reason: Optional[str] = None
    cancelled_by: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'timestamp': self.timestamp.isoformat(),
            'cancellation_reason': self.cancellation_reason,
            'cancelled_by': self.cancelled_by
        }


# 事件类型映射，用于反序列化
EVENT_TYPE_MAPPING = {
    'TrainingStartedEvent': TrainingStartedEvent,
    'TrainingProgressEvent': TrainingProgressEvent,
    'TrainingLogEvent': TrainingLogEvent,
    'TrainingCompletedEvent': TrainingCompletedEvent,
    'TrainingFailedEvent': TrainingFailedEvent,
    'TrainingCancelledEvent': TrainingCancelledEvent,
}


def create_event_from_dict(event_data: Dict[str, Any]) -> BaseEvent:
    """
    从字典创建事件对象
    
    Args:
        event_data: 事件数据字典
        
    Returns:
        BaseEvent: 事件对象
        
    Raises:
        ValueError: 未知的事件类型
    """
    event_type = event_data.get('event_type')
    if event_type not in EVENT_TYPE_MAPPING:
        raise ValueError(f"Unknown event type: {event_type}")
    
    event_class = EVENT_TYPE_MAPPING[event_type]
    
    # 移除通用字段，准备构造参数
    constructor_data = event_data.copy()
    constructor_data.pop('event_id', None)
    constructor_data.pop('event_type', None)
    constructor_data.pop('timestamp', None)
    
    # 创建事件对象
    event = event_class(**constructor_data)
    
    # 设置通用字段
    if 'event_id' in event_data:
        event.event_id = event_data['event_id']
    if 'timestamp' in event_data:
        if isinstance(event_data['timestamp'], str):
            event.timestamp = datetime.fromisoformat(event_data['timestamp'])
        else:
            event.timestamp = event_data['timestamp']
    
    return event
