# -*- coding: utf-8 -*-
"""
统一相关分析组件
提供多变量领先滞后筛选分析功能，包含相关性和KL散度双重评估
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import logging
from typing import List, Dict, Any, Optional, Tuple

# 配置matplotlib中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

from .base import TimeSeriesAnalysisComponent

logger = logging.getLogger(__name__)


class UnifiedCorrelationAnalysisComponent(TimeSeriesAnalysisComponent):
    """统一相关分析组件"""
    
    def __init__(self):
        super().__init__("time_lag_corr", "相关分析")

    def render(self, st_obj, **kwargs) -> Any:
        """
        重写渲染方法，跳过数据状态显示

        Args:
            st_obj: Streamlit对象
            **kwargs: 其他参数

        Returns:
            Any: 分析结果
        """
        try:
            # 检测标签页激活状态
            tab_index = kwargs.get('tab_index', 0)
            self.detect_tab_activation(st_obj, tab_index)

            # 直接获取数据，不显示数据状态信息
            data, data_source, data_name = self.get_module_data()

            if data is None:
                st_obj.info("📋 请在左侧侧边栏上传数据文件以进行分析")
                st_obj.markdown(f"""
                **📋 使用说明：**
                1. **数据上传**：在左侧侧边栏上传数据文件（唯一上传入口）
                2. **数据格式**：第一列为时间戳，其余列为变量数据
                3. **支持格式**：CSV、Excel (.xlsx, .xls)
                4. **编码支持**：UTF-8、GBK、GB2312等

                **🔄 数据共享说明：**
                - 侧边栏上传的数据在三个分析模块间自动共享
                - 平稳性分析、相关性分析、领先滞后分析使用同一数据源
                - 无需重复上传，一次上传即可在所有模块中使用
                """)
                return None

            # 渲染分析界面
            return self.render_analysis_interface(st_obj, data, data_name)

        except Exception as e:
            self.handle_error(st_obj, e, f"渲染{self.title}组件")
            return None
    
    def render_analysis_interface(self, st_obj, data: pd.DataFrame, data_name: str) -> Any:
        """
        渲染统一相关分析界面

        Args:
            st_obj: Streamlit对象
            data: 分析数据
            data_name: 数据名称

        Returns:
            Any: 分析结果
        """
        try:
            results = {}

            # 第一部分：DTW分析
            st_obj.markdown("#### 🕰️ DTW分析")
            try:
                from dashboard.ui.components.analysis.timeseries import DTWAnalysisComponent
                dtw_component = DTWAnalysisComponent()
                dtw_result = dtw_component.render_analysis_interface(st_obj, data, data_name)
                results["dtw"] = dtw_result
            except Exception as e:
                st_obj.error(f"DTW分析组件加载失败: {e}")
                import traceback
                st_obj.code(traceback.format_exc())

            st_obj.markdown("---")

            # 第二部分：领先滞后分析
            st_obj.markdown("#### ⏰ 领先滞后分析")
            st_obj.info("💡 **功能说明**：从多个候选变量中筛选出与目标变量最相关的预测变量，同时提供相关性和KL散度分析。支持单个变量的详细分析。")
            multivariate_result = self.render_multivariate_screening(st_obj, data, data_name)
            results["multivariate"] = multivariate_result

            return results

        except Exception as e:
            logger.error(f"渲染统一相关分析界面时出错: {e}")
            st_obj.error(f"渲染分析界面时出错: {e}")
            return None
    
# 双变量分析功能已移除，因为多变量分析已包含相同功能且更强大
    
    def render_multivariate_screening(self, st_obj, data: pd.DataFrame, data_name: str) -> Any:
        """渲染多变量领先滞后筛选界面"""
        try:
            # 参数设置区域
            col1, col2 = st_obj.columns(2)
            
            with col1:
                st_obj.markdown("**目标变量设置**")
                
                numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()
                if len(numeric_columns) < 2:
                    st_obj.warning("数据中的数值型变量少于2个，无法进行分析")
                    return None
                
                target_var = st_obj.selectbox(
                    "选择目标变量",
                    numeric_columns,
                    key="multivariate_target_var",
                    help="选择要预测的目标变量"
                )
                
                candidate_vars = st_obj.multiselect(
                    "选择候选变量",
                    [col for col in numeric_columns if col != target_var],
                    key="multivariate_candidate_vars",
                    help="选择用于筛选的候选预测变量"
                )

                # 添加分析按钮 - 使用图2的样式
                if st_obj.button(
                    "开始分析",
                    key="multivariate_analyze_button",
                    type="primary",
                    use_container_width=False
                ):
                    analyze_button = True
                else:
                    analyze_button = False

            with col2:
                st_obj.markdown("**分析配置**")
                
                max_lags_val = st_obj.number_input(
                    "最大滞后期数",
                    min_value=1,
                    max_value=50,
                    value=20,
                    key="multivariate_max_lags",
                    help="设置要分析的最大滞后期数"
                )
                
                kl_bins_val = st_obj.number_input(
                    "KL散度分箱数",
                    min_value=5,
                    max_value=50,
                    value=10,
                    key="multivariate_kl_bins",
                    help="设置KL散度计算的分箱数量"
                )
            
            if analyze_button and target_var and candidate_vars:
                return self.perform_multivariate_screening(st_obj, data, target_var, candidate_vars, max_lags_val, kl_bins_val)
            
            # 显示之前的结果（如果有）
            results = self.get_state('multivariate_results')
            if results:
                self.render_multivariate_results(st_obj, results)
                
            return results
            
        except Exception as e:
            logger.error(f"渲染多变量领先滞后筛选界面时出错: {e}")
            st_obj.error(f"渲染多变量领先滞后筛选界面时出错: {e}")
            return None
    
# 双变量分析执行方法已移除
    
    def perform_multivariate_screening(self, st_obj, data: pd.DataFrame, target_var: str, candidate_vars: List[str], max_lags: int, kl_bins: int):
        """执行多变量领先滞后筛选分析"""
        with st_obj.spinner("正在进行多变量领先滞后筛选分析..."):
            try:
                # 使用统一后端进行分析
                from dashboard.tools.time_series_property import combined_lead_lag_backend
                
                # 调用后端函数
                results_list, errors, warnings = combined_lead_lag_backend.perform_combined_lead_lag_analysis(
                    data, target_var, candidate_vars, max_lags, kl_bins
                )
                
                # 转换结果为DataFrame
                if results_list:
                    results_df = pd.DataFrame(results_list)
                else:
                    results_df = pd.DataFrame()
                
                results = {
                    'target_var': target_var,
                    'candidate_vars': candidate_vars,
                    'max_lags': max_lags,
                    'kl_bins': kl_bins,
                    'results_df': results_df,
                    'errors': errors,
                    'warnings': warnings
                }
                
                # 保存结果
                self.set_state('multivariate_results', results)
                
                # 渲染结果
                self.render_multivariate_results(st_obj, results)
                
                return results
                
            except Exception as e:
                logger.error(f"多变量领先滞后筛选分析失败: {e}")
                st_obj.error(f"分析失败: {e}")
                return None

# 双变量结果渲染方法已移除

    def render_multivariate_results(self, st_obj, results: Dict[str, Any]):
        """渲染多变量领先滞后筛选结果"""
        try:
            results_df = results.get('results_df')
            errors = results.get('errors', [])
            warnings = results.get('warnings', [])

            # 显示错误和警告
            if errors:
                for error in errors:
                    st_obj.error(f"❌ {error}")

            if warnings:
                for warning in warnings:
                    st_obj.warning(f"⚠️ {warning}")

            if results_df is None or results_df.empty:
                st_obj.warning("没有分析结果可显示")
                return

            st_obj.markdown("##### 筛选结果")

            # 格式化结果表格
            display_results = results_df.copy()

            # 移除不能序列化的列
            columns_to_remove = ['full_correlogram_df', 'full_kl_divergence_df']
            for col in columns_to_remove:
                if col in display_results.columns:
                    display_results = display_results.drop(columns=[col])

            # 列名映射
            column_mapping = {
                'target_variable': '目标变量',
                'candidate_variable': '候选变量',
                'k_corr': '最优滞后(相关)',
                'corr_at_k_corr': '最大相关系数',
                'k_kl': '最优滞后(KL)',
                'kl_at_k_kl': '最小KL散度',
                'notes': '备注'
            }

            display_results = display_results.rename(columns=column_mapping)

            # 数值格式化
            if '最大相关系数' in display_results.columns:
                display_results['最大相关系数'] = display_results['最大相关系数'].round(4)
            if '最小KL散度' in display_results.columns:
                display_results['最小KL散度'] = display_results['最小KL散度'].round(4)

            st_obj.dataframe(display_results, use_container_width=True)

            # 提供下载功能
            csv_string = display_results.to_csv(index=False, encoding='utf-8-sig')
            csv_data = csv_string.encode('utf-8-sig')
            st_obj.download_button(
                label="📥 下载筛选结果",
                data=csv_data,
                file_name=f"multivariate_screening_{results['target_var']}.csv",
                mime="text/csv",
                key="download_multivariate_data"
            )

            # 详细图表展示
            st_obj.markdown("**详细分析图表**")
            candidate_var_for_plot = st_obj.selectbox(
                "选择变量查看详细图表",
                results['candidate_vars'],
                key="multivariate_plot_var",
                help="选择一个候选变量查看其详细的相关性和KL散度图表"
            )

            if candidate_var_for_plot:
                self.render_detailed_multivariate_charts(st_obj, results, candidate_var_for_plot)

        except Exception as e:
            logger.error(f"渲染多变量结果时出错: {e}")
            st_obj.error(f"显示结果时出错: {e}")

    def plot_correlation_chart(self, st_obj, correlations: List[float], lags: List[int],
                              lagged_var: str, leading_var: str):
        """绘制相关性图表"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))

            # 绘制柱状图
            bars = ax.bar(lags, correlations, alpha=0.7, color='steelblue', edgecolor='black', linewidth=0.5)

            # 标记最大相关系数
            if correlations:
                max_corr_idx = np.argmax(np.abs(correlations))
                max_corr = correlations[max_corr_idx]
                max_lag = lags[max_corr_idx]

                bars[max_corr_idx].set_color('red')
                ax.annotate(f'最大相关性\n滞后期: {max_lag}\n相关系数: {max_corr:.3f}',
                           xy=(max_lag, max_corr),
                           xytext=(max_lag, max_corr + 0.1 * np.sign(max_corr)),
                           arrowprops=dict(arrowstyle='->', color='red'),
                           fontsize=10, ha='center')

            ax.set_xlabel('滞后期')
            ax.set_ylabel('相关系数')
            ax.set_title(f'{lagged_var} 与 {leading_var} 的时间滞后相关性')
            ax.grid(True, alpha=0.3)
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # 添加相关性强度参考线
            ax.axhline(y=0.3, color='orange', linestyle='--', alpha=0.5, label='中等相关性 (0.3)')
            ax.axhline(y=-0.3, color='orange', linestyle='--', alpha=0.5)
            ax.axhline(y=0.7, color='red', linestyle='--', alpha=0.5, label='强相关性 (0.7)')
            ax.axhline(y=-0.7, color='red', linestyle='--', alpha=0.5)

            ax.legend()
            plt.tight_layout()

            st_obj.pyplot(fig)
            plt.close()

        except Exception as e:
            logger.error(f"绘制相关性图表时出错: {e}")
            st_obj.error(f"绘制图表时出错: {e}")

    def render_detailed_multivariate_charts(self, st_obj, results: Dict[str, Any], candidate_var: str):
        """渲染多变量分析的详细图表"""
        try:
            # 获取原始数据
            data, _, _ = self.get_module_data()
            if data is None:
                st_obj.warning("无法获取原始数据")
                return

            # 导入后端函数获取详细数据
            from dashboard.tools.time_series_property import combined_lead_lag_backend

            detailed_corr_df, detailed_kl_df = combined_lead_lag_backend.get_detailed_lag_data_for_candidate(
                data, results['target_var'], candidate_var, results['max_lags'], results['kl_bins']
            )

            if detailed_corr_df is not None and detailed_kl_df is not None:
                # 创建两列布局
                col1, col2 = st_obj.columns(2)

                with col1:
                    st_obj.markdown(f"**{candidate_var} 相关性分析**")
                    if not detailed_corr_df.empty:
                        fig, ax = plt.subplots(figsize=(8, 5))
                        ax.plot(detailed_corr_df['Lag'], detailed_corr_df['Correlation'],
                               marker='o', linewidth=2, markersize=4)
                        ax.set_xlabel('滞后期')
                        ax.set_ylabel('相关系数')
                        ax.set_title(f'{results["target_var"]} vs {candidate_var} 相关性')
                        ax.grid(True, alpha=0.3)
                        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
                        plt.tight_layout()
                        st_obj.pyplot(fig)
                        plt.close()

                with col2:
                    st_obj.markdown(f"**{candidate_var} KL散度分析**")
                    if not detailed_kl_df.empty:
                        fig, ax = plt.subplots(figsize=(8, 5))
                        ax.plot(detailed_kl_df['Lag'], detailed_kl_df['KL_Divergence'],
                               marker='s', linewidth=2, markersize=4, color='orange')
                        ax.set_xlabel('滞后期')
                        ax.set_ylabel('KL散度')
                        ax.set_title(f'{results["target_var"]} vs {candidate_var} KL散度')
                        ax.grid(True, alpha=0.3)
                        plt.tight_layout()
                        st_obj.pyplot(fig)
                        plt.close()

        except Exception as e:
            logger.error(f"渲染详细图表时出错: {e}")
            st_obj.error(f"生成详细图表时出错: {e}")
