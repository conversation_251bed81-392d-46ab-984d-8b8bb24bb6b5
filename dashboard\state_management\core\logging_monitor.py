# -*- coding: utf-8 -*-
"""
统一日志和监控基础设施
提供结构化日志、性能监控、指标收集和告警功能
"""

import logging
import logging.handlers
import os
import json
import threading
import time
import traceback
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import sys
import queue

# 可选依赖
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    psutil = None
    HAS_PSUTIL = False


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"         # 计数器
    GAUGE = "gauge"            # 仪表盘
    HISTOGRAM = "histogram"     # 直方图
    TIMER = "timer"            # 计时器
    RATE = "rate"              # 速率


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: LogLevel
    logger_name: str
    message: str
    module: str = ""
    function: str = ""
    line_number: int = 0
    thread_id: int = 0
    process_id: int = 0
    extra_data: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None


@dataclass
class MetricEntry:
    """指标条目"""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""
    description: str = ""


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric_name: str
    condition: str  # "gt", "lt", "eq", "ne", "gte", "lte"
    threshold: Union[int, float]
    level: AlertLevel
    enabled: bool = True
    cooldown_seconds: int = 300  # 冷却时间
    last_triggered: Optional[datetime] = None
    description: str = ""


@dataclass
class Alert:
    """告警"""
    rule_name: str
    metric_name: str
    current_value: Union[int, float]
    threshold: Union[int, float]
    level: AlertLevel
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        """格式化日志记录"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process
        }
        
        # 添加额外数据
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_data, ensure_ascii=False)


class LoggingMonitor:
    """统一日志和监控系统"""
    
    def __init__(
        self,
        log_dir: str = "logs",
        log_level: LogLevel = None,  # 改为None，支持环境变量控制
        max_log_files: int = 10,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        enable_console: bool = True,
        enable_structured: bool = True,
        metrics_retention_hours: int = 24,
        enable_system_monitoring: bool = True,
        enable_log_deduplication: bool = True,  # 新增：启用日志去重
        log_sampling_rate: float = 1.0  # 新增：日志采样率
    ):
        """
        初始化日志和监控系统

        Args:
            log_dir: 日志目录
            log_level: 日志级别（None时从环境变量读取）
            max_log_files: 最大日志文件数
            max_file_size: 最大文件大小
            enable_console: 是否启用控制台输出
            enable_structured: 是否启用结构化日志
            metrics_retention_hours: 指标保留时间（小时）
            enable_system_monitoring: 是否启用系统监控
            enable_log_deduplication: 是否启用日志去重
            log_sampling_rate: 日志采样率（0.0-1.0）
        """
        self.log_dir = log_dir

        # 从环境变量获取日志级别
        self.log_level = self._get_log_level_from_env(log_level)

        self.enable_structured = enable_structured
        self.metrics_retention_hours = metrics_retention_hours
        self.enable_system_monitoring = enable_system_monitoring
        self.enable_log_deduplication = enable_log_deduplication
        self.log_sampling_rate = log_sampling_rate

        # 日志去重和采样相关
        self._log_dedup_cache = {}  # 存储日志哈希和时间戳
        self._log_dedup_window = 60  # 去重时间窗口（秒）
        self._log_sample_counter = 0  # 采样计数器

        # 禁用日志目录创建 - 只使用内存日志
        # os.makedirs(log_dir, exist_ok=True)
        
        # 日志存储
        self._log_entries: deque = deque(maxlen=10000)
        
        # 指标存储
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._metric_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 告警系统
        self._alert_rules: Dict[str, AlertRule] = {}
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_history: deque = deque(maxlen=1000)
        self._alert_callbacks: List[Callable[[Alert], None]] = []
        
        # 系统监控
        self._system_metrics: Dict[str, Any] = {}
        self._monitoring_enabled = enable_system_monitoring
        
        # 统计信息
        self._stats = {
            'total_logs': 0,
            'logs_by_level': {level.value: 0 for level in LogLevel},
            'total_metrics': 0,
            'metrics_by_type': {mtype.value: 0 for mtype in MetricType},
            'total_alerts': 0,
            'active_alerts': 0,
            'system_monitoring_enabled': enable_system_monitoring
        }
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 设置日志系统
        self._setup_logging(max_log_files, max_file_size, enable_console)
        
        # 启动监控线程
        if self._monitoring_enabled:
            self._monitor_thread = threading.Thread(target=self._monitoring_worker, daemon=True)
            self._monitor_thread.start()
        else:
            self._monitor_thread = None
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        
        self.logger = self.get_logger(__name__)
        self.logger.info(f"LoggingMonitor initialized with level: {self.log_level.value}, deduplication: {self.enable_log_deduplication}, sampling: {self.log_sampling_rate}")

    def _get_log_level_from_env(self, default_level):
        """从环境变量获取日志级别"""
        if default_level is not None:
            return default_level

        # 从环境变量读取日志级别，默认使用WARNING减少日志噪音
        env_level = os.getenv('LOG_LEVEL', 'WARNING').upper()

        # 生产环境检测
        is_production = os.getenv('ENVIRONMENT', '').lower() in ['prod', 'production']
        if is_production and env_level in ['DEBUG', 'INFO']:
            env_level = 'WARNING'  # 生产环境默认使用WARNING级别

        try:
            return LogLevel(env_level)
        except ValueError:
            print(f"[WARNING] Invalid LOG_LEVEL: {env_level}, using WARNING")
            return LogLevel.WARNING

    def _should_log_message(self, level: LogLevel, message: str) -> bool:
        """检查是否应该记录此日志消息（去重和采样）"""
        import hashlib

        # 采样检查
        if self.log_sampling_rate < 1.0:
            self._log_sample_counter += 1
            if (self._log_sample_counter % int(1.0 / self.log_sampling_rate)) != 0:
                return False

        # 去重检查
        if not self.enable_log_deduplication:
            return True

        # 对于ERROR和CRITICAL级别，不进行去重
        if level in [LogLevel.ERROR, LogLevel.CRITICAL]:
            return True

        # 生成消息哈希
        message_hash = hashlib.md5(f"{level.value}:{message}".encode()).hexdigest()
        current_time = time.time()

        # 检查是否在去重窗口内
        if message_hash in self._log_dedup_cache:
            last_time = self._log_dedup_cache[message_hash]
            if current_time - last_time < self._log_dedup_window:
                return False  # 重复消息，跳过

        # 更新缓存
        self._log_dedup_cache[message_hash] = current_time

        # 清理过期的缓存条目
        if len(self._log_dedup_cache) > 1000:  # 限制缓存大小
            expired_keys = [
                key for key, timestamp in self._log_dedup_cache.items()
                if current_time - timestamp > self._log_dedup_window
            ]
            for key in expired_keys:
                del self._log_dedup_cache[key]

        return True
    
    def _setup_logging(self, max_log_files: int, max_file_size: int, enable_console: bool):
        """设置日志系统"""
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.log_level.value))

        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 禁用文件处理器 - 不创建日志文件
        # log_file = os.path.join(self.log_dir, "app.log")
        # file_handler = logging.handlers.RotatingFileHandler(
        #     log_file,
        #     maxBytes=max_file_size,
        #     backupCount=max_log_files,
        #     encoding='utf-8'
        # )
        #
        # if self.enable_structured:
        #     file_handler.setFormatter(StructuredFormatter())
        # else:
        #     file_handler.setFormatter(logging.Formatter(
        #         '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        #     ))
        #
        # root_logger.addHandler(file_handler)
        
        # 控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))
            root_logger.addHandler(console_handler)

        # 禁用错误日志文件处理器 - 不创建错误日志文件
        # error_log_file = os.path.join(self.log_dir, "error.log")
        # error_handler = logging.handlers.RotatingFileHandler(
        #     error_log_file,
        #     maxBytes=max_file_size,
        #     backupCount=max_log_files,
        #     encoding='utf-8'
        # )
        # error_handler.setLevel(logging.ERROR)
        #
        # if self.enable_structured:
        #     error_handler.setFormatter(StructuredFormatter())
        # else:
        #     error_handler.setFormatter(logging.Formatter(
        #         '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        #     ))
        #
        # root_logger.addHandler(error_handler)
        
        # 添加自定义处理器来收集日志条目
        custom_handler = LogCollectorHandler(self)
        custom_handler.setLevel(getattr(logging, self.log_level.value))
        root_logger.addHandler(custom_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器
        """
        return logging.getLogger(name)
    
    def log_structured(
        self,
        level: LogLevel,
        message: str,
        logger_name: str = "app",
        extra_data: Dict[str, Any] = None,
        **kwargs
    ):
        """
        记录结构化日志 - 优化版本，支持去重和采样

        Args:
            level: 日志级别
            message: 日志消息
            logger_name: 日志器名称
            extra_data: 额外数据
            **kwargs: 其他参数
        """
        # 检查是否应该记录此日志
        if not self._should_log_message(level, message):
            return

        logger = self.get_logger(logger_name)

        # 创建日志记录
        log_record = logger.makeRecord(
            logger_name,
            getattr(logging, level.value),
            "",
            0,
            message,
            (),
            None,
            func="",
            extra=extra_data or {},
            sinfo=None
        )

        # 添加额外数据
        if extra_data:
            log_record.extra_data = extra_data

        logger.handle(log_record)
    
    def record_metric(
        self,
        name: str,
        value: Union[int, float],
        metric_type: MetricType = MetricType.GAUGE,
        tags: Dict[str, str] = None,
        unit: str = "",
        description: str = ""
    ):
        """
        记录指标
        
        Args:
            name: 指标名称
            value: 指标值
            metric_type: 指标类型
            tags: 标签
            unit: 单位
            description: 描述
        """
        with self._lock:
            try:
                # 创建指标条目
                metric = MetricEntry(
                    name=name,
                    value=value,
                    metric_type=metric_type,
                    tags=tags or {},
                    unit=unit,
                    description=description
                )
                
                # 存储指标
                self._metrics[name].append(metric)
                
                # 存储元数据
                if name not in self._metric_metadata:
                    self._metric_metadata[name] = {
                        'type': metric_type.value,
                        'unit': unit,
                        'description': description,
                        'first_recorded': datetime.now(),
                        'count': 0
                    }
                
                self._metric_metadata[name]['count'] += 1
                self._metric_metadata[name]['last_recorded'] = datetime.now()
                self._metric_metadata[name]['last_value'] = value
                
                # 更新统计
                self._stats['total_metrics'] += 1
                self._stats['metrics_by_type'][metric_type.value] += 1
                
                # 检查告警规则
                self._check_alert_rules(name, value)
                
            except Exception as e:
                self.logger.error(f"Failed to record metric {name}: {e}")
    
    def _check_alert_rules(self, metric_name: str, value: Union[int, float]):
        """检查告警规则"""
        try:
            for rule_name, rule in self._alert_rules.items():
                if rule.metric_name != metric_name or not rule.enabled:
                    continue
                
                # 检查冷却时间
                if rule.last_triggered:
                    cooldown_end = rule.last_triggered + timedelta(seconds=rule.cooldown_seconds)
                    if datetime.now() < cooldown_end:
                        continue
                
                # 检查条件
                triggered = False
                if rule.condition == "gt" and value > rule.threshold:
                    triggered = True
                elif rule.condition == "lt" and value < rule.threshold:
                    triggered = True
                elif rule.condition == "eq" and value == rule.threshold:
                    triggered = True
                elif rule.condition == "ne" and value != rule.threshold:
                    triggered = True
                elif rule.condition == "gte" and value >= rule.threshold:
                    triggered = True
                elif rule.condition == "lte" and value <= rule.threshold:
                    triggered = True
                
                if triggered:
                    self._trigger_alert(rule, value)
                    
        except Exception as e:
            self.logger.error(f"Failed to check alert rules: {e}")
    
    def _trigger_alert(self, rule: AlertRule, current_value: Union[int, float]):
        """触发告警"""
        try:
            # 创建告警
            alert = Alert(
                rule_name=rule.name,
                metric_name=rule.metric_name,
                current_value=current_value,
                threshold=rule.threshold,
                level=rule.level,
                message=f"Metric {rule.metric_name} is {current_value}, threshold is {rule.threshold}"
            )
            
            # 存储告警
            self._active_alerts[rule.name] = alert
            self._alert_history.append(alert)
            
            # 更新规则状态
            rule.last_triggered = datetime.now()
            
            # 更新统计
            self._stats['total_alerts'] += 1
            self._stats['active_alerts'] = len(self._active_alerts)
            
            # 调用回调函数
            for callback in self._alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")
            
            # 记录告警日志
            self.logger.warning(f"Alert triggered: {alert.message}")
            
        except Exception as e:
            self.logger.error(f"Failed to trigger alert: {e}")
    
    def add_alert_rule(self, rule: AlertRule):
        """
        添加告警规则
        
        Args:
            rule: 告警规则
        """
        with self._lock:
            self._alert_rules[rule.name] = rule
            self.logger.info(f"Added alert rule: {rule.name}")
    
    def remove_alert_rule(self, rule_name: str) -> bool:
        """
        移除告警规则
        
        Args:
            rule_name: 规则名称
            
        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if rule_name in self._alert_rules:
                del self._alert_rules[rule_name]
                # 同时移除相关的活跃告警
                if rule_name in self._active_alerts:
                    del self._active_alerts[rule_name]
                    self._stats['active_alerts'] = len(self._active_alerts)
                self.logger.info(f"Removed alert rule: {rule_name}")
                return True
            return False
    
    def resolve_alert(self, rule_name: str) -> bool:
        """
        解决告警
        
        Args:
            rule_name: 规则名称
            
        Returns:
            bool: 是否成功解决
        """
        with self._lock:
            if rule_name in self._active_alerts:
                alert = self._active_alerts[rule_name]
                alert.resolved = True
                alert.resolved_at = datetime.now()
                del self._active_alerts[rule_name]
                self._stats['active_alerts'] = len(self._active_alerts)
                self.logger.info(f"Resolved alert: {rule_name}")
                return True
            return False

    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """
        添加告警回调函数

        Args:
            callback: 回调函数
        """
        with self._lock:
            self._alert_callbacks.append(callback)

    def _monitoring_worker(self):
        """监控工作线程"""
        while True:
            try:
                time.sleep(30)  # 每30秒收集一次系统指标
                self._collect_system_metrics()
            except Exception as e:
                self.logger.error(f"Error in monitoring worker: {e}")

    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            if not HAS_PSUTIL:
                # 如果没有psutil，记录基本的模拟指标
                import random
                self.record_metric("system.cpu.usage", random.uniform(10, 80), MetricType.GAUGE, unit="%")
                self.record_metric("system.memory.usage", random.uniform(30, 90), MetricType.GAUGE, unit="%")
                self._system_metrics = {
                    'cpu_percent': random.uniform(10, 80),
                    'memory_percent': random.uniform(30, 90),
                    'last_updated': datetime.now(),
                    'psutil_available': False
                }
                return

            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric("system.cpu.usage", cpu_percent, MetricType.GAUGE, unit="%")

            # 内存使用情况
            memory = psutil.virtual_memory()
            self.record_metric("system.memory.usage", memory.percent, MetricType.GAUGE, unit="%")
            self.record_metric("system.memory.available", memory.available, MetricType.GAUGE, unit="bytes")
            self.record_metric("system.memory.used", memory.used, MetricType.GAUGE, unit="bytes")

            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.record_metric("system.disk.usage", disk_percent, MetricType.GAUGE, unit="%")
            self.record_metric("system.disk.free", disk.free, MetricType.GAUGE, unit="bytes")

            # 网络IO
            net_io = psutil.net_io_counters()
            self.record_metric("system.network.bytes_sent", net_io.bytes_sent, MetricType.COUNTER, unit="bytes")
            self.record_metric("system.network.bytes_recv", net_io.bytes_recv, MetricType.COUNTER, unit="bytes")

            # 进程信息
            process = psutil.Process()
            self.record_metric("process.cpu.usage", process.cpu_percent(), MetricType.GAUGE, unit="%")
            self.record_metric("process.memory.usage", process.memory_percent(), MetricType.GAUGE, unit="%")
            self.record_metric("process.memory.rss", process.memory_info().rss, MetricType.GAUGE, unit="bytes")

            # 更新系统指标缓存
            self._system_metrics = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk_percent,
                'process_cpu': process.cpu_percent(),
                'process_memory': process.memory_percent(),
                'last_updated': datetime.now(),
                'psutil_available': True
            }

        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")

    def _cleanup_worker(self):
        """清理工作线程"""
        while True:
            try:
                time.sleep(3600)  # 每小时清理一次
                self._cleanup_old_data()
            except Exception as e:
                self.logger.error(f"Error in cleanup worker: {e}")

    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.metrics_retention_hours)

            # 清理旧指标
            for metric_name, metric_deque in self._metrics.items():
                # 从队列前面移除旧数据
                while metric_deque and metric_deque[0].timestamp < cutoff_time:
                    metric_deque.popleft()

            # 清理已解决的旧告警
            resolved_alerts = []
            for alert in self._alert_history:
                if alert.resolved and alert.resolved_at and alert.resolved_at < cutoff_time:
                    resolved_alerts.append(alert)

            for alert in resolved_alerts:
                try:
                    self._alert_history.remove(alert)
                except ValueError:
                    pass

            self.logger.debug(f"Cleaned up old data, removed {len(resolved_alerts)} old alerts")

        except Exception as e:
            self.logger.error(f"Failed to cleanup old data: {e}")

    def get_metrics(self, name: str = None, limit: int = 100) -> List[MetricEntry]:
        """
        获取指标数据

        Args:
            name: 指标名称，None表示获取所有指标
            limit: 返回数量限制

        Returns:
            List[MetricEntry]: 指标列表
        """
        with self._lock:
            if name:
                if name in self._metrics:
                    return list(self._metrics[name])[-limit:]
                return []
            else:
                # 返回所有指标的最新数据
                all_metrics = []
                for metric_deque in self._metrics.values():
                    all_metrics.extend(list(metric_deque)[-limit:])

                # 按时间排序
                all_metrics.sort(key=lambda x: x.timestamp, reverse=True)
                return all_metrics[:limit]

    def get_metric_summary(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取指标摘要

        Args:
            name: 指标名称

        Returns:
            Optional[Dict[str, Any]]: 指标摘要
        """
        with self._lock:
            if name not in self._metrics or not self._metrics[name]:
                return None

            metrics = list(self._metrics[name])
            values = [m.value for m in metrics]

            summary = {
                'name': name,
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'latest': values[-1] if values else None,
                'first_recorded': metrics[0].timestamp.isoformat(),
                'last_recorded': metrics[-1].timestamp.isoformat()
            }

            # 添加元数据
            if name in self._metric_metadata:
                summary.update(self._metric_metadata[name])

            return summary

    def get_logs(self, level: LogLevel = None, limit: int = 100) -> List[LogEntry]:
        """
        获取日志条目

        Args:
            level: 日志级别过滤
            limit: 返回数量限制

        Returns:
            List[LogEntry]: 日志列表
        """
        with self._lock:
            logs = list(self._log_entries)

            # 按级别过滤
            if level:
                logs = [log for log in logs if log.level == level]

            # 按时间排序并限制数量
            logs.sort(key=lambda x: x.timestamp, reverse=True)
            return logs[:limit]

    def get_alerts(self, active_only: bool = False, limit: int = 100) -> List[Alert]:
        """
        获取告警列表

        Args:
            active_only: 是否只返回活跃告警
            limit: 返回数量限制

        Returns:
            List[Alert]: 告警列表
        """
        with self._lock:
            if active_only:
                return list(self._active_alerts.values())
            else:
                alerts = list(self._alert_history)
                alerts.sort(key=lambda x: x.timestamp, reverse=True)
                return alerts[:limit]

    def get_system_metrics(self) -> Dict[str, Any]:
        """
        获取系统指标

        Returns:
            Dict[str, Any]: 系统指标
        """
        with self._lock:
            return self._system_metrics.copy()

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            stats = self._stats.copy()

            # 添加实时统计
            stats.update({
                'log_entries_count': len(self._log_entries),
                'metrics_count': len(self._metrics),
                'alert_rules_count': len(self._alert_rules),
                'alert_history_count': len(self._alert_history),
                'system_metrics': self._system_metrics
            })

            return stats

    def export_metrics(self, format: str = "json") -> str:
        """
        导出指标数据

        Args:
            format: 导出格式

        Returns:
            str: 导出的数据
        """
        with self._lock:
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'metrics': {},
                'metadata': self._metric_metadata.copy()
            }

            # 导出所有指标
            for name, metric_deque in self._metrics.items():
                export_data['metrics'][name] = [
                    {
                        'timestamp': m.timestamp.isoformat(),
                        'value': m.value,
                        'type': m.metric_type.value,
                        'tags': m.tags,
                        'unit': m.unit
                    }
                    for m in metric_deque
                ]

            if format.lower() == "json":
                return json.dumps(export_data, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"Unsupported export format: {format}")

    def clear_metrics(self, name: str = None):
        """
        清除指标数据

        Args:
            name: 指标名称，None表示清除所有指标
        """
        with self._lock:
            if name:
                if name in self._metrics:
                    self._metrics[name].clear()
                    if name in self._metric_metadata:
                        del self._metric_metadata[name]
                    self.logger.info(f"Cleared metrics for: {name}")
            else:
                self._metrics.clear()
                self._metric_metadata.clear()
                self.logger.info("Cleared all metrics")

    def set_log_level(self, level: LogLevel):
        """
        设置日志级别

        Args:
            level: 日志级别
        """
        self.log_level = level
        logging.getLogger().setLevel(getattr(logging, level.value))
        self.logger.info(f"Log level set to: {level.value}")


class LogCollectorHandler(logging.Handler):
    """日志收集处理器"""

    def __init__(self, monitor: LoggingMonitor):
        super().__init__()
        self.monitor = monitor

    def emit(self, record):
        """处理日志记录"""
        try:
            # 创建日志条目
            log_entry = LogEntry(
                timestamp=datetime.fromtimestamp(record.created),
                level=LogLevel(record.levelname),
                logger_name=record.name,
                message=record.getMessage(),
                module=record.module,
                function=record.funcName,
                line_number=record.lineno,
                thread_id=record.thread,
                process_id=record.process,
                extra_data=getattr(record, 'extra_data', {}),
                stack_trace=self.format(record) if record.exc_info else None
            )

            # 存储日志条目
            with self.monitor._lock:
                self.monitor._log_entries.append(log_entry)
                self.monitor._stats['total_logs'] += 1
                self.monitor._stats['logs_by_level'][record.levelname] += 1

        except Exception:
            # 避免日志处理器本身出错
            pass


def create_alert_rule(
    name: str,
    metric_name: str,
    condition: str,
    threshold: Union[int, float],
    level: AlertLevel = AlertLevel.WARNING,
    cooldown_seconds: int = 300,
    description: str = ""
) -> AlertRule:
    """
    创建告警规则的便捷函数

    Args:
        name: 规则名称
        metric_name: 指标名称
        condition: 条件
        threshold: 阈值
        level: 告警级别
        cooldown_seconds: 冷却时间
        description: 描述

    Returns:
        AlertRule: 告警规则
    """
    return AlertRule(
        name=name,
        metric_name=metric_name,
        condition=condition,
        threshold=threshold,
        level=level,
        cooldown_seconds=cooldown_seconds,
        description=description
    )
