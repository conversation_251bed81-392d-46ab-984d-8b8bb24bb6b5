"""
DFM数据准备模块 - UI接口
这个模块为UI提供统一的数据准备接口，调用重构后的模块化数据处理系统。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Tuple, Optional, List
import logging
from pathlib import Path
import tempfile
import os

# 导入重构后的模块
try:
    from .modules.main_data_processor import prepare_data as process_data_internal
    from .modules.mapping_manager import load_mappings
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        modules_dir = os.path.join(current_dir, 'modules')
        if modules_dir not in sys.path:
            sys.path.insert(0, modules_dir)

        from main_data_processor import prepare_data as process_data_internal
        from mapping_manager import load_mappings
    except ImportError:
        # 最后的fallback：使用独立的包装器
        try:
            from .mapping_wrapper import load_mappings
            # 如果main_data_processor不可用，使用简化版本
            process_data_internal = None
        except ImportError:
            # 如果所有导入都失败，使用本地实现
            load_mappings = None
            process_data_internal = None

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def _fallback_load_mappings(excel_path: str, sheet_name: str, indicator_col: str = '高频指标',
                           type_col: str = '类型', industry_col: str = '行业') -> Tuple[Dict, Dict]:
    """
    Fallback映射加载函数，当主要的load_mappings不可用时使用
    """
    try:
        import pandas as pd
        df = pd.read_excel(excel_path, sheet_name=sheet_name)

        var_type_map = {}
        var_industry_map = {}

        if indicator_col in df.columns:
            if type_col in df.columns:
                var_type_map = dict(zip(df[indicator_col].fillna(''), df[type_col].fillna('')))
            if industry_col in df.columns:
                var_industry_map = dict(zip(df[indicator_col].fillna(''), df[industry_col].fillna('')))

        return var_type_map, var_industry_map
    except Exception as e:
        logger.error(f"Fallback映射加载失败: {e}")
        return {}, {}

# 如果load_mappings不可用，使用fallback
if load_mappings is None:
    load_mappings = _fallback_load_mappings

def prepare_data(
    excel_path: str,
    target_freq: str = "W-FRI",
    target_sheet_name: str = "工业增加值同比增速_月度_同花顺",
    target_variable_name: str = "规模以上工业增加值:当月同比",
    consecutive_nan_threshold: int = 10,
    data_start_date: str = "2010-01-31",
    data_end_date: str = "2025-07-03",
    reference_sheet_name: str = "指标体系",
    reference_column_name: str = "高频指标"
) -> Tuple[Optional[pd.DataFrame], Optional[Dict], Optional[Dict], Optional[List[Dict]]]:
    """
    执行DFM数据准备流程

    参数:
        excel_path: Excel文件路径
        target_freq: 目标频率
        target_sheet_name: 目标工作表名称
        target_variable_name: 目标变量名称
        consecutive_nan_threshold: 连续NaN阈值
        data_start_date: 开始日期
        data_end_date: 结束日期
        reference_sheet_name: 指标映射表名称
        reference_column_name: 参考列名称

    返回:
        Tuple[Optional[pd.DataFrame], Optional[Dict], Optional[Dict], Optional[List[Dict]]]:
            - 最终对齐的周度数据 (DataFrame)
            - 变量到行业的映射 (Dict)
            - 合并的转换日志 (Dict)
            - 详细的移除日志 (List[Dict])
    """
    
    try:
        logger.info("开始DFM数据准备流程...")
        
        # 处理文件路径
        if hasattr(excel_path, 'read'):
            # 如果是文件对象，保存到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                tmp_file.write(excel_path.read())
                excel_path = tmp_file.name
        
        # 设置配置参数，匹配重构后的函数签名
        logger.info(f"调用数据处理函数...")

        if process_data_internal is not None:
            # 执行数据处理，使用正确的参数名称
            result = process_data_internal(
                excel_path=excel_path,
                target_freq=target_freq,
                target_sheet_name=target_sheet_name,
                target_variable_name=target_variable_name,
                consecutive_nan_threshold=consecutive_nan_threshold,
                data_start_date=data_start_date,
                data_end_date=data_end_date,
                reference_sheet_name=reference_sheet_name,
                reference_column_name=reference_column_name
            )
        else:
            # 如果主要处理函数不可用，返回基本的映射信息
            logger.warning("主要数据处理函数不可用，返回基本映射信息")
            var_type_map, var_industry_map = load_mappings(excel_path, reference_sheet_name,
                                                         reference_column_name, '类型', '行业')
            result = (None, var_industry_map, var_type_map, [])
        
        # 解包返回结果
        processed_data, variable_mapping, transform_log, removal_log = result

        # 检查是否处理成功
        if processed_data is None:
            raise ValueError("数据处理失败，返回了None")

        logger.info(f"数据准备完成! 数据形状: {processed_data.shape}")

        # 直接返回与原始函数相同的格式
        return processed_data, variable_mapping, transform_log, removal_log
        
    except Exception as e:
        logger.error(f"数据准备过程中发生错误: {str(e)}")
        return None, None, None, None


# 导出的函数
__all__ = [
    'prepare_data',
    'load_mappings'
]
