"""
数据加载模块

负责从Excel文件中加载不同类型的数据：
- 目标变量数据
- 日度/周度预测变量数据
- 月度预测变量数据
"""

import pandas as pd
import numpy as np
import unicodedata
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict

from .config_constants import debug_print
from .format_detection import detect_sheet_format, parse_sheet_info
from .data_cleaner import DataCleaner

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        self.cleaner = DataCleaner()
        self.var_industry_map = {}
        self.raw_columns_across_all_sheets = set()
    
    def load_target_sheet(
        self, 
        excel_file, 
        sheet_name: str,
        target_variable_name: str,
        industry_name: str = "Macro"
    ) -> Tuple[Optional[pd.Series], Optional[pd.Series], Optional[pd.DataFrame], Set[str]]:
        """
        加载目标表格数据
        
        Args:
            excel_file: Excel文件对象
            sheet_name: 表格名称
            target_variable_name: 目标变量名称
            industry_name: 行业名称
            
        Returns:
            Tuple: (发布日期, 目标变量值, 预测变量DataFrame, 目标表格列名集合)
        """
        print(f"      检测到目标 Sheet，行业: '{industry_name}'...")
        
        try:
            # 检测格式并读取数据
            format_info = detect_sheet_format(excel_file, sheet_name)
            read_header = format_info['header']
            skip_rows = format_info['skiprows']
            
            print(f"    [目标Sheet读取参数] 格式: {format_info['format']}, header={read_header}, skiprows={skip_rows}")
            
            df_raw = pd.read_excel(excel_file, sheet_name=sheet_name, header=read_header, skiprows=skip_rows)
            debug_print(df_raw.head())
            debug_print(df_raw.tail())
            
            # 清理数据
            df_raw = self.cleaner.clean_zero_values(df_raw, "[目标Sheet] ")
            
            if df_raw.shape[1] < 2:
                print(f"      错误: 目标 Sheet '{sheet_name}' 列数 < 2。跳过。")
                return None, None, None, set()
            
            # 提取日期和目标变量
            date_col_name = df_raw.columns[0]
            actual_target_variable_name = df_raw.columns[1]
            target_sheet_cols = {actual_target_variable_name}
            
            print(f"      确认目标变量 (B列): '{actual_target_variable_name}'")
            print(f"      解析发布日期 (A列: '{date_col_name}')...")
            
            publication_dates = pd.to_datetime(df_raw[date_col_name], errors='coerce')
            valid_date_mask = publication_dates.notna()
            
            if not valid_date_mask.any():
                print(f"      错误: 无法从列 '{date_col_name}' 解析任何有效日期。跳过目标Sheet。")
                return None, None, None, set()
            
            publication_dates = publication_dates[valid_date_mask]
            
            debug_print(df_raw.tail()[[date_col_name, actual_target_variable_name]])
            debug_print(valid_date_mask.tail())
            
            # 提取目标变量值
            print(f"      提取目标变量原始值...")
            target_values = pd.to_numeric(df_raw.loc[valid_date_mask, actual_target_variable_name], errors='coerce')
            target_values.index = publication_dates
            
            # 更新映射
            norm_target_name = unicodedata.normalize('NFKC', actual_target_variable_name).strip().lower()
            self.var_industry_map[norm_target_name] = industry_name
            
            # 提取月度预测变量 (C列及以后)
            target_sheet_predictors = pd.DataFrame()
            if df_raw.shape[1] > 2:
                print(f"      提取目标 Sheet 的月度预测变量 (C列及以后)...")
                
                # 移除Unnamed列
                df_raw = self.cleaner.remove_unnamed_columns(df_raw, "[目标Sheet预测变量] ")
                
                temp_monthly_predictors = {}
                for col_idx in range(2, df_raw.shape[1]):
                    col_name = df_raw.columns[col_idx]
                    target_sheet_cols.add(col_name)
                    
                    # 清理值
                    cleaned_series = df_raw[col_name].astype(str).str.replace('%', '', regex=False).str.replace(',', '', regex=False).str.strip()
                    predictor_values = pd.to_numeric(cleaned_series, errors='coerce')
                    
                    # 创建按发布日期索引的序列
                    temp_monthly_predictors[col_name] = pd.Series(
                        predictor_values[valid_date_mask].values,
                        index=publication_dates
                    )
                    
                    # 更新映射和跟踪
                    norm_pred_col = unicodedata.normalize('NFKC', str(col_name)).strip().lower()
                    if norm_pred_col:
                        self.var_industry_map[norm_pred_col] = industry_name
                        self.raw_columns_across_all_sheets.add(norm_pred_col)
                
                target_sheet_predictors = pd.DataFrame(temp_monthly_predictors).sort_index()
                target_sheet_predictors = target_sheet_predictors.dropna(axis=1, how='all')
                
                print(f"      提取了 {target_sheet_predictors.shape[1]} 个有效的月度预测变量 (按发布日期索引)。")
            else:
                print(f"      目标 Sheet 仅含 A, B 列。")
            
            return publication_dates, target_values, target_sheet_predictors, target_sheet_cols
            
        except Exception as e:
            print(f"      加载或处理目标 Sheet '{sheet_name}' 出错: {e}. 跳过。")
            return None, None, None, set()
    
    def load_daily_weekly_sheet(
        self, 
        excel_file, 
        sheet_name: str,
        freq_type: str,
        industry_name: str
    ) -> Optional[pd.DataFrame]:
        """
        加载日度/周度数据表格
        
        Args:
            excel_file: Excel文件对象
            sheet_name: 表格名称
            freq_type: 频率类型 ('daily' 或 'weekly')
            industry_name: 行业名称
            
        Returns:
            Optional[pd.DataFrame]: 加载的数据，如果失败返回None
        """
        print(f"      检测到预测变量 Sheet ('{freq_type}', 行业: '{industry_name}')...")
        
        try:
            # 使用通用格式检测
            format_info = detect_sheet_format(excel_file, sheet_name)
            read_header = format_info['header']
            skip_rows = format_info['skiprows']
            read_index_col = 0  # 总是假设索引是第一列

            print(f"        [格式检测结果] {format_info['format']} (来源: {format_info['source']})")
            print(f"        [读取参数] header={read_header}, skiprows={skip_rows}, index_col={read_index_col}")

            df = pd.read_excel(excel_file, sheet_name=sheet_name, header=read_header, skiprows=skip_rows, index_col=read_index_col, parse_dates=True)

            # 清理数据
            df = self.cleaner.clean_zero_values(df, f"[{freq_type}] ")
            df = self.cleaner.remove_unnamed_columns(df, f"[{freq_type}] ")

            # 强制索引转换为日期时间
            print(f"      尝试将 '{sheet_name}' 的索引转换为日期时间...")
            original_index_len = len(df.index)
            df.index = pd.to_datetime(df.index, errors='coerce')
            
            if df is None or df.empty:
                return None
                
            df = df.loc[df.index.notna()]  # 过滤索引转换失败的行
            filtered_index_len = len(df.index)
            
            if filtered_index_len < original_index_len:
                print(f"      警告: 在 '{sheet_name}' 中移除了 {original_index_len - filtered_index_len} 行，因为它们的索引无法解析为有效日期。")

            df = df.dropna(axis=1, how='all')
            if df.empty:
                return None
                
            df_numeric = df.apply(pd.to_numeric, errors='coerce')
            if df_numeric.empty or df_numeric.isnull().all().all():
                return None
                
            print(f"      Sheet '{sheet_name}' ({industry_name}, {freq_type}) 加载完成。 Shape: {df_numeric.shape}")
            
            # 更新映射
            for col in df_numeric.columns:
                norm_col = unicodedata.normalize('NFKC', str(col)).strip().lower()
                if norm_col:
                    self.var_industry_map[norm_col] = industry_name
                    self.raw_columns_across_all_sheets.add(norm_col)
            
            return df_numeric
            
        except Exception as e:
            print(f"      加载或处理 {freq_type} Sheet '{sheet_name}' 时出错: {e}. 跳过。")
            return None

    def load_monthly_predictor_sheet(
        self,
        excel_file,
        sheet_name: str,
        industry_name: str
    ) -> Optional[pd.DataFrame]:
        """
        加载月度预测变量表格

        Args:
            excel_file: Excel文件对象
            sheet_name: 表格名称
            industry_name: 行业名称

        Returns:
            Optional[pd.DataFrame]: 加载的数据，如果失败返回None
        """
        print(f"      检测到非目标月度预测 Sheet，行业: '{industry_name}'...")

        try:
            # 使用通用格式检测
            format_info = detect_sheet_format(excel_file, sheet_name)
            read_header = format_info['header']
            skip_rows = format_info['skiprows']

            print(f"        [格式检测结果] {format_info['format']} (来源: {format_info['source']})")
            print(f"        [读取参数] header={read_header}, skiprows={skip_rows}")

            df_raw_pred = pd.read_excel(excel_file, sheet_name=sheet_name, header=read_header, skiprows=skip_rows)

            # 清理数据
            df_raw_pred = self.cleaner.remove_unnamed_columns(df_raw_pred, "[月度预测] ")
            df_raw_pred = self.cleaner.clean_zero_values(df_raw_pred, "[月度预测] ")

            if df_raw_pred.shape[1] < 2:
                print(f"      错误: 月度预测 Sheet '{sheet_name}' 列数 < 2。跳过。")
                return None

            date_col_name_pred = df_raw_pred.columns[0]
            print(f"      解析发布日期 (A列: '{date_col_name_pred}')...")

            publication_dates_predictor = pd.to_datetime(df_raw_pred[date_col_name_pred], errors='coerce')
            valid_date_mask_pred = publication_dates_predictor.notna()

            if not valid_date_mask_pred.any():
                print(f"      错误: 无法从列 '{date_col_name_pred}' 解析任何有效日期。跳过此Sheet。")
                return None

            publication_dates_predictor = publication_dates_predictor[valid_date_mask_pred]

            print(f"      提取月度预测变量 (B列及以后)...")
            temp_monthly_predictors_sheet = {}

            for col_idx_pred in range(1, df_raw_pred.shape[1]):  # 从B列开始
                col_name_pred = df_raw_pred.columns[col_idx_pred]

                # 清理值
                cleaned_series_pred = df_raw_pred[col_name_pred].astype(str).str.replace('%', '', regex=False).str.replace(',', '', regex=False).str.strip()
                predictor_values_pred = pd.to_numeric(cleaned_series_pred, errors='coerce')

                # 创建按发布日期索引的序列
                temp_monthly_predictors_sheet[col_name_pred] = pd.Series(
                    predictor_values_pred[valid_date_mask_pred].values,
                    index=publication_dates_predictor
                )

                # 更新映射和跟踪
                norm_pred_col_p = unicodedata.normalize('NFKC', str(col_name_pred)).strip().lower()
                if norm_pred_col_p:
                    self.var_industry_map[norm_pred_col_p] = industry_name
                    self.raw_columns_across_all_sheets.add(norm_pred_col_p)

            df_monthly_pred_sheet = pd.DataFrame(temp_monthly_predictors_sheet).sort_index()
            df_monthly_pred_sheet = df_monthly_pred_sheet.dropna(axis=1, how='all')

            if not df_monthly_pred_sheet.empty:
                print(f"      提取了 {df_monthly_pred_sheet.shape[1]} 个有效的月度预测变量 (按发布日期索引)。")
                return df_monthly_pred_sheet
            else:
                print("      此 Sheet 未包含有效的月度预测变量数据。")
                return None

        except Exception as e_pred:
            print(f"      加载或处理月度预测 Sheet '{sheet_name}' 出错: {e_pred}. 跳过。")
            return None

    def get_var_industry_map(self) -> Dict[str, str]:
        """获取变量行业映射"""
        return self.var_industry_map.copy()

    def get_raw_columns_set(self) -> Set[str]:
        """获取所有加载的列名集合（标准化后）"""
        return self.raw_columns_across_all_sheets.copy()

    def get_removed_variables_log(self) -> List[Dict]:
        """获取移除变量的日志"""
        return self.cleaner.get_removed_variables_log()

# 导出的类
__all__ = [
    'DataLoader'
]
