# -*- coding: utf-8 -*-
"""
并发安全管理器
提供线程安全的锁管理和死锁预防机制
"""

import threading
import time
import logging
from typing import Dict, Any, Optional, ContextManager
from contextlib import contextmanager
from collections import defaultdict


class ConcurrentSafetyManager:
    """并发安全管理器"""
    
    def __init__(self, state_manager):
        """
        初始化并发安全管理器
        
        Args:
            state_manager: 状态管理器实例
        """
        self.state_manager = state_manager
        self.logger = logging.getLogger(__name__)
        
        # 锁管理
        self._locks: Dict[str, threading.RLock] = {}
        self._lock_stats = defaultdict(int)
        self._lock_holders: Dict[str, int] = {}  # 锁持有者线程ID
        self._lock_acquisition_times: Dict[str, float] = {}
        
        # 主锁用于保护锁字典本身
        self._main_lock = threading.RLock()
        
        # 死锁检测
        self._lock_timeout = 30.0  # 默认锁超时时间
        self._deadlock_detection_enabled = True
        
        self.logger.info("ConcurrentSafetyManager initialized")
    
    def acquire_lock(self, lock_name: str, timeout: Optional[float] = None) -> bool:
        """
        获取指定名称的锁
        
        Args:
            lock_name: 锁名称
            timeout: 超时时间（秒）
            
        Returns:
            是否成功获取锁
        """
        if timeout is None:
            timeout = self._lock_timeout
        
        try:
            # 获取或创建锁
            with self._main_lock:
                if lock_name not in self._locks:
                    self._locks[lock_name] = threading.RLock()
                lock = self._locks[lock_name]
            
            # 尝试获取锁
            start_time = time.time()
            acquired = lock.acquire(timeout=timeout)
            
            if acquired:
                thread_id = threading.get_ident()
                self._lock_holders[lock_name] = thread_id
                self._lock_acquisition_times[lock_name] = start_time
                self._lock_stats[f"{lock_name}_acquired"] += 1
                
                self.logger.debug(f"Lock '{lock_name}' acquired by thread {thread_id}")
                return True
            else:
                self._lock_stats[f"{lock_name}_timeout"] += 1
                self.logger.warning(f"Failed to acquire lock '{lock_name}' within {timeout}s")
                return False
                
        except Exception as e:
            self._lock_stats[f"{lock_name}_error"] += 1
            self.logger.error(f"Error acquiring lock '{lock_name}': {e}")
            return False
    
    def release_lock(self, lock_name: str) -> bool:
        """
        释放指定名称的锁
        
        Args:
            lock_name: 锁名称
            
        Returns:
            是否成功释放锁
        """
        try:
            with self._main_lock:
                if lock_name not in self._locks:
                    self.logger.warning(f"Attempting to release non-existent lock '{lock_name}'")
                    return False
                
                lock = self._locks[lock_name]
            
            # 释放锁
            lock.release()
            
            # 清理锁持有者信息
            if lock_name in self._lock_holders:
                thread_id = self._lock_holders.pop(lock_name)
                self.logger.debug(f"Lock '{lock_name}' released by thread {thread_id}")
            
            if lock_name in self._lock_acquisition_times:
                acquisition_time = self._lock_acquisition_times.pop(lock_name)
                hold_duration = time.time() - acquisition_time
                self._lock_stats[f"{lock_name}_hold_time"] += hold_duration
            
            self._lock_stats[f"{lock_name}_released"] += 1
            return True
            
        except Exception as e:
            self._lock_stats[f"{lock_name}_release_error"] += 1
            self.logger.error(f"Error releasing lock '{lock_name}': {e}")
            return False
    
    @contextmanager
    def with_lock(self, lock_name: str, timeout: Optional[float] = None):
        """
        锁上下文管理器
        
        Args:
            lock_name: 锁名称
            timeout: 超时时间（秒）
            
        Yields:
            锁上下文
            
        Raises:
            TimeoutError: 获取锁超时
        """
        acquired = self.acquire_lock(lock_name, timeout)
        if not acquired:
            raise TimeoutError(f"Failed to acquire lock '{lock_name}' within {timeout}s")
        
        try:
            yield
        finally:
            self.release_lock(lock_name)
    
    def get_lock_stats(self) -> Dict[str, Any]:
        """
        获取锁统计信息
        
        Returns:
            锁统计信息字典
        """
        with self._main_lock:
            stats = {
                'total_locks': len(self._locks),
                'active_locks': len(self._lock_holders),
                'lock_holders': self._lock_holders.copy(),
                'lock_stats': dict(self._lock_stats),
                'deadlock_detection_enabled': self._deadlock_detection_enabled,
                'default_timeout': self._lock_timeout
            }
            
            # 计算平均持有时间
            for lock_name in self._locks:
                acquired_key = f"{lock_name}_acquired"
                hold_time_key = f"{lock_name}_hold_time"
                
                if acquired_key in self._lock_stats and self._lock_stats[acquired_key] > 0:
                    avg_hold_time = (self._lock_stats.get(hold_time_key, 0) / 
                                   self._lock_stats[acquired_key])
                    stats[f"{lock_name}_avg_hold_time"] = avg_hold_time
            
            return stats
    
    def detect_potential_deadlock(self) -> bool:
        """
        检测潜在的死锁情况
        
        Returns:
            是否检测到潜在死锁
        """
        if not self._deadlock_detection_enabled:
            return False
        
        current_time = time.time()
        potential_deadlocks = []
        
        with self._main_lock:
            for lock_name, acquisition_time in self._lock_acquisition_times.items():
                hold_duration = current_time - acquisition_time
                
                # 如果锁持有时间超过阈值，可能存在死锁
                if hold_duration > self._lock_timeout * 0.8:
                    thread_id = self._lock_holders.get(lock_name)
                    potential_deadlocks.append({
                        'lock_name': lock_name,
                        'thread_id': thread_id,
                        'hold_duration': hold_duration
                    })
        
        if potential_deadlocks:
            self.logger.warning(f"Potential deadlock detected: {potential_deadlocks}")
            return True
        
        return False
    
    def force_release_all_locks(self) -> int:
        """
        强制释放所有锁（紧急情况使用）
        
        Returns:
            释放的锁数量
        """
        released_count = 0
        
        with self._main_lock:
            lock_names = list(self._locks.keys())
        
        for lock_name in lock_names:
            try:
                if self.release_lock(lock_name):
                    released_count += 1
            except Exception as e:
                self.logger.error(f"Failed to force release lock '{lock_name}': {e}")
        
        self.logger.warning(f"Force released {released_count} locks")
        return released_count
    
    def set_timeout(self, timeout: float):
        """设置默认锁超时时间"""
        self._lock_timeout = timeout
        self.logger.info(f"Lock timeout set to {timeout}s")
    
    def enable_deadlock_detection(self, enabled: bool = True):
        """启用或禁用死锁检测"""
        self._deadlock_detection_enabled = enabled
        self.logger.info(f"Deadlock detection {'enabled' if enabled else 'disabled'}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.force_release_all_locks()
            with self._main_lock:
                self._locks.clear()
                self._lock_holders.clear()
                self._lock_acquisition_times.clear()
            self.logger.info("ConcurrentSafetyManager cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
