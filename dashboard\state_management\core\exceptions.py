# -*- coding: utf-8 -*-
"""
状态管理异常类
定义状态管理模块的自定义异常
"""


class StateManagerError(Exception):
    """状态管理器基础异常"""
    pass


class CircularDependencyError(StateManagerError):
    """循环依赖异常"""
    pass


class StateValidationError(StateManagerError):
    """状态验证异常"""
    pass


class AdapterRegistrationError(StateManagerError):
    """适配器注册异常"""
    pass


class StateNotFoundError(StateManagerError):
    """状态未找到异常"""
    pass


class ConcurrencyError(StateManagerError):
    """并发操作异常"""
    pass
