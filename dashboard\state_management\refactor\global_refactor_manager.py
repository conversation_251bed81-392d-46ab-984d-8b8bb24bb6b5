# -*- coding: utf-8 -*-
"""
全局重构器管理器
提供全局单例的重构器实例，避免重复创建和初始化
"""

import streamlit as st
from typing import Optional

from .dfm_module_refactor import DFMModuleRefactor
from .tools_module_refactor import ToolsModuleRefactor
from .preview_module_refactor import PreviewModuleRefactor
from .navigation_module_refactor import NavigationModuleRefactor
from ..unified_state_manager import UnifiedStateManager


class GlobalRefactorManager:
    """全局重构器管理器 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._dfm_refactor = None
            self._tools_refactor = None
            self._preview_refactor = None
            self._navigation_refactor = None
            self._unified_manager = None
            GlobalRefactorManager._initialized = True
    
    def _get_unified_manager(self) -> Optional[UnifiedStateManager]:
        """获取统一状态管理器实例"""
        if self._unified_manager is None:
            from .. import get_unified_manager
            self._unified_manager = get_unified_manager()
        return self._unified_manager
    
    def get_dfm_refactor(self) -> Optional[DFMModuleRefactor]:
        """获取DFM重构器实例（全局单例）"""
        if self._dfm_refactor is None:
            unified_manager = self._get_unified_manager()
            if unified_manager is not None:
                self._dfm_refactor = DFMModuleRefactor(unified_manager)
        return self._dfm_refactor
    
    def get_tools_refactor(self) -> Optional[ToolsModuleRefactor]:
        """获取工具重构器实例（全局单例）"""
        if self._tools_refactor is None:
            unified_manager = self._get_unified_manager()
            if unified_manager is not None:
                self._tools_refactor = ToolsModuleRefactor(unified_manager)
        return self._tools_refactor
    
    def get_preview_refactor(self) -> Optional[PreviewModuleRefactor]:
        """获取预览重构器实例（全局单例）"""
        if self._preview_refactor is None:
            unified_manager = self._get_unified_manager()
            if unified_manager is not None:
                self._preview_refactor = PreviewModuleRefactor(unified_manager)
        return self._preview_refactor
    
    def get_navigation_refactor(self) -> Optional[NavigationModuleRefactor]:
        """获取导航重构器实例（全局单例）"""
        if self._navigation_refactor is None:
            unified_manager = self._get_unified_manager()
            if unified_manager is not None:
                self._navigation_refactor = NavigationModuleRefactor(unified_manager)
        return self._navigation_refactor
    
    def reset(self):
        """重置所有重构器实例（用于测试或重新初始化）"""
        self._dfm_refactor = None
        self._tools_refactor = None
        self._preview_refactor = None
        self._navigation_refactor = None
        self._unified_manager = None


# 全局管理器实例和线程锁
import threading
_global_manager = None
_global_manager_lock = threading.Lock()


def _get_global_manager():
    """获取全局管理器实例（线程安全单例）"""
    global _global_manager
    if _global_manager is None:
        with _global_manager_lock:
            if _global_manager is None:
                _global_manager = GlobalRefactorManager()
    return _global_manager


# 便捷的全局函数（移除缓存装饰器，改用线程安全单例）
def get_global_dfm_refactor():
    """获取全局DFM重构器实例（线程安全）"""
    manager = _get_global_manager()
    return manager.get_dfm_refactor()


def get_global_tools_refactor():
    """获取全局工具重构器实例（线程安全）"""
    manager = _get_global_manager()
    return manager.get_tools_refactor()


def get_global_preview_refactor():
    """获取全局预览重构器实例（线程安全）"""
    manager = _get_global_manager()
    return manager.get_preview_refactor()


def get_global_navigation_refactor():
    """获取全局导航重构器实例（线程安全）"""
    manager = _get_global_manager()
    return manager.get_navigation_refactor()


def reset_global_refactors():
    """重置所有全局重构器实例"""
    _global_manager.reset()
    # 清除Streamlit缓存
    st.cache_resource.clear()
