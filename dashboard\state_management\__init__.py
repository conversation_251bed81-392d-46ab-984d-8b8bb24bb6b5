# -*- coding: utf-8 -*-
"""
统一状态管理系统
提供跨模块的统一状态管理、数据流控制和性能监控
"""

# 导入统一状态管理器（主要接口）
from .unified_state_manager import UnifiedStateManager

# 导入新的统一接口和工具
from .interface import (
    StateManagerInterface,
    get_state_interface,
    get_state,
    set_state,
    clear_state
)
from .key_standards import (
    ModulePrefix,
    StateKeyStandards,
    get_key_standards
)
from .migration_tool import (
    StateMigrationTool,
    get_migration_tool,
    analyze_migration_needs,
    migrate_session_state
)

# 导入重构模块（最重要的部分）
# 注意：现在重构模块不再依赖get_unified_manager，避免了循环导入
try:
    from .refactor import (
        ModuleRefactorBase,
        DFMModuleRefactor,
        ToolsModuleRefactor,
        PreviewModuleRefactor,
        NavigationModuleRefactor
    )
    REFACTOR_AVAILABLE = True
except ImportError as e:
    REFACTOR_AVAILABLE = False

# 可选导入核心基础设施
try:
    from .core import (
        ModuleManager,
        DataFlowController,
        StateSynchronizer,
        PerformanceMonitor,
        ErrorHandler,
        ConfigManager,
        LoggingMonitor
    )
    CORE_AVAILABLE = True
except ImportError as e:
    CORE_AVAILABLE = False

# 可选导入适配器（仅保留实际使用的）
try:
    from .adapters import (
        PreviewAdapter,
        DFMAdapter,
        ToolsAdapter,
        NavigationAdapter
    )
    ADAPTERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 适配器模块导入失败: {e}")
    ADAPTERS_AVAILABLE = False

__version__ = "1.0.0"
__author__ = "HFTA Development Team"

# 动态构建__all__列表
__all__ = [
    'UnifiedStateManager',
    'get_unified_manager',
    # 新的统一接口
    'StateManagerInterface',
    'get_state_interface',
    'get_state',
    'set_state',
    'clear_state',
    # 键标准化
    'ModulePrefix',
    'StateKeyStandards',
    'get_key_standards',
    # 迁移工具
    'StateMigrationTool',
    'get_migration_tool',
    'analyze_migration_needs',
    'migrate_session_state'
]

if REFACTOR_AVAILABLE:
    __all__.extend([
        'ModuleRefactorBase',
        'DFMModuleRefactor',
        'ToolsModuleRefactor',
        'PreviewModuleRefactor',
        'NavigationModuleRefactor'
    ])

if CORE_AVAILABLE:
    __all__.extend([
        'ModuleManager',
        'DataFlowController',
        'StateSynchronizer',
        'PerformanceMonitor',
        'ErrorHandler',
        'ConfigManager',
        'LoggingMonitor'
    ])

if ADAPTERS_AVAILABLE:
    __all__.extend([
        'PreviewAdapter',
        'DFMAdapter',
        'ToolsAdapter',
        'NavigationAdapter'
    ])

# 模块级别的便捷函数
_unified_manager_instance = None
_initialization_lock = False

def get_unified_manager():
    """获取统一状态管理器实例（单例模式）"""
    import os

    # 在多进程环境中禁用状态管理系统
    if os.getenv('DISABLE_STATE_MANAGEMENT', 'false').lower() == 'true':
        return None

    global _unified_manager_instance, _initialization_lock

    # 减少调试日志输出 - 只在首次创建时打印详细信息
    if _unified_manager_instance is not None:
        return _unified_manager_instance

    # 防止重复初始化
    if _initialization_lock:
        return _unified_manager_instance

    _initialization_lock = True
    try:
        _unified_manager_instance = UnifiedStateManager()
        # 确保logger属性存在
        if not hasattr(_unified_manager_instance, 'logger'):
            import logging
            _unified_manager_instance.logger = logging.getLogger('unified_state_manager')
    except Exception as e:
        print(f"[ERROR StateManager] 统一状态管理器初始化失败: {e}")
        # 不使用fallback，直接抛出异常让调用者处理
        _unified_manager_instance = None
        raise RuntimeError(f"统一状态管理器初始化失败: {e}")
    finally:
        _initialization_lock = False

    return _unified_manager_instance
