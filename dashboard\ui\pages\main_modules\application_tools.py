# -*- coding: utf-8 -*-
"""
应用工具主模块欢迎页面
"""

import streamlit as st
from typing import List
from ...components.base import UIComponent
from ...components.cards import ModuleCard, InfoCard
from ...constants import UIConstants

class ApplicationToolsWelcomePage(UIComponent):
    """应用工具欢迎页面"""
    
    def __init__(self):
        self.constants = UIConstants
        self.module_config = self.constants.MAIN_MODULES["应用工具"]
    
    def render(self, st_obj, **kwargs) -> None:
        """渲染应用工具欢迎页面"""
        # 处理导航事件
        self._handle_navigation(st_obj)

        # 只显示标题和介绍
        self._render_header(st_obj)

        # 渲染子模块选择
        self._render_sub_modules(st_obj)
    
    def _render_header(self, st_obj):
        """渲染页面头部"""
        st_obj.markdown(f"""
        <div style="text-align: center; padding: 3rem 0 2rem 0;">
            <div style="font-size: 4em; margin-bottom: 1rem;">{self.module_config['icon']}</div>
            <h1 style="color: #333; margin-bottom: 1rem; font-weight: 700;">应用工具</h1>
            <p style="font-size: 1.3em; color: #666; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                {self.module_config['description']}
            </p>
        </div>
        """, unsafe_allow_html=True)

    def _render_sub_modules(self, st_obj):
        """渲染子模块选择卡片"""
        st_obj.markdown("### 选择工具类型")

        # 创建两列布局
        col1, col2 = st_obj.columns(2)

        with col1:
            # 数据探索卡片
            if st_obj.button(
                "🔍 数据探索",
                key="nav_data_exploration",
                use_container_width=True,
                help="进行数据的平稳性分析、相关性分析和领先滞后分析"
            ):
                # 使用统一状态管理器
                try:
                    from dashboard.state_management import get_unified_manager
                    import time
                    state_manager = get_unified_manager()
                    if state_manager:
                        # 设置导航时间戳，用于循环渲染检测
                        current_time = time.time()
                        state_manager.set_state('dashboard.last_navigation_time', current_time)
                        state_manager.set_state('navigation.navigate_to_sub_module', '数据探索')
                    else:
                        # 降级到session_state
                        st_obj.session_state['navigate_to_sub_module'] = '数据探索'
                except ImportError:
                    # 降级到session_state
                    st_obj.session_state['navigate_to_sub_module'] = '数据探索'

        with col2:
            # 数据预处理卡片
            if st_obj.button(
                "🧹 数据预处理",
                key="nav_data_preprocessing",
                use_container_width=True,
                help="进行数据清洗、变量计算、数据追加与合并等预处理操作"
            ):
                # 使用统一状态管理器
                try:
                    from dashboard.state_management import get_unified_manager
                    import time
                    state_manager = get_unified_manager()
                    if state_manager:
                        # 设置导航时间戳，用于循环渲染检测
                        current_time = time.time()
                        state_manager.set_state('dashboard.last_navigation_time', current_time)
                        state_manager.set_state('navigation.navigate_to_sub_module', '数据预处理')
                    else:
                        # 降级到session_state
                        st_obj.session_state['navigate_to_sub_module'] = '数据预处理'
                except ImportError:
                    # 降级到session_state
                    st_obj.session_state['navigate_to_sub_module'] = '数据预处理'

        # 添加一些说明文字
        st_obj.markdown("""
        <div style="margin-top: 2rem; padding: 1rem; background-color: #f8f9fa; border-radius: 0.5rem;">
            <h4 style="color: #495057; margin-bottom: 0.5rem;">💡 使用提示</h4>
            <ul style="color: #6c757d; margin-bottom: 0;">
                <li><strong>数据探索</strong>：适用于分析数据的统计特性和时间序列特征</li>
                <li><strong>数据预处理</strong>：适用于数据清洗、转换和准备工作</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    def _navigate_to_sub_module(self, st_obj, sub_module: str):
        """导航到子模块"""
        try:
            # 使用统一状态管理器设置导航状态
            from dashboard.state_management import get_unified_manager
            from dashboard.state_management.refactor.navigation_module_refactor import NavigationModuleRefactor

            unified_manager = get_unified_manager()
            if not unified_manager:
                st_obj.error("统一状态管理器不可用")
                return

            nav_manager = NavigationModuleRefactor(unified_manager)

            # 特殊处理：如果是导航到数据探索，需要清除之前的状态
            if sub_module == "数据探索":
                try:
                    from dashboard.state_management.adapters.data_exploration_adapter import get_data_exploration_adapter
                    adapter = get_data_exploration_adapter()
                    if adapter:
                        # 使用统一状态管理器设置导航标志
                        if unified_manager:
                            unified_manager.set_state('navigation.sidebar_navigation_return', True)
                            unified_manager.set_state('navigation.data_exploration_just_cleared', True)
                        else:
                            # 统一状态管理器不可用时，抛出错误
                            raise RuntimeError("统一状态管理器不可用，无法设置导航标志")

                        # 强制清除当前活跃的分析模块状态
                        unified_manager.set_state('navigation.clear_data_exploration_active_tab', True)

                        # 设置导航初始化标志为False，确保重新初始化
                        unified_manager.set_state('navigation.data_exploration_initialized', False)

                        # 关键修复：设置清除状态标志，确保TabDetector返回None
                        adapter.unified_manager.set_state(adapter.CLEARING_STATE_KEY, True)

                        # 清除所有数据探索状态，确保显示欢迎页面
                        adapter.clear_all_states()

                        # 设置强制卡片视图标志
                        adapter.unified_manager.set_state(adapter.FORCE_CARD_VIEW_KEY, True)

                        # 移除立即rerun，避免循环渲染

                except Exception as e:
                    print(f"清除数据探索状态失败: {e}")

            # 设置子模块
            nav_manager.set_current_sub_module(sub_module)

            # 使用统一状态管理器设置导航待处理标志
            try:
                from dashboard.state_management import get_unified_manager
                state_manager = get_unified_manager()
                if state_manager:
                    state_manager.set_state('navigation._navigation_pending', True)
                else:
                    # 降级到session_state
                    st_obj.session_state['_navigation_pending'] = True
            except ImportError:
                # 降级到session_state
                st_obj.session_state['_navigation_pending'] = True

        except Exception as e:
            st_obj.error(f"导航失败: {e}")

    def _handle_navigation(self, st_obj):
        """处理导航事件"""
        try:
            from dashboard.state_management import get_unified_manager
            state_manager = get_unified_manager()

            # 检查是否有导航请求
            if state_manager:
                sub_module = state_manager.get_state('navigation.navigate_to_sub_module')
                if sub_module:
                    state_manager.clear_state('navigation.navigate_to_sub_module')
                    self._navigate_to_sub_module(st_obj, sub_module)

                # 检查是否有待处理的导航
                if state_manager.get_state('navigation._navigation_pending', False):
                    state_manager.set_state('navigation._navigation_pending', False)
                    st_obj.rerun()
            else:
                # 降级到session_state
                if 'navigate_to_sub_module' in st_obj.session_state:
                    sub_module = st_obj.session_state.pop('navigate_to_sub_module')
                    self._navigate_to_sub_module(st_obj, sub_module)

                # 检查是否有待处理的导航
                if st_obj.session_state.get('_navigation_pending', False):
                    st_obj.session_state['_navigation_pending'] = False
                    st_obj.rerun()
        except ImportError:
            # 降级到session_state
            if 'navigate_to_sub_module' in st_obj.session_state:
                sub_module = st_obj.session_state.pop('navigate_to_sub_module')
                self._navigate_to_sub_module(st_obj, sub_module)

            # 检查是否有待处理的导航
            if st_obj.session_state.get('_navigation_pending', False):
                st_obj.session_state['_navigation_pending'] = False
                st_obj.rerun()
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return ['navigate_to_sub_module']
