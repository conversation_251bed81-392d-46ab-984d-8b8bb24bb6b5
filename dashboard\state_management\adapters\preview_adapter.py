# -*- coding: utf-8 -*-
"""
Preview模块适配器
为数据预览模块提供统一状态管理适配功能
"""

import pandas as pd
from typing import Any, Dict, List, Optional
import logging
from .base_adapter import BaseAdapter
from ..core.module_manager import ModuleManager
from ..core.data_flow_controller import DataFlowController
from ..core.state_synchronizer import StateSynchronizer
from ..core.performance_monitor import PerformanceMonitor
from ..core.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from ..core.config_manager import ConfigManager
from ..core.logging_monitor import LoggingMonitor
from ..core.state_metadata import StateScope, DataType

logger = logging.getLogger(__name__)

class PreviewAdapter(BaseAdapter):
    """Preview模块适配器"""
    
    # 预览模块的状态键映射
    STATE_KEY_MAPPING = {
        # 主要数据
        'preview_data_loaded_files': 'loaded_files',
        'preview_weekly_df': 'weekly_df',
        'preview_monthly_df': 'monthly_df', 
        'preview_daily_df': 'daily_df',
        'preview_source_map': 'source_map',
        'preview_indicator_industry_map': 'indicator_industry_map',
        
        # 行业数据
        'preview_weekly_industries': 'weekly_industries',
        'preview_monthly_industries': 'monthly_industries',
        'preview_daily_industries': 'daily_industries',
        'preview_clean_industry_map': 'clean_industry_map',
        
        # 缓存数据
        'preview_weekly_summary_cache': 'weekly_summary_cache',
        'preview_monthly_summary_cache': 'monthly_summary_cache',
        'preview_daily_summary_cache': 'daily_summary_cache',
        'preview_monthly_growth_summary_df': 'monthly_growth_summary_df',
        
        # 向后兼容的旧键名
        'data_loaded': 'loaded_files',
        'weekly_df': 'weekly_df',
        'monthly_df': 'monthly_df',
        'daily_df': 'daily_df',
        'source_map': 'source_map',
        'indicator_industry_map': 'indicator_industry_map',
        'weekly_industries': 'weekly_industries',
        'monthly_industries': 'monthly_industries',
        'clean_industry_map': 'clean_industry_map',
        'weekly_summary_cache': 'weekly_summary_cache',
        'monthly_summary_cache': 'monthly_summary_cache'
    }
    
    def __init__(
        self,
        state_manager,
        module_name: str = "preview",
        # 保持向后兼容的可选参数
        module_manager: Optional[ModuleManager] = None,
        data_flow_controller: Optional[DataFlowController] = None,
        state_synchronizer: Optional[StateSynchronizer] = None,
        performance_monitor: Optional[PerformanceMonitor] = None,
        error_handler: Optional[ErrorHandler] = None,
        config_manager: Optional[ConfigManager] = None,
        logging_monitor: Optional[LoggingMonitor] = None
    ):
        """
        初始化Preview适配器

        Args:
            state_manager: 统一状态管理器
            module_name: 模块名称
            其他参数: 向后兼容的可选依赖组件
        """
        # 调用基类构造函数
        super().__init__(state_manager, module_name)

        # 保存依赖组件（向后兼容）
        self.module_manager = module_manager
        self.data_flow_controller = data_flow_controller
        self.state_synchronizer = state_synchronizer
        self.performance_monitor = performance_monitor
        self.error_handler = error_handler
        self.config_manager = config_manager
        self.logging_monitor = logging_monitor

    def _setup_module(self):
        """模块特定的初始化"""
        # 设置日志记录器
        if hasattr(self, 'logging_monitor') and self.logging_monitor:
            self.logger = self.logging_monitor.get_logger("preview_adapter")
        else:
            self.logger = logger

        # 注册模块（如果模块管理器可用）
        if hasattr(self, 'module_manager') and self.module_manager:
            self.module_manager.register_module(self.module_name)

        self.logger.info(f"PreviewAdapter initialized for module: {self.module_name}")
    
    def _get_unified_key(self, key: str) -> str:
        """
        获取统一的状态键名
        
        Args:
            key: 原始键名
            
        Returns:
            str: 统一的键名
        """
        mapped_key = self.STATE_KEY_MAPPING.get(key, key)
        return f"preview_{mapped_key}"
    
    def set_data(self, key: str, value: Any, description: str = "") -> bool:
        """
        设置预览数据

        Args:
            key: 状态键
            value: 状态值
            description: 描述信息

        Returns:
            bool: 设置是否成功
        """
        try:
            unified_key = self._get_unified_key(key)

            # 只使用核心管理器存储数据
            success = self.core_manager.set_state(unified_key, value)
            if success:
                # 也存储原始键以保持兼容性
                self.core_manager.set_state(key, value)
                self.logger.debug(f"Set preview data: {key} -> {unified_key}")
                return True
            else:
                self.logger.error(f"Core manager failed to set {unified_key}")
                return False

        except Exception as e:
            self.logger.error(f"Failed to set preview data {key}: {e}")
            return False
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """
        获取预览数据

        Args:
            key: 状态键
            default: 默认值

        Returns:
            Any: 状态值
        """
        try:
            unified_key = self._get_unified_key(key)

            # 优先从统一键获取，然后回退到原始键
            value = self.core_manager.get_state(unified_key, None)
            if value is None:
                value = self.core_manager.get_state(key, default)
            else:
                # 如果从统一键获取到了值，但是None，则使用默认值
                if value is None:
                    value = default

            self.logger.debug(f"Get preview data: {key} -> {unified_key}")
            return value

        except Exception as e:
            self.logger.error(f"Failed to get preview data {key}: {e}")
            return default
    
    def has_data(self, key: str) -> bool:
        """
        检查预览数据是否存在
        
        Args:
            key: 状态键
            
        Returns:
            bool: 数据是否存在
        """
        unified_key = self._get_unified_key(key)
        return self.core_manager.has_state(unified_key)
    
    def delete_data(self, key: str) -> bool:
        """
        删除预览数据
        
        Args:
            key: 状态键
            
        Returns:
            bool: 删除是否成功
        """
        unified_key = self._get_unified_key(key)
        success = self.core_manager.delete_state(unified_key)
        
        if success:
            self.logger.debug(f"Deleted preview data: {key} -> {unified_key}")
        
        return success
    
    def clear_all_data(self, exclude_shared: bool = True) -> bool:
        """
        清除所有预览数据

        Args:
            exclude_shared: 是否排除共享数据

        Returns:
            bool: 清除是否成功
        """
        # 清除所有preview相关的状态
        try:
            # 定义需要清除的preview相关键
            preview_keys = [
                'preview_data_loaded_files', 'preview_weekly_df', 'preview_monthly_df',
                'preview_daily_df', 'preview_industrial_df', 'preview_source_map',
                'preview_clean_industry_map', 'preview_weekly_industries',
                'preview_monthly_industries', 'preview_daily_industries',
                'preview_industrial_industries', 'preview_weekly_summary_cache',
                'preview_monthly_summary_cache', 'preview_daily_summary_cache',
                'preview_industrial_summary_cache', 'preview_full_weekly_summary',
                'preview_full_monthly_summary', 'preview_full_industrial_summary'
            ]

            # 使用核心管理器清除状态
            for key in preview_keys:
                self.core_manager.clear_state(key)
                # 也清除统一键格式
                unified_key = self._get_unified_key(key)
                self.core_manager.clear_state(unified_key)

            self.logger.info("Cleared all preview data")
            return True

        except Exception as e:
            self.logger.error(f"Failed to clear preview data: {e}")
            return False
    
    def load_industrial_data(self, weekly_df: pd.DataFrame, monthly_df: pd.DataFrame,
                           daily_df: pd.DataFrame, source_map: Dict, 
                           indicator_map: Dict, filename: str) -> bool:
        """
        加载工业数据的便捷方法
        
        Args:
            weekly_df: 周度数据
            monthly_df: 月度数据
            daily_df: 日度数据
            source_map: 数据源映射
            indicator_map: 指标行业映射
            filename: 文件名
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 设置主要数据
            self.set_data('preview_weekly_df', weekly_df, "周度工业数据")
            self.set_data('preview_monthly_df', monthly_df, "月度工业数据")
            self.set_data('preview_daily_df', daily_df, "日度工业数据")
            self.set_data('preview_source_map', source_map, "数据源映射")
            self.set_data('preview_indicator_industry_map', indicator_map, "指标行业映射")
            self.set_data('preview_data_loaded_files', filename, "已加载文件名")
            
            # 初始化缓存
            self.set_data('preview_weekly_summary_cache', {}, "周度摘要缓存")
            self.set_data('preview_monthly_summary_cache', {}, "月度摘要缓存")
            self.set_data('preview_daily_summary_cache', {}, "日度摘要缓存")
            
            self.logger.info(f"Industrial data loaded successfully: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load industrial data: {e}")
            return False
    
    def set_industry_data(self, weekly_industries: List[str], 
                         monthly_industries: List[str],
                         daily_industries: List[str],
                         clean_industry_map: Dict) -> bool:
        """
        设置行业数据的便捷方法
        
        Args:
            weekly_industries: 周度行业列表
            monthly_industries: 月度行业列表
            daily_industries: 日度行业列表
            clean_industry_map: 清理后的行业映射
            
        Returns:
            bool: 设置是否成功
        """
        try:
            self.set_data('preview_weekly_industries', weekly_industries, "周度行业列表")
            self.set_data('preview_monthly_industries', monthly_industries, "月度行业列表")
            self.set_data('preview_daily_industries', daily_industries, "日度行业列表")
            self.set_data('preview_clean_industry_map', clean_industry_map, "清理后的行业映射")
            
            self.logger.info("Industry data set successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set industry data: {e}")
            return False
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        获取预览数据摘要
        
        Returns:
            Dict[str, Any]: 数据摘要
        """
        weekly_df = self.get_data('preview_weekly_df', pd.DataFrame())
        monthly_df = self.get_data('preview_monthly_df', pd.DataFrame())
        daily_df = self.get_data('preview_daily_df', pd.DataFrame())
        
        return {
            'loaded_file': self.get_data('preview_data_loaded_files'),
            'weekly_indicators': len(weekly_df.columns) if not weekly_df.empty else 0,
            'monthly_indicators': len(monthly_df.columns) if not monthly_df.empty else 0,
            'daily_indicators': len(daily_df.columns) if not daily_df.empty else 0,
            'weekly_industries': len(self.get_data('preview_weekly_industries', [])),
            'monthly_industries': len(self.get_data('preview_monthly_industries', [])),
            'daily_industries': len(self.get_data('preview_daily_industries', [])),
            'has_data': bool(self.get_data('preview_data_loaded_files'))
        }
    
    def share_data_to_module(self, target_module: str, 
                           data_mapping: Optional[Dict[str, str]] = None) -> bool:
        """
        将预览数据共享给其他模块
        
        Args:
            target_module: 目标模块名称
            data_mapping: 数据映射规则 {source_key: target_key}
            
        Returns:
            bool: 共享是否成功
        """
        if data_mapping is None:
            # 默认的数据共享映射
            data_mapping = {
                'preview_weekly_df': f'{target_module}_weekly_df',
                'preview_monthly_df': f'{target_module}_monthly_df',
                'preview_daily_df': f'{target_module}_daily_df',
                'preview_source_map': f'{target_module}_source_map',
                'preview_indicator_industry_map': f'{target_module}_indicator_industry_map'
            }
        
        success_count = 0
        for source_key, target_key in data_mapping.items():
            unified_source_key = self._get_unified_key(source_key)
            if self.core_manager.has_state(unified_source_key):
                # 简化的数据共享：直接复制数据到目标键
                source_data = self.core_manager.get_state(unified_source_key)
                success = self.core_manager.set_state(target_key, source_data)
                if success:
                    success_count += 1
        
        self.logger.info(f"Shared {success_count}/{len(data_mapping)} data items to {target_module}")
        return success_count == len(data_mapping)
