# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Chinese Economic Analysis Platform (经济运行分析平台)** - a sophisticated Streamlit-based dashboard for economic data analysis and monitoring, focusing on industrial and consumer economic indicators.

## Running the Application

```bash
# Main entry point
streamlit run dashboard/dashboard.py
```

## Architecture Overview

### Core Components

1. **Dashboard Core** (`dashboard/dashboard.py`)
   - Main application entry point with unified state management
   - Module configuration and navigation routing
   - CSS injection and styling system

2. **State Management** (`dashboard/state_management/`)
   - Unified state manager for cross-module data sharing
   - Thread-safe concurrent operations
   - Performance monitoring and caching

3. **DFM Module** (`dashboard/DFM/`)
   - Dynamic Factor Model implementation
   - Data preparation, model training, and analysis
   - News analysis integration

4. **UI Framework** (`dashboard/ui/`)
   - Component-based architecture with lazy loading
   - Navigation system with main/sub module routing
   - Responsive styling and theme management

### Key Dependencies

```python
streamlit      # Web dashboard framework
pandas         # Data manipulation
numpy          # Numerical computing
scipy          # Scientific computing
scikit-learn   # Machine learning
statsmodels    # Statistical modeling
matplotlib     # Plotting
altair         # Declarative visualization
```

## Module Navigation Structure

```python
MODULE_CONFIG = {
    "数据预览": {
        "工业": None,
        "消费": None,
    },
    "监测分析": {
        "工业": ["工业增加值", "工业企业利润拆解"],
        "消费": ["宏观运行", "企业经营"]
    },
    "模型分析": {
        "DFM 模型": ["数据准备", "模型训练", "模型分析", "新闻分析"]
    },
    "应用工具": {
        "数据预处理": ["数据清洗", "变量计算", "数据追加与合并", "数据比较"],
        "数据探索": ["平稳性分析", "相关分析"]
    }
}
```

## State Management Pattern

The application uses a sophisticated unified state management system:

```python
from dashboard.state_management import get_unified_manager
state_manager = get_unified_manager()

# Setting state
state_manager.set_state('navigation.main_module', '数据预览')

# Getting state
current_module = state_manager.get_state('navigation.main_module', '数据预览')
```

## Performance Considerations

1. **Lazy Loading**: Components are loaded on-demand via `core.lazy_loader`
2. **Caching**: Extensive use of `@st.cache_data` for data operations
3. **State Synchronization**: Navigation state is carefully managed to prevent render loops
4. **CSS Injection**: Styles are injected once per process to avoid redundant operations

## Data Processing Pipeline

1. **Data Input**: Excel/CSV files in `data/` directory
2. **Preprocessing**: Time series cleaning, frequency alignment, missing data handling
3. **Analysis**: DFM modeling, correlation analysis, stationarity tests
4. **Visualization**: Interactive charts using Plotly/Altair

## Important Notes

- **Language**: All UI text and documentation is in Chinese
- **Data Format**: Primarily works with Chinese economic indicators (CPI, PPI, industrial data)
- **Time Alignment**: Special logic for aligning monthly/weekly data to specific dates (Fridays)
- **Navigation**: Complex state management for module switching - avoid modifying navigation logic without understanding the full flow

## Common Tasks

To add a new analysis module:
1. Update `MODULE_CONFIG` in `dashboard.py`
2. Create component in `ui/components/`
3. Register in content router
4. Add state management logic if needed

To debug navigation issues:
- Check `ui/utils/navigation_manager.py`
- Review state synchronization in content router
- Verify button state management

To add new data preprocessing:
- Implement in `tools/time_series_pretreat/`
- Follow existing patterns for UI integration
- Ensure proper state management for results

To modify state related functions:
- always use UnifiedStateManager 


## 语言规范
- 所有对话和文档都使用中文
- 文档使用 markdown 格式

## 操作规范
- 永远保持主动，自己测试、自己运行、自己纠错、再测试、再运行 