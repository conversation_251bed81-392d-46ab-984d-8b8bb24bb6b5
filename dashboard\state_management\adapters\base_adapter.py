# -*- coding: utf-8 -*-
"""
基础适配器类
定义所有状态管理适配器的统一接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
import threading
import time


class BaseAdapter(ABC):
    """状态管理适配器基类"""
    
    def __init__(self, state_manager, module_name: str):
        """
        初始化基础适配器
        
        Args:
            state_manager: 统一状态管理器实例
            module_name: 模块名称
        """
        self.state_manager = state_manager
        self.module_name = module_name
        self._lock = threading.RLock()
        self._initialized = False
        
        # 初始化模块特定功能
        self._setup_module()
        self._initialized = True
    
    @abstractmethod
    def _setup_module(self):
        """模块特定的初始化 - 子类必须实现"""
        pass
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """
        获取状态的通用实现
        
        Args:
            key: 状态键
            default: 默认值
            
        Returns:
            状态值或默认值
        """
        if not key:
            return default
            
        try:
            with self._lock:
                full_key = self._get_full_key(key)
                return self.state_manager.get_state(full_key, default)
        except Exception as e:
            self._handle_error(f"Failed to get state {key}", e)
            return default
    
    def set_state(self, key: str, value: Any) -> bool:
        """
        设置状态的通用实现
        
        Args:
            key: 状态键
            value: 状态值
            
        Returns:
            是否设置成功
        """
        if not key:
            return False
            
        try:
            with self._lock:
                full_key = self._get_full_key(key)
                return self.state_manager.set_state(full_key, value)
        except Exception as e:
            self._handle_error(f"Failed to set state {key}", e)
            return False
    
    def delete_state(self, key: str) -> bool:
        """
        删除状态的通用实现
        
        Args:
            key: 状态键
            
        Returns:
            是否删除成功
        """
        if not key:
            return False
            
        try:
            with self._lock:
                full_key = self._get_full_key(key)
                return self.state_manager.delete_state(full_key)
        except Exception as e:
            self._handle_error(f"Failed to delete state {key}", e)
            return False
    
    def _get_full_key(self, key: str) -> str:
        """
        获取完整的状态键
        
        Args:
            key: 原始键
            
        Returns:
            完整的状态键
        """
        return f"{self.module_name}.{key}"
    
    def _handle_error(self, message: str, error: Exception):
        """
        处理错误
        
        Args:
            message: 错误消息
            error: 异常对象
        """
        if hasattr(self.state_manager, 'logger') and self.state_manager.logger:
            self.state_manager.logger.error(f"[{self.module_name}] {message}: {error}")
        else:
            print(f"[{self.module_name}] {message}: {error}")
    
    def get_module_name(self) -> str:
        """获取模块名称"""
        return self.module_name
    
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取适配器统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'module_name': self.module_name,
            'initialized': self._initialized,
            'class_name': self.__class__.__name__
        }
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(module='{self.module_name}', initialized={self._initialized})"
