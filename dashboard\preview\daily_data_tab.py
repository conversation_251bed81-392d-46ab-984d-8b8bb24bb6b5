import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
from .plotting_utils import plot_daily_indicator
from .growth_calculator import calculate_daily_growth_summary

# 导入统一状态管理系统 - 强制要求可用
from dashboard.state_management import get_unified_manager
from dashboard.state_management.refactor import PreviewModuleRefactor

# 全局变量存储管理器实例
_unified_manager = None
_preview_refactor = None

@st.cache_resource
def get_unified_state_manager():
    """获取统一状态管理器实例"""
    return get_unified_manager()

_unified_manager = get_unified_state_manager()

def get_preview_refactor():
    """获取Preview重构适配器实例（缓存版本）"""
    # streamlit已在顶部导入

    @st.cache_resource
    def _get_preview_refactor():
        unified_manager = get_unified_state_manager()
        preview_refactor = PreviewModuleRefactor(unified_manager)
        # 注意：不再调用 on_module_load()，避免重复初始化

        return preview_refactor

    return _get_preview_refactor()

def display_daily_tab(st):
    """Displays the daily data analysis tab."""

    # 获取统一状态管理器和Preview重构适配器
    unified_manager = get_unified_state_manager()
    preview_refactor = get_preview_refactor()

    # 统一的状态数据获取函数
    def get_state_data(key, default=None):
        """统一的状态数据获取函数"""
        # 只使用preview重构适配器
        return preview_refactor.get_preview_state(key, default)

    def set_state_data(key, value):
        """统一的状态数据设置函数"""
        # 只使用preview重构适配器
        return preview_refactor.set_preview_state(key, value)

    # Check for daily_df specifically for this tab's operation
    daily_df = get_state_data('daily_df', pd.DataFrame())
    if daily_df.empty:
        st.info("日度数据尚未加载或为空，请返回'数据概览'模块上传数据。")
        return

    # Use the pre-populated daily industries list
    daily_industries = get_state_data('daily_industries', [])
    clean_industry_map = get_state_data('clean_industry_map', {})
    source_map = get_state_data('source_map', {})
    # 强制清除缓存以使用新的计算逻辑
    daily_summary_cache = {}

    # --- Pre-calculation still needed here if not moved to dashboard.py ---
    # --- If moved, this block can be removed ---
    data_loaded = get_state_data('data_loaded', False)
    if data_loaded and not daily_summary_cache and daily_industries:
        with st.spinner("正在计算日度行业摘要..."): # Keep spinner for feedback
            for industry_name in daily_industries:
                if industry_name not in daily_summary_cache:
                    original_sources = clean_industry_map.get(industry_name, [])
                    industry_indicator_cols = [ind for ind, src in source_map.items()
                                             if src in original_sources and ind in daily_df.columns]
                    if industry_indicator_cols:
                        industry_daily_data = daily_df[industry_indicator_cols]
                        try:
                            summary_table = calculate_daily_growth_summary(industry_daily_data)
                            daily_summary_cache[industry_name] = summary_table
                        except Exception as e:
                            print(f"Error pre-calculating summary for {industry_name}: {e}")
                            daily_summary_cache[industry_name] = pd.DataFrame()
                    else:
                         daily_summary_cache[industry_name] = pd.DataFrame()
            # 更新缓存到状态管理器
            set_state_data('daily_summary_cache', daily_summary_cache)
            print("--- Daily pre-calculation finished in daily_data_tab.py ---") # Debug print

    # --- Middle Section: Industry Data Analysis ---


    if not daily_df.empty:
        # 使用 Markdown 控制标签样式
        st.markdown("**选择行业大类**")
        # Use cleaned industry names
        selected_industry_d_clean = st.selectbox(
            "select_industry_daily", # Internal key, label is handled by markdown
            daily_industries, # Use the cleaned list
            key="industry_select_daily",
            label_visibility="collapsed" # Hide the default label
        )

        if selected_industry_d_clean:
            # Get the original full source name(s) corresponding to the clean name
            original_sources = clean_industry_map.get(selected_industry_d_clean, [])

            # --- 显示日度摘要 ---
            # Use clean name for cache key
            if selected_industry_d_clean not in daily_summary_cache:
                with st.spinner(f"正在计算 '{selected_industry_d_clean}' 的日度摘要..."):
                    # 筛选属于该行业 (匹配任何一个原始来源) 的日度指标列
                    industry_indicator_cols = [ind for ind, src in source_map.items()
                                             if src in original_sources and ind in daily_df.columns]
                    if industry_indicator_cols:
                        industry_daily_data = daily_df[industry_indicator_cols]
                        try:
                            summary_table = calculate_daily_growth_summary(industry_daily_data)
                        except Exception as e:
                             st.error(f"计算日度摘要时出错 ({selected_industry_d_clean}): {e}")
                             summary_table = pd.DataFrame()
                        daily_summary_cache[selected_industry_d_clean] = summary_table
                    else:
                        daily_summary_cache[selected_industry_d_clean] = pd.DataFrame()

                # 更新缓存到状态管理器
                set_state_data('daily_summary_cache', daily_summary_cache)

            summary_table = daily_summary_cache[selected_industry_d_clean]

            # 使用 Markdown 控制标题字体大小 (使用 bold)
            st.markdown(f"**{selected_industry_d_clean} - 日度数据摘要**")

            # --- Calculate and Display Summary Sentence ---
            if not summary_table.empty:
                total_indicators = len(summary_table)

                # Convert percentage columns to numeric for comparison, handling errors
                dod_numeric = pd.to_numeric(summary_table['环比昨日'].astype(str).str.replace('%', ''), errors='coerce')

                # Convert other relevant columns to numeric
                latest_val = pd.to_numeric(summary_table['最新值'], errors='coerce')
                week_mean = pd.to_numeric(summary_table.get('上周均值', pd.Series(dtype=float)), errors='coerce')
                month_mean = pd.to_numeric(summary_table.get('上月均值', pd.Series(dtype=float)), errors='coerce')
                year_mean = pd.to_numeric(summary_table.get('近1年平均值', pd.Series(dtype=float)), errors='coerce')
                max_1y = pd.to_numeric(summary_table.get('近1年最大值', pd.Series(dtype=float)), errors='coerce')
                min_1y = pd.to_numeric(summary_table.get('近1年最小值', pd.Series(dtype=float)), errors='coerce')

                # Count based on conditions, handling NaNs
                dod_increase_count = (dod_numeric > 0).sum()
                above_week_mean_count = (latest_val > week_mean).sum()
                above_month_mean_count = (latest_val > month_mean).sum()
                above_year_mean_count = (latest_val > year_mean).sum()
                above_max_count = (latest_val > max_1y).sum()
                below_min_count = (latest_val < min_1y).sum()

                # 计算占比
                dod_pct = (dod_increase_count / total_indicators * 100) if total_indicators > 0 else 0
                above_year_mean_pct = (above_year_mean_count / total_indicators * 100) if total_indicators > 0 else 0
                above_max_pct = (above_max_count / total_indicators * 100) if total_indicators > 0 else 0
                below_min_pct = (below_min_count / total_indicators * 100) if total_indicators > 0 else 0

                # 生成摘要句子
                summary_sentence = (
                    f"{selected_industry_d_clean}行业最新日度高频指标共{total_indicators}个，"
                    f"有{dod_increase_count}个环比昨日增长（占比{dod_pct:.1f}%），"
                    f"与近1周历史值比，{above_week_mean_count}个高于上周均值，"
                    f"与近1个月历史值比，{above_month_mean_count}个高于上月均值；"
                    f"与近1年历史值比，{above_year_mean_count}个高于平均值（占比{above_year_mean_pct:.1f}%），"
                    f"{above_max_count}个高于最大值（占比{above_max_pct:.1f}%），"
                    f"{below_min_count}个低于最小值（占比{below_min_pct:.1f}%）。"
                )
                st.markdown(f'<p style="color:red;">{summary_sentence}</p>', unsafe_allow_html=True)
            # --- End of Summary Sentence ---

            if not summary_table.empty:
                # 只保留需要的列
                display_columns = ['日度指标名称', '最新日期', '最新值', '环比昨日', '上周均值', '上月均值', '近1年平均值', '近1年最大值', '近1年最小值']
                available_columns = [col for col in display_columns if col in summary_table.columns]
                summary_table_display = summary_table[available_columns].copy()

                # 添加颜色样式
                def highlight_positive_negative(val):
                    try:
                        val_float = float(str(val).replace('%', ''))
                        if val_float > 0:
                            return 'background-color: #ffcdd2'  # 更深的红色
                        elif val_float < 0:
                            return 'background-color: #c8e6c9'  # 更深的绿色
                        return ''
                    except (ValueError, TypeError):
                        return ''

                # 默认按"环比昨日"降序排序
                try:
                    summary_table_sorted = summary_table_display.copy()
                    sort_col_name = '环比昨日'  # 更新排序列名
                    summary_table_sorted[f'{sort_col_name}_numeric'] = pd.to_numeric(
                        summary_table_sorted[sort_col_name].astype(str).str.replace('%', ''), errors='coerce'
                    )
                    summary_table_sorted = summary_table_sorted.sort_values(
                        by=f'{sort_col_name}_numeric', ascending=False, na_position='last'
                    ).drop(columns=[f'{sort_col_name}_numeric'])
                except KeyError:
                    st.warning(f"无法按 '{sort_col_name}' 排序日度摘要，该列不存在。")
                    summary_table_sorted = summary_table_display
                except Exception as e:
                    st.warning(f"按 '{sort_col_name}' 排序日度摘要时出错: {e}")
                    summary_table_sorted = summary_table_display

                # 应用样式并显示表格 - 更新格式化
                try:
                    highlight_cols = ['环比昨日']
                    # --- MODIFIED FORMATTING ---
                    # First, format all numeric columns to 2 decimal places
                    format_dict = {col: '{:.2f}' for col in summary_table_sorted.select_dtypes(include=np.number).columns}

                    # Then, specifically override format for percentage columns
                    format_dict['环比昨日'] = '{:.2%}'

                    # Explicitly format known numeric cols again to ensure consistency if they were overwritten
                    known_numeric_cols = ['最新值', '上周均值', '上月均值', '近1年平均值', '近1年最大值', '近1年最小值']
                    for col in known_numeric_cols:
                        if col in summary_table_sorted.columns:
                            format_dict[col] = '{:.2f}'

                    styled_table = summary_table_sorted.style.format(format_dict)\
                                         .apply(lambda x: x.map(highlight_positive_negative), subset=highlight_cols)
                    # Hide index
                    st.dataframe(styled_table, hide_index=True)

                    # 添加自定义CSV下载按钮（修复编码问题）
                    csv_string = summary_table_sorted.to_csv(index=False, encoding='utf-8-sig')
                    csv_data = csv_string.encode('utf-8-sig')
                    st.download_button(
                        label="📥 下载日度数据摘要 (CSV)",
                        data=csv_data,
                        file_name=f"日度数据摘要_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv",
                        key="download_daily_summary_csv"
                    )
                    # --- END MODIFIED FORMATTING ---
                except KeyError as e:
                    st.error(f"格式化/高亮日度摘要表时出错，列名可能不匹配: {e} (需要列: {highlight_cols})")

            # --- 图表显示逻辑（移出 summary_table 判断块） ---
            # 显示所有指标的时间序列图 - 更新筛选逻辑
            # Use original_sources to find the correct indicators in source_map
            industry_indicators = [ind for ind, src in source_map.items()
                                if src in original_sources and ind in daily_df.columns]

            if not industry_indicators:
                st.warning(f"行业 '{selected_industry_d_clean}' 没有可供可视化的日度指标。")
            else:
                current_year = datetime.now().year
                previous_year = current_year - 1

                # 计算每个指标的日环比并排序 (用于图表分列)
                indicator_changes = {}
                for indicator in industry_indicators:
                    indicator_series = daily_df[indicator].dropna()
                    if len(indicator_series) >= 2:
                        latest_value = indicator_series.iloc[-1]
                        previous_value = indicator_series.iloc[-2]
                        if previous_value != 0:
                            try:
                                 dod_change = (latest_value - previous_value) / previous_value
                                 indicator_changes[indicator] = dod_change
                            except ZeroDivisionError:
                                 indicator_changes[indicator] = np.inf
                        else:
                            indicator_changes[indicator] = np.inf
                    else:
                        indicator_changes[indicator] = 0

                # 按日环比排序指标 (用于图表分列)
                sorted_indicators = sorted(industry_indicators,
                                        key=lambda x: indicator_changes.get(x, 0) if pd.notna(indicator_changes.get(x, 0)) else -np.inf, # Handle NaN/inf
                                        reverse=True)

                # 创建两列布局
                col1, col2 = st.columns(2)

                # 在第一列显示日环比为正的指标
                with col1:
                    for indicator in sorted_indicators:
                        change = indicator_changes.get(indicator, 0)
                        if pd.notna(change) and change > 0 and change != np.inf:
                            indicator_series = daily_df[indicator].dropna()
                            if not indicator_series.empty:
                                with st.spinner(f"正在生成 {indicator} 的图表..."):
                                    fig = plot_daily_indicator(
                                        indicator_series=indicator_series,
                                        indicator_name=indicator,
                                        current_year=current_year,
                                        previous_year=previous_year
                                    )
                                    st.plotly_chart(fig, use_container_width=True)

                # 在第二列显示日环比为负/零/Inf 的指标
                with col2:
                    for indicator in sorted_indicators:
                        change = indicator_changes.get(indicator, 0)
                        if not (pd.notna(change) and change > 0 and change != np.inf):
                            indicator_series = daily_df[indicator].dropna()
                            if not indicator_series.empty:
                                with st.spinner(f"正在生成 {indicator} 的图表..."):
                                    fig = plot_daily_indicator(
                                        indicator_series=indicator_series,
                                        indicator_name=indicator,
                                        current_year=current_year,
                                        previous_year=previous_year
                                    )
                                    st.plotly_chart(fig, use_container_width=True)
            # --- End of 图表显示逻辑 ---
        # else: # Optional block if needed when no industry is selected
            # pass

    else:
        st.warning("未加载日度数据，请先在数据加载步骤中处理。")

# Ensure necessary libraries are imported, potentially adding more as needed
# Example: import plotly.express as px if it were used here. 