# -*- coding: utf-8 -*-
"""
模块管理器
统一管理所有应用模块的注册、生命周期和依赖关系
"""

import logging
from typing import Dict, Set, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import threading
from .state_metadata import StateScope


class ModuleStatus(Enum):
    """模块状态枚举"""
    UNREGISTERED = "unregistered"
    REGISTERED = "registered"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    ERROR = "error"


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    version: str = "1.0.0"
    description: str = ""
    dependencies: Set[str] = field(default_factory=set)
    state_keys: List[str] = field(default_factory=list)
    shared_keys: List[str] = field(default_factory=list)
    status: ModuleStatus = ModuleStatus.UNREGISTERED
    registered_at: Optional[datetime] = None
    last_active: Optional[datetime] = None
    adapter_class: Optional[type] = None
    adapter_instance: Optional[Any] = None
    initialization_callback: Optional[Callable] = None
    cleanup_callback: Optional[Callable] = None


class ModuleManager:
    """模块管理器 - 统一管理所有应用模块"""
    
    def __init__(self, unified_manager=None):
        """
        初始化模块管理器
        
        Args:
            unified_manager: 统一状态管理器实例
        """
        self.unified_manager = unified_manager
        self.logger = logging.getLogger(__name__)
        
        # 模块注册表
        self._modules: Dict[str, ModuleInfo] = {}
        
        # 依赖关系图
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._reverse_dependencies: Dict[str, Set[str]] = {}
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._registration_count = 0
        self._last_registration_time = None
        
        self.logger.info("ModuleManager initialized")
    
    def register_module(self, 
                       name: str,
                       version: str = "1.0.0",
                       description: str = "",
                       dependencies: Set[str] = None,
                       state_keys: List[str] = None,
                       shared_keys: List[str] = None,
                       adapter_class: type = None,
                       initialization_callback: Callable = None,
                       cleanup_callback: Callable = None) -> bool:
        """
        注册模块
        
        Args:
            name: 模块名称
            version: 模块版本
            description: 模块描述
            dependencies: 依赖的其他模块
            state_keys: 模块使用的状态键
            shared_keys: 模块共享的状态键
            adapter_class: 模块适配器类
            initialization_callback: 初始化回调函数
            cleanup_callback: 清理回调函数
            
        Returns:
            bool: 注册是否成功
        """
        with self._lock:
            try:
                # 检查模块是否已注册
                if name in self._modules:
                    self.logger.warning(f"Module {name} already registered")
                    return False
                
                # 验证依赖关系
                dependencies = dependencies or set()
                if not self._validate_dependencies(name, dependencies):
                    return False
                
                # 创建模块信息
                module_info = ModuleInfo(
                    name=name,
                    version=version,
                    description=description,
                    dependencies=dependencies,
                    state_keys=state_keys or [],
                    shared_keys=shared_keys or [],
                    status=ModuleStatus.REGISTERED,
                    registered_at=datetime.now(),
                    adapter_class=adapter_class,
                    initialization_callback=initialization_callback,
                    cleanup_callback=cleanup_callback
                )
                
                # 注册模块
                self._modules[name] = module_info
                
                # 更新依赖关系图
                self._update_dependency_graph(name, dependencies)
                
                # 更新统计信息
                self._registration_count += 1
                self._last_registration_time = datetime.now()
                
                self.logger.info(f"Module {name} registered successfully")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to register module {name}: {e}")
                return False
    
    def _validate_dependencies(self, module_name: str, dependencies: Set[str]) -> bool:
        """验证模块依赖关系"""
        # 检查循环依赖
        if module_name in dependencies:
            self.logger.error(f"Module {module_name} cannot depend on itself")
            return False
        
        # 检查是否会形成循环依赖
        for dep in dependencies:
            if self._would_create_cycle(module_name, dep):
                self.logger.error(f"Adding dependency {dep} to {module_name} would create a cycle")
                return False
        
        return True
    
    def _would_create_cycle(self, module_name: str, dependency: str) -> bool:
        """检查添加依赖是否会创建循环"""
        if dependency not in self._modules:
            return False
        
        # 使用DFS检查是否存在从dependency到module_name的路径
        visited = set()
        
        def dfs(current: str) -> bool:
            if current == module_name:
                return True
            if current in visited:
                return False
            
            visited.add(current)
            for dep in self._dependency_graph.get(current, set()):
                if dfs(dep):
                    return True
            return False
        
        return dfs(dependency)
    
    def _update_dependency_graph(self, module_name: str, dependencies: Set[str]):
        """更新依赖关系图"""
        self._dependency_graph[module_name] = dependencies.copy()
        
        # 更新反向依赖关系
        for dep in dependencies:
            if dep not in self._reverse_dependencies:
                self._reverse_dependencies[dep] = set()
            self._reverse_dependencies[dep].add(module_name)
    
    def get_module_info(self, name: str) -> Optional[ModuleInfo]:
        """获取模块信息"""
        return self._modules.get(name)
    
    def is_module_registered(self, name: str) -> bool:
        """检查模块是否已注册"""
        return name in self._modules
    
    def get_registered_modules(self) -> List[str]:
        """获取所有已注册的模块名称"""
        return list(self._modules.keys())
    
    def get_module_dependencies(self, name: str) -> Set[str]:
        """获取模块的依赖关系"""
        return self._dependency_graph.get(name, set()).copy()
    
    def get_module_dependents(self, name: str) -> Set[str]:
        """获取依赖于指定模块的其他模块"""
        return self._reverse_dependencies.get(name, set()).copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取模块管理器统计信息"""
        return {
            'total_modules': len(self._modules),
            'registration_count': self._registration_count,
            'last_registration_time': self._last_registration_time,
            'modules_by_status': {
                status.value: len([m for m in self._modules.values() if m.status == status])
                for status in ModuleStatus
            }
        }

    def initialize_module(self, name: str) -> bool:
        """
        初始化模块

        Args:
            name: 模块名称

        Returns:
            bool: 初始化是否成功
        """
        with self._lock:
            try:
                if name not in self._modules:
                    self.logger.error(f"Module {name} not registered")
                    return False

                module_info = self._modules[name]

                # 检查模块状态
                if module_info.status == ModuleStatus.ACTIVE:
                    self.logger.info(f"Module {name} already active")
                    return True

                # 设置初始化状态
                module_info.status = ModuleStatus.INITIALIZING

                # 检查依赖是否满足
                for dep in module_info.dependencies:
                    if dep not in self._modules:
                        self.logger.error(f"Dependency {dep} not registered for module {name}")
                        module_info.status = ModuleStatus.ERROR
                        return False

                    dep_module = self._modules[dep]
                    if dep_module.status != ModuleStatus.ACTIVE:
                        self.logger.error(f"Dependency {dep} not active for module {name}")
                        module_info.status = ModuleStatus.ERROR
                        return False

                # 创建适配器实例
                if module_info.adapter_class and not module_info.adapter_instance:
                    try:
                        module_info.adapter_instance = module_info.adapter_class(self.unified_manager)
                        self.logger.debug(f"Created adapter instance for module {name}")
                    except Exception as e:
                        self.logger.error(f"Failed to create adapter for module {name}: {e}")
                        module_info.status = ModuleStatus.ERROR
                        return False

                # 执行初始化回调
                if module_info.initialization_callback:
                    try:
                        module_info.initialization_callback(module_info.adapter_instance)
                        self.logger.debug(f"Executed initialization callback for module {name}")
                    except Exception as e:
                        self.logger.error(f"Initialization callback failed for module {name}: {e}")
                        module_info.status = ModuleStatus.ERROR
                        return False

                # 设置为活跃状态
                module_info.status = ModuleStatus.ACTIVE
                module_info.last_active = datetime.now()

                self.logger.info(f"Module {name} initialized successfully")
                return True

            except Exception as e:
                self.logger.error(f"Failed to initialize module {name}: {e}")
                if name in self._modules:
                    self._modules[name].status = ModuleStatus.ERROR
                return False

    def suspend_module(self, name: str) -> bool:
        """
        暂停模块

        Args:
            name: 模块名称

        Returns:
            bool: 暂停是否成功
        """
        with self._lock:
            try:
                if name not in self._modules:
                    self.logger.error(f"Module {name} not registered")
                    return False

                module_info = self._modules[name]

                if module_info.status != ModuleStatus.ACTIVE:
                    self.logger.warning(f"Module {name} not active, cannot suspend")
                    return False

                # 检查是否有其他模块依赖此模块
                dependents = self.get_module_dependents(name)
                active_dependents = [
                    dep for dep in dependents
                    if self._modules[dep].status == ModuleStatus.ACTIVE
                ]

                if active_dependents:
                    self.logger.error(f"Cannot suspend module {name}, active dependents: {active_dependents}")
                    return False

                # 暂停模块
                module_info.status = ModuleStatus.SUSPENDED

                self.logger.info(f"Module {name} suspended successfully")
                return True

            except Exception as e:
                self.logger.error(f"Failed to suspend module {name}: {e}")
                return False

    def unregister_module(self, name: str) -> bool:
        """
        注销模块

        Args:
            name: 模块名称

        Returns:
            bool: 注销是否成功
        """
        with self._lock:
            try:
                if name not in self._modules:
                    self.logger.warning(f"Module {name} not registered")
                    return True

                module_info = self._modules[name]

                # 检查是否有其他模块依赖此模块
                dependents = self.get_module_dependents(name)
                if dependents:
                    self.logger.error(f"Cannot unregister module {name}, has dependents: {dependents}")
                    return False

                # 执行清理回调
                if module_info.cleanup_callback and module_info.adapter_instance:
                    try:
                        module_info.cleanup_callback(module_info.adapter_instance)
                        self.logger.debug(f"Executed cleanup callback for module {name}")
                    except Exception as e:
                        self.logger.warning(f"Cleanup callback failed for module {name}: {e}")

                # 从依赖关系图中移除
                if name in self._dependency_graph:
                    # 从其他模块的反向依赖中移除
                    for dep in self._dependency_graph[name]:
                        if dep in self._reverse_dependencies:
                            self._reverse_dependencies[dep].discard(name)

                    del self._dependency_graph[name]

                # 移除反向依赖
                if name in self._reverse_dependencies:
                    del self._reverse_dependencies[name]

                # 移除模块
                del self._modules[name]

                self.logger.info(f"Module {name} unregistered successfully")
                return True

            except Exception as e:
                self.logger.error(f"Failed to unregister module {name}: {e}")
                return False

    def get_initialization_order(self) -> List[str]:
        """
        获取模块初始化顺序（拓扑排序）

        Returns:
            List[str]: 按依赖关系排序的模块名称列表
        """
        # 使用Kahn算法进行拓扑排序
        in_degree = {}
        for module in self._modules:
            in_degree[module] = len(self._dependency_graph.get(module, set()))

        queue = [module for module, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # 更新依赖此模块的其他模块的入度
            for dependent in self._reverse_dependencies.get(current, set()):
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append(dependent)

        # 检查是否存在循环依赖
        if len(result) != len(self._modules):
            remaining = set(self._modules.keys()) - set(result)
            self.logger.error(f"Circular dependency detected among modules: {remaining}")
            return []

        return result
