#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据比较组件
提供数据比较和分析功能的UI组件
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging

from ..base import UIComponent

logger = logging.getLogger(__name__)

class DataComparisonComponent(UIComponent):
    """数据比较组件"""
    
    def __init__(self):
        self.component_id = "data_comparison"
        self.component_name = "数据比较"

    def render(self, st_obj, **kwargs) -> None:
        """渲染组件"""
        st_obj.markdown("### 数据比较")
        st_obj.info("📋 数据比较功能正在优化中，即将推出更强大的分析功能")

    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return [
            f"{self.component_id}_dataset_selection",
            f"{self.component_id}_comparison_type",
            f"{self.component_id}_correlation_variables",
            f"{self.component_id}_correlation_method",
            f"{self.component_id}_distribution_variable",
            f"{self.component_id}_distribution_method"
        ]
    
    def render_input_section(self, st_obj, data_list: List[pd.DataFrame] = None, data_names: List[str] = None) -> Dict[str, Any]:
        """渲染数据比较输入界面"""
        st_obj.markdown("#### 数据比较配置")
        
        if not data_list or len(data_list) < 2:
            st_obj.warning("需要至少两个数据集进行比较")
            return {'error': '数据集不足'}
        
        # 选择比较的数据集
        selected_indices = st_obj.multiselect(
            "选择要比较的数据集",
            range(len(data_list)),
            format_func=lambda x: data_names[x] if data_names else f"数据{x+1}",
            default=list(range(min(2, len(data_list)))),
            key=f"{self.component_id}_dataset_selection"
        )
        
        if len(selected_indices) < 2:
            st_obj.warning("请至少选择两个数据集进行比较")
            return {'selected_indices': selected_indices}
        
        # 比较类型
        comparison_type = st_obj.selectbox(
            "比较类型",
            ["基本统计比较", "变量相关性比较", "数据分布比较", "缺失值比较"],
            key=f"{self.component_id}_comparison_type"
        )
        
        result = {
            'selected_indices': selected_indices,
            'comparison_type': comparison_type,
            'data_list': data_list,
            'data_names': data_names or [f"数据{i+1}" for i in range(len(data_list))]
        }
        
        # 根据比较类型显示相应选项
        if comparison_type == "变量相关性比较":
            correlation_result = self.render_correlation_options(st_obj, data_list, selected_indices)
            result.update(correlation_result)
        elif comparison_type == "数据分布比较":
            distribution_result = self.render_distribution_options(st_obj, data_list, selected_indices)
            result.update(distribution_result)
        
        return result
    
    def render_correlation_options(self, st_obj, data_list: List[pd.DataFrame], selected_indices: List[int]) -> Dict[str, Any]:
        """渲染相关性比较选项"""
        st_obj.markdown("##### 相关性比较设置")
        
        # 获取所有数值列
        all_numeric_cols = set()
        for idx in selected_indices:
            numeric_cols = data_list[idx].select_dtypes(include=[np.number]).columns
            all_numeric_cols.update(numeric_cols)
        
        all_numeric_cols = list(all_numeric_cols)
        
        if not all_numeric_cols:
            st_obj.warning("选择的数据集中没有数值列")
            return {'error': '没有数值列'}
        
        # 选择要比较的变量
        selected_variables = st_obj.multiselect(
            "选择要比较的变量",
            all_numeric_cols,
            default=all_numeric_cols[:5] if len(all_numeric_cols) >= 5 else all_numeric_cols,
            key=f"{self.component_id}_correlation_variables"
        )
        
        # 相关性方法
        correlation_method = st_obj.selectbox(
            "相关性计算方法",
            ["pearson", "spearman", "kendall"],
            key=f"{self.component_id}_correlation_method"
        )
        
        return {
            'selected_variables': selected_variables,
            'correlation_method': correlation_method
        }
    
    def render_distribution_options(self, st_obj, data_list: List[pd.DataFrame], selected_indices: List[int]) -> Dict[str, Any]:
        """渲染分布比较选项"""
        st_obj.markdown("##### 分布比较设置")
        
        # 获取所有数值列
        all_numeric_cols = set()
        for idx in selected_indices:
            numeric_cols = data_list[idx].select_dtypes(include=[np.number]).columns
            all_numeric_cols.update(numeric_cols)
        
        all_numeric_cols = list(all_numeric_cols)
        
        if not all_numeric_cols:
            st_obj.warning("选择的数据集中没有数值列")
            return {'error': '没有数值列'}
        
        # 选择要比较分布的变量
        selected_variable = st_obj.selectbox(
            "选择要比较分布的变量",
            all_numeric_cols,
            key=f"{self.component_id}_distribution_variable"
        )
        
        # 分布比较方法
        distribution_method = st_obj.selectbox(
            "分布比较方法",
            ["直方图比较", "箱线图比较", "密度图比较", "Q-Q图比较"],
            key=f"{self.component_id}_distribution_method"
        )
        
        return {
            'selected_variable': selected_variable,
            'distribution_method': distribution_method
        }
    
    def perform_basic_comparison(self, data_list: List[pd.DataFrame], selected_indices: List[int], 
                               data_names: List[str]) -> Dict[str, Any]:
        """执行基本统计比较"""
        try:
            comparison_results = {}
            
            for idx in selected_indices:
                data = data_list[idx]
                name = data_names[idx]
                
                # 基本统计信息
                basic_stats = {
                    'shape': data.shape,
                    'memory_usage': data.memory_usage(deep=True).sum(),
                    'missing_values': data.isnull().sum().sum(),
                    'numeric_columns': len(data.select_dtypes(include=[np.number]).columns),
                    'categorical_columns': len(data.select_dtypes(include=['object']).columns),
                    'datetime_columns': len(data.select_dtypes(include=['datetime']).columns)
                }
                
                # 数值列统计
                numeric_data = data.select_dtypes(include=[np.number])
                if not numeric_data.empty:
                    basic_stats['numeric_summary'] = numeric_data.describe()
                
                comparison_results[name] = basic_stats
            
            logger.info(f"基本统计比较完成，比较了 {len(selected_indices)} 个数据集")
            return comparison_results
            
        except Exception as e:
            logger.error(f"基本统计比较失败: {e}")
            return {}
    
    def perform_correlation_comparison(self, data_list: List[pd.DataFrame], selected_indices: List[int],
                                     selected_variables: List[str], method: str = "pearson") -> Dict[str, Any]:
        """执行相关性比较"""
        try:
            correlation_results = {}
            
            for idx in selected_indices:
                data = data_list[idx]
                name = f"数据{idx+1}"
                
                # 筛选存在的变量
                available_vars = [var for var in selected_variables if var in data.columns]
                
                if len(available_vars) >= 2:
                    correlation_matrix = data[available_vars].corr(method=method)
                    correlation_results[name] = correlation_matrix
                else:
                    correlation_results[name] = f"数据集中可用变量不足: {available_vars}"
            
            logger.info(f"相关性比较完成，使用方法: {method}")
            return correlation_results
            
        except Exception as e:
            logger.error(f"相关性比较失败: {e}")
            return {}
    
    def perform_missing_value_comparison(self, data_list: List[pd.DataFrame], selected_indices: List[int],
                                       data_names: List[str]) -> Dict[str, Any]:
        """执行缺失值比较"""
        try:
            missing_results = {}
            
            for idx in selected_indices:
                data = data_list[idx]
                name = data_names[idx]
                
                missing_info = {
                    'total_missing': data.isnull().sum().sum(),
                    'missing_percentage': (data.isnull().sum().sum() / data.size) * 100,
                    'columns_with_missing': data.isnull().sum()[data.isnull().sum() > 0].to_dict(),
                    'complete_rows': len(data.dropna()),
                    'complete_percentage': (len(data.dropna()) / len(data)) * 100
                }
                
                missing_results[name] = missing_info
            
            logger.info(f"缺失值比较完成，比较了 {len(selected_indices)} 个数据集")
            return missing_results
            
        except Exception as e:
            logger.error(f"缺失值比较失败: {e}")
            return {}


__all__ = ['DataComparisonComponent']
