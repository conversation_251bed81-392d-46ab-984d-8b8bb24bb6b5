# -*- coding: utf-8 -*-
"""
统一错误处理系统
提供全局错误处理、异常分类、错误恢复和监控功能
"""

import logging
import traceback
import threading
import time
from typing import Dict, List, Optional, Any, Callable, Type, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import functools
import inspect
import sys
import json


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    LOW = "low"           # 低级错误，不影响核心功能
    MEDIUM = "medium"     # 中级错误，影响部分功能
    HIGH = "high"         # 高级错误，影响核心功能
    CRITICAL = "critical" # 严重错误，系统无法正常运行


class ErrorCategory(Enum):
    """错误类别枚举"""
    SYSTEM = "system"           # 系统级错误
    NETWORK = "network"         # 网络相关错误
    DATABASE = "database"       # 数据库相关错误
    VALIDATION = "validation"   # 数据验证错误
    PERMISSION = "permission"   # 权限相关错误
    RESOURCE = "resource"       # 资源相关错误
    BUSINESS = "business"       # 业务逻辑错误
    EXTERNAL = "external"       # 外部服务错误
    UNKNOWN = "unknown"         # 未知错误


class RecoveryStrategy(Enum):
    """恢复策略枚举"""
    NONE = "none"               # 不进行恢复
    RETRY = "retry"             # 重试操作
    EMERGENCY = "emergency"     # 使用紧急方案
    RESET = "reset"             # 重置状态
    ESCALATE = "escalate"       # 上报处理
    SHUTDOWN = "shutdown"       # 关闭服务


@dataclass
class ErrorContext:
    """错误上下文信息"""
    module: str = ""
    function: str = ""
    line_number: int = 0
    file_path: str = ""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorRecord:
    """错误记录"""
    error_id: str
    timestamp: datetime
    exception_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    stack_trace: str
    recovery_strategy: RecoveryStrategy
    recovery_attempted: bool = False
    recovery_successful: bool = False
    occurrence_count: int = 1
    first_occurrence: datetime = field(default_factory=datetime.now)
    last_occurrence: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolution_notes: str = ""


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, max_records: int = 10000, cleanup_interval: int = 3600, enable_cleanup_thread: bool = True):
        """
        初始化错误处理器

        Args:
            max_records: 最大错误记录数
            cleanup_interval: 清理间隔（秒）
            enable_cleanup_thread: 是否启用清理线程
        """
        self.logger = logging.getLogger(__name__)
        
        # 错误记录存储
        self._error_records: Dict[str, ErrorRecord] = {}
        self._error_patterns: Dict[str, ErrorRecord] = {}  # 错误模式匹配
        self._max_records = max_records
        
        # 错误分类规则
        self._classification_rules: Dict[Type[Exception], Dict[str, Any]] = {}
        self._recovery_handlers: Dict[ErrorCategory, List[Callable]] = {}
        
        # 统计信息
        self._error_stats = {
            'total_errors': 0,
            'errors_by_severity': {s.value: 0 for s in ErrorSeverity},
            'errors_by_category': {c.value: 0 for c in ErrorCategory},
            'recovery_success_rate': 0.0,
            'last_cleanup': datetime.now()
        }
        
        # 配置
        self._cleanup_interval = cleanup_interval
        self._auto_recovery_enabled = True
        self._notification_enabled = True
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 启动清理线程
        if enable_cleanup_thread:
            self._cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
            self._cleanup_thread.start()
        else:
            self._cleanup_thread = None
        
        # 注册默认分类规则
        self._register_default_classification_rules()
        
        self.logger.info("ErrorHandler initialized")
    
    def _register_default_classification_rules(self):
        """注册默认的错误分类规则"""
        # 系统错误
        self.register_classification_rule(
            MemoryError, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, RecoveryStrategy.SHUTDOWN
        )
        self.register_classification_rule(
            SystemError, ErrorCategory.SYSTEM, ErrorSeverity.HIGH, RecoveryStrategy.RESET
        )
        
        # 网络错误
        self.register_classification_rule(
            ConnectionError, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY
        )
        self.register_classification_rule(
            TimeoutError, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY
        )
        
        # 验证错误
        self.register_classification_rule(
            ValueError, ErrorCategory.VALIDATION, ErrorSeverity.LOW, RecoveryStrategy.NONE
        )
        self.register_classification_rule(
            TypeError, ErrorCategory.VALIDATION, ErrorSeverity.LOW, RecoveryStrategy.NONE
        )
        
        # 权限错误
        self.register_classification_rule(
            PermissionError, ErrorCategory.PERMISSION, ErrorSeverity.MEDIUM, RecoveryStrategy.ESCALATE
        )
        
        # 资源错误
        self.register_classification_rule(
            FileNotFoundError, ErrorCategory.RESOURCE, ErrorSeverity.MEDIUM, RecoveryStrategy.EMERGENCY
        )
        self.register_classification_rule(
            OSError, ErrorCategory.RESOURCE, ErrorSeverity.MEDIUM, RecoveryStrategy.EMERGENCY
        )
    
    def register_classification_rule(
        self, 
        exception_type: Type[Exception], 
        category: ErrorCategory, 
        severity: ErrorSeverity, 
        recovery_strategy: RecoveryStrategy
    ):
        """
        注册错误分类规则
        
        Args:
            exception_type: 异常类型
            category: 错误类别
            severity: 错误严重程度
            recovery_strategy: 恢复策略
        """
        with self._lock:
            self._classification_rules[exception_type] = {
                'category': category,
                'severity': severity,
                'recovery_strategy': recovery_strategy
            }
            self.logger.debug(f"Registered classification rule for {exception_type.__name__}")
    
    def register_recovery_handler(self, category: ErrorCategory, handler: Callable):
        """
        注册恢复处理器
        
        Args:
            category: 错误类别
            handler: 恢复处理函数
        """
        with self._lock:
            if category not in self._recovery_handlers:
                self._recovery_handlers[category] = []
            self._recovery_handlers[category].append(handler)
            self.logger.debug(f"Registered recovery handler for {category.value}")
    
    def handle_error(
        self, 
        exception: Exception, 
        context: Optional[ErrorContext] = None,
        custom_message: Optional[str] = None
    ) -> str:
        """
        处理错误
        
        Args:
            exception: 异常对象
            context: 错误上下文
            custom_message: 自定义错误消息
            
        Returns:
            str: 错误ID
        """
        with self._lock:
            try:
                # 生成错误ID
                error_id = self._generate_error_id(exception, context)
                
                # 分类错误
                classification = self._classify_error(exception)
                
                # 获取堆栈跟踪
                stack_trace = traceback.format_exc()
                
                # 创建或更新错误记录
                if error_id in self._error_records:
                    # 更新现有记录
                    record = self._error_records[error_id]
                    record.occurrence_count += 1
                    record.last_occurrence = datetime.now()
                else:
                    # 创建新记录
                    # 确保context是ErrorContext对象
                    if context is None:
                        error_context = ErrorContext()
                    elif isinstance(context, dict):
                        # 如果传入的是字典，转换为ErrorContext对象
                        error_context = ErrorContext(
                            module=context.get('module', ''),
                            function=context.get('function', ''),
                            line_number=context.get('line_number', 0),
                            file_path=context.get('file_path', ''),
                            user_id=context.get('user_id', None),
                            session_id=context.get('session_id', None),
                            request_id=context.get('request_id', None),
                            additional_data=context.get('additional_data', {})
                        )
                    else:
                        error_context = context
                    
                    record = ErrorRecord(
                        error_id=error_id,
                        timestamp=datetime.now(),
                        exception_type=type(exception).__name__,
                        message=custom_message or str(exception),
                        severity=classification['severity'],
                        category=classification['category'],
                        context=error_context,
                        stack_trace=stack_trace,
                        recovery_strategy=classification['recovery_strategy']
                    )
                    self._error_records[error_id] = record
                
                # 更新统计信息
                self._update_statistics(record)
                
                # 记录日志
                self._log_error(record)
                
                # 尝试自动恢复
                if self._auto_recovery_enabled:
                    self._attempt_recovery(record)
                
                # 发送通知
                if self._notification_enabled:
                    self._send_notification(record)
                
                return error_id
                
            except Exception as e:
                # 错误处理器本身出错，记录到系统日志
                self.logger.critical(f"Error in error handler: {e}")
                return "error_handler_failure"
    
    def _generate_error_id(self, exception: Exception, context: Optional[ErrorContext]) -> str:
        """生成错误ID"""
        # 基于异常类型、消息和上下文生成唯一ID
        components = [
            type(exception).__name__,
            str(exception)[:100],  # 限制消息长度
        ]
        
        if context:
            # 安全地访问context属性
            if isinstance(context, dict):
                components.extend([
                    context.get('module', ''),
                    context.get('function', ''),
                    str(context.get('line_number', 0))
                ])
            else:
                components.extend([
                    getattr(context, 'module', ''),
                    getattr(context, 'function', ''),
                    str(getattr(context, 'line_number', 0))
                ])
        
        # 使用hash生成简短ID
        import hashlib
        content = "|".join(components)
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def _classify_error(self, exception: Exception) -> Dict[str, Any]:
        """分类错误"""
        exception_type = type(exception)
        
        # 查找精确匹配
        if exception_type in self._classification_rules:
            return self._classification_rules[exception_type]
        
        # 查找父类匹配
        for exc_type, classification in self._classification_rules.items():
            if issubclass(exception_type, exc_type):
                return classification
        
        # 默认分类
        return {
            'category': ErrorCategory.UNKNOWN,
            'severity': ErrorSeverity.MEDIUM,
            'recovery_strategy': RecoveryStrategy.NONE
        }
    
    def _update_statistics(self, record: ErrorRecord):
        """更新统计信息"""
        if record.occurrence_count == 1:  # 新错误
            self._error_stats['total_errors'] += 1
            self._error_stats['errors_by_severity'][record.severity.value] += 1
            self._error_stats['errors_by_category'][record.category.value] += 1
    
    def _log_error(self, record: ErrorRecord):
        """记录错误日志"""
        log_level = {
            ErrorSeverity.LOW: logging.WARNING,
            ErrorSeverity.MEDIUM: logging.ERROR,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(record.severity, logging.ERROR)
        
        message = (
            f"Error [{record.error_id}] {record.exception_type}: {record.message} "
            f"(Severity: {record.severity.value}, Category: {record.category.value}, "
            f"Count: {record.occurrence_count})"
        )
        
        self.logger.log(log_level, message)
        
        # 对于严重错误，记录完整堆栈跟踪
        if record.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.log(log_level, f"Stack trace for {record.error_id}:\n{record.stack_trace}")
    
    def _attempt_recovery(self, record: ErrorRecord):
        """尝试自动恢复"""
        if record.recovery_attempted:
            return
        
        record.recovery_attempted = True
        
        try:
            # 执行恢复策略
            if record.recovery_strategy == RecoveryStrategy.RETRY:
                # 重试逻辑由调用方实现
                pass
            elif record.recovery_strategy == RecoveryStrategy.EMERGENCY:
                # 执行备用方案
                self._execute_emergency_recovery(record)
            elif record.recovery_strategy == RecoveryStrategy.RESET:
                # 重置状态
                self._execute_reset(record)
            elif record.recovery_strategy == RecoveryStrategy.ESCALATE:
                # 上报处理
                self._execute_escalation(record)
            elif record.recovery_strategy == RecoveryStrategy.SHUTDOWN:
                # 关闭服务
                self._execute_shutdown(record)
            
            # 执行注册的恢复处理器
            handlers = self._recovery_handlers.get(record.category, [])
            for handler in handlers:
                try:
                    handler(record)
                    record.recovery_successful = True
                except Exception as e:
                    self.logger.error(f"Recovery handler failed: {e}")
                    
        except Exception as e:
            self.logger.error(f"Recovery attempt failed for {record.error_id}: {e}")
    
    def _execute_emergency_recovery(self, record: ErrorRecord):
        """执行备用方案"""
        self.logger.info(f"Executing emergency recovery for error {record.error_id}")
        # 具体的备用方案由子类或注册的处理器实现
    
    def _execute_reset(self, record: ErrorRecord):
        """执行重置操作"""
        self.logger.info(f"Executing reset for error {record.error_id}")
        # 具体的重置操作由子类或注册的处理器实现
    
    def _execute_escalation(self, record: ErrorRecord):
        """执行上报操作"""
        self.logger.warning(f"Escalating error {record.error_id}")
        # 具体的上报操作由子类或注册的处理器实现
    
    def _execute_shutdown(self, record: ErrorRecord):
        """执行关闭操作"""
        self.logger.critical(f"Initiating shutdown due to error {record.error_id}")
        # 具体的关闭操作由子类或注册的处理器实现
    
    def _send_notification(self, record: ErrorRecord):
        """发送错误通知"""
        # 根据错误严重程度决定是否发送通知
        if record.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.info(f"Sending notification for error {record.error_id}")
            # 具体的通知逻辑由子类或外部系统实现

    def _cleanup_worker(self):
        """清理工作线程"""
        while True:
            try:
                time.sleep(self._cleanup_interval)
                self._cleanup_old_records()
            except Exception as e:
                self.logger.error(f"Error in cleanup worker: {e}")

    def _cleanup_old_records(self):
        """清理旧的错误记录"""
        with self._lock:
            try:
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(days=7)  # 保留7天的记录

                # 找出需要清理的记录
                records_to_remove = []
                for error_id, record in self._error_records.items():
                    if record.last_occurrence < cutoff_time and record.resolved:
                        records_to_remove.append(error_id)

                # 如果记录数超过限制，清理最旧的记录
                if len(self._error_records) > self._max_records:
                    sorted_records = sorted(
                        self._error_records.items(),
                        key=lambda x: x[1].last_occurrence
                    )
                    excess_count = len(self._error_records) - self._max_records
                    for i in range(excess_count):
                        records_to_remove.append(sorted_records[i][0])

                # 执行清理
                for error_id in records_to_remove:
                    del self._error_records[error_id]

                if records_to_remove:
                    self.logger.info(f"Cleaned up {len(records_to_remove)} old error records")

                self._error_stats['last_cleanup'] = current_time

            except Exception as e:
                self.logger.error(f"Failed to cleanup old records: {e}")

    def get_error_record(self, error_id: str) -> Optional[ErrorRecord]:
        """
        获取错误记录

        Args:
            error_id: 错误ID

        Returns:
            Optional[ErrorRecord]: 错误记录
        """
        with self._lock:
            return self._error_records.get(error_id)

    def get_error_records(
        self,
        category: Optional[ErrorCategory] = None,
        severity: Optional[ErrorSeverity] = None,
        resolved: Optional[bool] = None,
        limit: int = 100
    ) -> List[ErrorRecord]:
        """
        获取错误记录列表

        Args:
            category: 错误类别过滤
            severity: 错误严重程度过滤
            resolved: 是否已解决过滤
            limit: 返回记录数限制

        Returns:
            List[ErrorRecord]: 错误记录列表
        """
        with self._lock:
            records = list(self._error_records.values())

            # 应用过滤条件
            if category is not None:
                records = [r for r in records if r.category == category]
            if severity is not None:
                records = [r for r in records if r.severity == severity]
            if resolved is not None:
                records = [r for r in records if r.resolved == resolved]

            # 按时间排序（最新的在前）
            records.sort(key=lambda x: x.last_occurrence, reverse=True)

            return records[:limit]

    def resolve_error(self, error_id: str, resolution_notes: str = ""):
        """
        标记错误为已解决

        Args:
            error_id: 错误ID
            resolution_notes: 解决方案说明
        """
        with self._lock:
            if error_id in self._error_records:
                record = self._error_records[error_id]
                record.resolved = True
                record.resolution_notes = resolution_notes
                self.logger.info(f"Error {error_id} marked as resolved")
            else:
                self.logger.warning(f"Error {error_id} not found")

    def get_error_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            # 计算恢复成功率
            total_recovery_attempts = sum(
                1 for record in self._error_records.values()
                if record.recovery_attempted
            )
            successful_recoveries = sum(
                1 for record in self._error_records.values()
                if record.recovery_successful
            )

            recovery_rate = (
                successful_recoveries / total_recovery_attempts
                if total_recovery_attempts > 0 else 0.0
            )

            # 计算最近24小时的错误数
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_errors = sum(
                1 for record in self._error_records.values()
                if record.last_occurrence >= recent_cutoff
            )

            stats = self._error_stats.copy()
            stats.update({
                'recovery_success_rate': recovery_rate,
                'total_records': len(self._error_records),
                'unresolved_errors': sum(
                    1 for record in self._error_records.values()
                    if not record.resolved
                ),
                'recent_errors_24h': recent_errors,
                'recovery_attempts': total_recovery_attempts,
                'successful_recoveries': successful_recoveries
            })

            return stats

    def export_error_data(self, include_stack_traces: bool = False) -> Dict[str, Any]:
        """
        导出错误数据

        Args:
            include_stack_traces: 是否包含堆栈跟踪

        Returns:
            Dict[str, Any]: 导出的错误数据
        """
        with self._lock:
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'statistics': self.get_error_statistics(),
                'records': []
            }

            for record in self._error_records.values():
                record_data = {
                    'error_id': record.error_id,
                    'timestamp': record.timestamp.isoformat(),
                    'exception_type': record.exception_type,
                    'message': record.message,
                    'severity': record.severity.value,
                    'category': record.category.value,
                    'context': {
                        'module': record.context.module,
                        'function': record.context.function,
                        'line_number': record.context.line_number,
                        'file_path': record.context.file_path,
                        'additional_data': record.context.additional_data
                    },
                    'recovery_strategy': record.recovery_strategy.value,
                    'recovery_attempted': record.recovery_attempted,
                    'recovery_successful': record.recovery_successful,
                    'occurrence_count': record.occurrence_count,
                    'first_occurrence': record.first_occurrence.isoformat(),
                    'last_occurrence': record.last_occurrence.isoformat(),
                    'resolved': record.resolved,
                    'resolution_notes': record.resolution_notes
                }

                if include_stack_traces:
                    record_data['stack_trace'] = record.stack_trace

                export_data['records'].append(record_data)

            return export_data

    def clear_resolved_errors(self):
        """清除已解决的错误"""
        with self._lock:
            resolved_ids = [
                error_id for error_id, record in self._error_records.items()
                if record.resolved
            ]

            for error_id in resolved_ids:
                del self._error_records[error_id]

            self.logger.info(f"Cleared {len(resolved_ids)} resolved errors")

    def set_auto_recovery(self, enabled: bool):
        """设置自动恢复开关"""
        self._auto_recovery_enabled = enabled
        self.logger.info(f"Auto recovery {'enabled' if enabled else 'disabled'}")

    def set_notifications(self, enabled: bool):
        """设置通知开关"""
        self._notification_enabled = enabled
        self.logger.info(f"Notifications {'enabled' if enabled else 'disabled'}")


def error_handler_decorator(
    error_handler: ErrorHandler,
    category: Optional[ErrorCategory] = None,
    severity: Optional[ErrorSeverity] = None,
    recovery_strategy: Optional[RecoveryStrategy] = None,
    reraise: bool = True
):
    """
    错误处理装饰器

    Args:
        error_handler: 错误处理器实例
        category: 错误类别
        severity: 错误严重程度
        recovery_strategy: 恢复策略
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 创建错误上下文
                frame = inspect.currentframe()
                context = ErrorContext(
                    module=func.__module__,
                    function=func.__name__,
                    line_number=frame.f_lineno if frame else 0,
                    file_path=inspect.getfile(func)
                )

                # 处理错误
                error_id = error_handler.handle_error(e, context)

                # 根据配置决定是否重新抛出异常
                if reraise:
                    raise
                else:
                    return None

        return wrapper
    return decorator


def create_error_context(
    module: str = "",
    function: str = "",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    request_id: Optional[str] = None,
    **additional_data
) -> ErrorContext:
    """
    创建错误上下文

    Args:
        module: 模块名
        function: 函数名
        user_id: 用户ID
        session_id: 会话ID
        request_id: 请求ID
        **additional_data: 额外数据

    Returns:
        ErrorContext: 错误上下文
    """
    return ErrorContext(
        module=module,
        function=function,
        user_id=user_id,
        session_id=session_id,
        request_id=request_id,
        additional_data=additional_data
    )
