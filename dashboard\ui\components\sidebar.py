# -*- coding: utf-8 -*-
"""
侧边栏组件
提供侧边栏相关的UI组件
"""

import streamlit as st
import pandas as pd
import numpy as np
import io
from typing import List, Dict, Any, Optional
from datetime import datetime
from contextlib import contextmanager
# 延迟导入避免循环导入
# from .base import UIComponent
from .module_selector import render_main_module_selector, render_sub_module_selector
from ..utils.debug_helpers import debug_navigation
from ..utils.state_helpers import get_staged_data

class SidebarComponent:
    """侧边栏组件基类"""

    def __init__(self):
        pass

    def render(self, st_obj, **kwargs) -> None:
        """渲染侧边栏组件"""
        pass

    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return []

class DataUploadSidebar(SidebarComponent):
    """数据上传侧边栏组件"""
    
    def __init__(self, title: str = "数据上传", 
                 accepted_types: List[str] = None,
                 help_text: str = None):
        super().__init__()
        self.title = title
        self.accepted_types = accepted_types or ['xlsx', 'xls', 'csv']
        self.help_text = help_text or "请上传Excel或CSV格式的数据文件"
    
    def render(self, st_obj, **kwargs) -> None:
        """渲染数据上传侧边栏"""
        with st_obj.sidebar:
            st_obj.markdown(f"### {self.title}")
            
            uploaded_file = st_obj.file_uploader(
                "选择数据文件",
                type=self.accepted_types,
                help=self.help_text,
                key=kwargs.get('upload_key', 'data_upload')
            )
            
            if uploaded_file is not None:
                st_obj.success(f"文件 '{uploaded_file.name}' 上传成功！")
                
                # 文件信息
                file_size = len(uploaded_file.getvalue()) / 1024 / 1024  # MB
                st_obj.info(f"文件大小: {file_size:.2f} MB")
                
                return uploaded_file
            
            return None
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return ['data_upload']

class ParameterSidebar(SidebarComponent):
    """参数配置侧边栏组件"""
    
    def __init__(self, title: str = "参数配置", 
                 parameters: List[Dict[str, Any]] = None):
        super().__init__()
        self.title = title
        self.parameters = parameters or []
    
    def render(self, st_obj, **kwargs) -> Dict[str, Any]:
        """渲染参数配置侧边栏"""
        with st_obj.sidebar:
            st_obj.markdown(f"### {self.title}")
            
            param_values = {}
            
            for param in self.parameters:
                param_type = param.get('type', 'text')
                param_key = param['key']
                param_label = param.get('label', param_key)
                param_default = param.get('default')
                param_help = param.get('help')
                
                if param_type == 'text':
                    value = st_obj.text_input(
                        param_label,
                        value=param_default or "",
                        help=param_help,
                        key=f"param_{param_key}"
                    )
                elif param_type == 'number':
                    value = st_obj.number_input(
                        param_label,
                        value=param_default or 0,
                        help=param_help,
                        key=f"param_{param_key}"
                    )
                elif param_type == 'select':
                    options = param.get('options', [])
                    value = st_obj.selectbox(
                        param_label,
                        options=options,
                        index=options.index(param_default) if param_default in options else 0,
                        help=param_help,
                        key=f"param_{param_key}"
                    )
                elif param_type == 'checkbox':
                    value = st_obj.checkbox(
                        param_label,
                        value=param_default or False,
                        help=param_help,
                        key=f"param_{param_key}"
                    )
                elif param_type == 'slider':
                    min_val = param.get('min', 0)
                    max_val = param.get('max', 100)
                    value = st_obj.slider(
                        param_label,
                        min_value=min_val,
                        max_value=max_val,
                        value=param_default or min_val,
                        help=param_help,
                        key=f"param_{param_key}"
                    )
                else:
                    value = param_default
                
                param_values[param_key] = value
            
            return param_values
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return [f"param_{param['key']}" for param in self.parameters]


class DataExplorationSidebar(SidebarComponent):
    """数据探索专用侧边栏组件 - 集中所有数据上传功能"""

    def __init__(self,
                 title: str = "📁 数据探索 - 数据上传",
                 accepted_types: List[str] = None,
                 help_text: str = None):
        super().__init__()
        self.title = title
        self.accepted_types = accepted_types or ['csv', 'xlsx', 'xls']
        self.help_text = help_text or "上传CSV或Excel文件进行时间序列数据探索分析"

    def render(self, st_obj, **kwargs) -> Optional[pd.DataFrame]:
        """渲染数据探索专用侧边栏 - 唯一的数据上传入口"""
        # 使用侧边栏容器确保在侧边栏中渲染
        with st_obj.sidebar:
            st_obj.markdown(f"### {self.title}")

            # 添加文件上传组件的黑色字体样式 - 超高特异性确保覆盖所有情况
            st_obj.markdown("""
            <style>
            /* 侧边栏所有文字元素强制黑色 - 最高优先级 */
            [data-testid="stSidebar"] h1,
            [data-testid="stSidebar"] h2,
            [data-testid="stSidebar"] h3,
            [data-testid="stSidebar"] h4,
            [data-testid="stSidebar"] h5,
            [data-testid="stSidebar"] h6,
            [data-testid="stSidebar"] p,
            [data-testid="stSidebar"] span,
            [data-testid="stSidebar"] div,
            [data-testid="stSidebar"] small,
            [data-testid="stSidebar"] strong,
            [data-testid="stSidebar"] label,
            [data-testid="stSidebar"] .stMarkdown,
            [data-testid="stSidebar"] .stMarkdown *,
            [data-testid="stSidebar"] .st-emotion-cache-102y9h7,
            [data-testid="stSidebar"] .st-emotion-cache-102y9h7 *,
            [data-testid="stSidebar"] .st-emotion-cache-v40j46,
            [data-testid="stSidebar"] .st-emotion-cache-v40j46 *,
            [data-testid="stSidebar"] .st-emotion-cache-1xulwhk,
            [data-testid="stSidebar"] .st-emotion-cache-1xulwhk *,
            [data-testid="stSidebar"] .st-emotion-cache-p7i6r9,
            [data-testid="stSidebar"] .st-emotion-cache-p7i6r9 *,
            [data-testid="stSidebar"] .erovr380,
            [data-testid="stSidebar"] .erovr380 * {
                color: #000000 !important;
            }

            /* 侧边栏文件上传组件 - 超高特异性选择器 */
            [data-testid="stSidebar"] .stFileUploader,
            [data-testid="stSidebar"] .stFileUploader *,
            [data-testid="stSidebar"] .stFileUploader [data-testid="stFileUploaderDropzone"],
            [data-testid="stSidebar"] .stFileUploader [data-testid="stFileUploaderDropzone"] *,
            [data-testid="stSidebar"] .stFileUploader label,
            [data-testid="stSidebar"] .stFileUploader label *,
            [data-testid="stSidebar"] .stFileUploader button,
            [data-testid="stSidebar"] .stFileUploader button *,
            [data-testid="stSidebar"] .stFileUploader small,
            [data-testid="stSidebar"] .stFileUploader div,
            [data-testid="stSidebar"] .stFileUploader span,
            [data-testid="stSidebar"] .stFileUploader p,
            [data-testid="stSidebar"] .stFileUploader > div,
            [data-testid="stSidebar"] .stFileUploader > div > div,
            [data-testid="stSidebar"] .stFileUploader > div > div > div,
            [data-testid="stSidebar"] .stFileUploader > div > div > div > div,
            [data-testid="stSidebar"] .stFileUploader label > div,
            [data-testid="stSidebar"] .stFileUploader label > div > div,
            [data-testid="stSidebar"] .stFileUploader label > div > div > div,
            /* 针对Streamlit emotion CSS类的终极选择器 */
            [data-testid="stSidebar"] .stFileUploader [class*="st-emotion-cache"],
            [data-testid="stSidebar"] .stFileUploader [class*="st-emotion-cache"] *,
            [data-testid="stSidebar"] .stFileUploader [class*="e16xj5sw"],
            [data-testid="stSidebar"] .stFileUploader [class*="e16xj5sw"] *,
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] [class*="st-emotion-cache"],
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] [class*="st-emotion-cache"] *,
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] [class*="e16xj5sw"],
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] [class*="e16xj5sw"] * {
                color: #000000 !important;
                fill: #000000 !important;
            }

            /* 确保拖拽区域文字为黑色 */
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"],
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] *,
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] div,
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] span,
            [data-testid="stSidebar"] [data-testid="stFileUploaderDropzone"] p {
                color: #000000 !important;
                fill: #000000 !important;
            }

            /* 隐藏黑点tooltip图标和可能的黑色方块 */
            [data-testid="stSidebar"] .stTooltipIcon,
            [data-testid="stSidebar"] .stTooltipHoverTarget,
            [data-testid="stSidebar"] .st-emotion-cache-oj1fi {
                display: none !important;
                visibility: hidden !important;
            }

            /* 隐藏文件上传器中可能的黑色方块元素 */
            [data-testid="stSidebar"] .stFileUploader .st-emotion-cache-1xulwhk,
            [data-testid="stSidebar"] .stFileUploader .st-emotion-cache-p7i6r9,
            [data-testid="stSidebar"] .stFileUploader .st-emotion-cache-102y9h7,
            [data-testid="stSidebar"] .stFileUploader .st-emotion-cache-v40j46 {
                background-color: transparent !important;
                border: none !important;
                box-shadow: none !important;
            }

            /* 确保文件上传器的所有子元素都有正确的样式 */
            [data-testid="stSidebar"] .stFileUploader > div > div > div {
                background-color: transparent !important;
                color: #000000 !important;
            }

            /* 修复文件大小文本的颜色 - 这是黑色方块的真正原因 */
            [data-testid="stSidebar"] small,
            [data-testid="stSidebar"] .stFileUploader small,
            [data-testid="stSidebar"] small[class*="st-emotion-cache"],
            [data-testid="stSidebar"] small[class*="ejh2rmr"] {
                color: #9CA3AF !important;
                border-color: transparent !important;
                background-color: transparent !important;
            }

            /* 强制覆盖所有可能的黑色文本 */
            [data-testid="stSidebar"] * {
                color: inherit !important;
            }

            [data-testid="stSidebar"] .stFileUploader * {
                color: #9CA3AF !important;
            }

            /* 隐藏可能的黑色图标或按钮 */
            [data-testid="stSidebar"] .stFileUploader svg[fill="currentColor"],
            [data-testid="stSidebar"] .stFileUploader [data-testid="stIcon"] {
                fill: #666666 !important;
                color: #666666 !important;
            }

            /* 强制隐藏文件上传器中的所有黑色方块 */
            [data-testid="stSidebar"] .stFileUploader div[style*="background-color: rgb(0, 0, 0)"],
            [data-testid="stSidebar"] .stFileUploader div[style*="background: rgb(0, 0, 0)"],
            [data-testid="stSidebar"] .stFileUploader div[style*="background-color: black"],
            [data-testid="stSidebar"] .stFileUploader div[style*="background: black"],
            [data-testid="stSidebar"] .stFileUploader span[style*="background-color: rgb(0, 0, 0)"],
            [data-testid="stSidebar"] .stFileUploader span[style*="background: rgb(0, 0, 0)"] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }

            /* 隐藏文件上传器中的所有可能的黑色元素 */
            [data-testid="stSidebar"] .stFileUploader [style*="color: rgb(0, 0, 0)"],
            [data-testid="stSidebar"] .stFileUploader [style*="fill: rgb(0, 0, 0)"],
            [data-testid="stSidebar"] .stFileUploader [style*="background: #000"],
            [data-testid="stSidebar"] .stFileUploader [style*="background-color: #000"] {
                background-color: transparent !important;
                color: #666666 !important;
                fill: #666666 !important;
            }
            </style>
            """, unsafe_allow_html=True)

            # 文件上传器 - 数据探索模块的唯一上传入口
            uploaded_file = st_obj.file_uploader(
                "拖拽或选择数据文件",
                type=self.accepted_types,
                help=self.help_text,
                key="data_exploration_sidebar_upload"
            )

            if uploaded_file is not None:
                try:
                    # 显示上传进度
                    with st_obj.spinner("正在处理数据文件..."):
                        # 加载数据
                        if uploaded_file.name.endswith('.csv'):
                            # 尝试不同的编码
                            file_content = uploaded_file.read()
                            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                                try:
                                    data = pd.read_csv(io.StringIO(file_content.decode(encoding)))
                                    st_obj.info(f"✅ 使用 {encoding} 编码成功解析CSV文件")
                                    break
                                except UnicodeDecodeError:
                                    continue
                            else:
                                st_obj.error("❌ 无法解码CSV文件，请检查文件编码或转换为Excel格式")
                                return None
                        else:
                            data = pd.read_excel(uploaded_file)

                        # 数据验证和清理
                        if data.empty:
                            st_obj.error("❌ 文件为空，请检查数据文件")
                            return None

                        # 删除完全为空的行和列
                        original_shape = data.shape
                        data = data.dropna(how='all').dropna(axis=1, how='all')

                        if data.empty:
                            st_obj.error("❌ 文件在清理后为空，请检查数据内容")
                            return None

                        # 显示数据清理信息
                        if original_shape != data.shape:
                            st_obj.info(f"🧹 数据清理：{original_shape} → {data.shape}")

                        # 存储到统一状态管理器
                        self.store_exploration_data(data, uploaded_file.name)

                        # 简化显示，避免黑色方框问题
                        st_obj.success(f"✅ 数据上传成功！")
                        st_obj.info(f"📊 数据形状: {data.shape[0]} 行 × {data.shape[1]} 列")

                        return data

                except Exception as e:
                    st_obj.error(f"❌ 数据加载失败: {str(e)}")
                    st_obj.error("请检查文件格式是否正确，或尝试转换为标准的CSV/Excel格式")
                    return None

            return None

    def store_exploration_data(self, data: pd.DataFrame, file_name: str):
        """存储数据到统一状态管理器 - 确保所有分析模块都能访问"""
        try:
            # 使用数据探索适配器存储数据
            from dashboard.state_management.adapters.data_exploration_adapter import get_data_exploration_adapter

            adapter = get_data_exploration_adapter()
            if adapter:
                # 检查是否需要重新存储数据（避免重复打印）
                current_data_hash = hash(str(data.shape) + file_name)
                last_stored_hash = getattr(self, '_last_stored_data_hash', None)

                if current_data_hash != last_stored_hash:
                    # 存储到各个分析模块，确保数据共享
                    success_count = 0
                    for module in ['stationarity', 'time_lag_corr', 'lead_lag']:
                        success = adapter.set_module_data(module, data, file_name, 'upload')
                        if success:
                            success_count += 1
                        # 移除每个模块的单独打印，减少日志噪音

                    # 只打印一次总结信息
                    if success_count == 3:
                        print(f"✅ 数据已存储到所有分析模块，供分析使用: {data.shape}")
                    else:
                        print(f"⚠️ 数据存储部分成功: {success_count}/3 个模块")

                    # 记录当前数据hash，避免重复处理
                    self._last_stored_data_hash = current_data_hash
                # 如果数据没有变化，不打印任何信息
            else:
                print("❌ 数据探索适配器不可用")

        except Exception as e:
            print(f"❌ 存储数据探索数据失败: {e}")

    def render_data_overview(self, st_obj, data: pd.DataFrame, file_name: str):
        """渲染数据概览 - 增强版本"""
        # 基本统计信息
        col1, col2, col3 = st_obj.columns(3)

        with col1:
            st_obj.metric("📊 行数", f"{data.shape[0]:,}")

        with col2:
            st_obj.metric("📋 列数", data.shape[1])

        with col3:
            memory_mb = data.memory_usage(deep=True).sum() / 1024 / 1024
            st_obj.metric("💾 内存", f"{memory_mb:.1f}MB")

        # 数据类型信息
        st_obj.markdown("**📈 数据类型分布：**")
        type_counts = data.dtypes.value_counts()
        type_info = " | ".join([f"{dtype}: {count}" for dtype, count in type_counts.items()])
        st_obj.caption(type_info)

        # 显示数据预览
        with st_obj.expander("🔍 数据预览 (前5行)", expanded=False):
            st_obj.dataframe(data.head(5), use_container_width=True)

        # 显示列名信息
        with st_obj.expander("📝 列名列表", expanded=False):
            cols_text = ", ".join(data.columns.tolist())
            st_obj.text(cols_text)

    def render_current_data_status(self, st_obj):
        """显示当前数据状态 - 增强版本"""
        try:
            from ..utils.state_helpers import get_tools_refactor_instance

            tools_refactor = get_tools_refactor_instance()
            if tools_refactor:
                current_data = tools_refactor.get_tools_state('data_exploration', 'current_data')
                current_data_name = tools_refactor.get_tools_state('data_exploration', 'current_data_name')
                current_data_info = tools_refactor.get_tools_state('data_exploration', 'current_data_info')

                st_obj.markdown("---")
                st_obj.markdown("#### 📊 当前数据状态")

                if current_data is not None and current_data_info:
                    # 显示数据信息
                    st_obj.success(f"✅ 已加载：{current_data_name}")

                    # 显示详细信息
                    col1, col2 = st_obj.columns(2)
                    with col1:
                        st_obj.caption(f"📏 {current_data_info['shape'][0]}行 × {current_data_info['shape'][1]}列")
                    with col2:
                        upload_time = current_data_info.get('upload_time', '')
                        if upload_time:
                            time_str = upload_time.split('T')[1][:8] if 'T' in upload_time else upload_time
                            st_obj.caption(f"⏰ {time_str}")

                    # 数据可用性提示
                    st_obj.info("💡 数据已在三个分析模块间共享，可直接进行分析")
                else:
                    st_obj.warning("⚠️ 暂无数据，请上传数据文件")
                    st_obj.markdown("""
                    <div style="font-size: 0.8em; color: #666;">
                    📋 <strong>支持格式：</strong><br>
                    • CSV文件 (UTF-8, GBK, GB2312编码)<br>
                    • Excel文件 (.xlsx, .xls)
                    </div>
                    """, unsafe_allow_html=True)
        except Exception as e:
            st_obj.error(f"❌ 显示数据状态失败: {e}")
            print(f"显示数据状态失败: {e}")

    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return ['data_exploration_sidebar_upload']


class DFMDataUploadSidebar(SidebarComponent):
    """DFM数据上传侧边栏组件"""

    def __init__(self):
        super().__init__()
        self._supported_formats = ['xlsx', 'xls']
        self._max_file_size = 100 * 1024 * 1024  # 100MB

    def render(self, st_obj, **kwargs) -> Dict[str, Any]:
        """渲染DFM数据上传侧边栏"""
        st_obj.subheader("📁 DFM 数据上传")

        # 检查是否已有上传的文件
        existing_file = self._get_existing_file()

        if existing_file:
            # 显示已上传文件的信息
            st_obj.success(f"✅ 已上传文件: {existing_file['name']}")

            # 显示文件信息
            col1, col2 = st_obj.columns(2)
            with col1:
                st_obj.metric("文件大小", f"{existing_file.get('size', 0):.2f} MB")
            with col2:
                st_obj.metric("上传时间", existing_file.get('upload_time', '未知'))

            # 提供重新上传选项
            if st_obj.button("🔄 重新上传文件", key="dfm_reupload_btn"):
                self._clear_existing_file()
                st_obj.rerun()

            return {
                'has_file': True,
                'file_info': existing_file,
                'uploaded_file': None
            }

        # 文件上传器
        upload_key = kwargs.get('upload_key', 'dfm_data_upload')
        from dashboard.ui.utils.debug_helpers import debug_log
        debug_log(f"DFM上传器 - 使用上传键: {upload_key}", "DEBUG")
        uploaded_file = st_obj.file_uploader(
            "选择Excel数据文件",
            type=self._supported_formats,
            help="请上传包含时间序列数据的Excel文件（支持.xlsx, .xls格式）",
            key=upload_key
        )

        debug_log(f"DFM上传器 - uploaded_file: {uploaded_file is not None}", "DEBUG")
        if uploaded_file is not None:
            debug_log(f"DFM上传器 - 文件名: {uploaded_file.name}, 大小: {len(uploaded_file.getvalue())} bytes", "DEBUG")
            # 验证文件
            validation_result = self._validate_file(uploaded_file)

            if validation_result['valid']:
                # 保存文件到DFM状态管理
                save_result = self._save_file_to_dfm_state(uploaded_file)

                if save_result['success']:
                    st_obj.success(f"✅ 文件 '{uploaded_file.name}' 上传成功！")
                    st_obj.info("📅 文件已加载，可用于DFM模型的各个功能模块。")

                    # 显示文件信息
                    file_size = len(uploaded_file.getvalue()) / 1024 / 1024
                    st_obj.info(f"📊 文件大小: {file_size:.2f} MB")

                    return {
                        'has_file': True,
                        'uploaded_file': uploaded_file,
                        'file_size': file_size,
                        'save_result': save_result
                    }
                else:
                    st_obj.error(f"❌ 文件保存失败: {save_result['error']}")
            else:
                st_obj.error(f"❌ 文件验证失败: {validation_result['error']}")



        return {
            'has_file': False,
            'uploaded_file': None
        }

    def _get_existing_file(self) -> Optional[Dict[str, Any]]:
        """获取已存在的文件信息"""
        try:
            # 使用DFM统一状态管理系统
            from dashboard.state_management.refactor import get_global_dfm_refactor

            dfm_refactor = get_global_dfm_refactor()
            if dfm_refactor:
                # 获取文件对象和路径
                file_obj = dfm_refactor.get_dfm_state('data_prep', 'dfm_training_data_file', None)
                file_path = dfm_refactor.get_dfm_state('data_prep', 'dfm_uploaded_excel_file_path', None)

                if file_obj and file_path:
                    # 计算文件大小
                    file_size = len(file_obj.getvalue()) / 1024 / 1024 if hasattr(file_obj, 'getvalue') else 0

                    return {
                        'name': file_path,
                        'size': file_size,
                        'upload_time': datetime.now().strftime('%H:%M:%S'),
                        'file_obj': file_obj
                    }
            return None
        except Exception as e:
            print(f"获取已存在文件失败: {e}")
            return None

    def _clear_existing_file(self) -> None:
        """清除已存在的文件"""
        try:
            from dashboard.state_management.refactor import get_global_dfm_refactor

            dfm_refactor = get_global_dfm_refactor()
            if dfm_refactor:
                # 清除相关状态
                dfm_refactor.set_dfm_state('data_prep', 'dfm_training_data_file', None)
                dfm_refactor.set_dfm_state('data_prep', 'dfm_uploaded_excel_file_path', None)
                dfm_refactor.set_dfm_state('data_prep', 'dfm_file_processed', False)
                dfm_refactor.set_dfm_state('data_prep', 'dfm_date_detection_needed', True)
        except Exception as e:
            print(f"清除已存在文件失败: {e}")

    def _validate_file(self, uploaded_file) -> Dict[str, Any]:
        """验证上传的文件"""
        try:
            # 检查文件格式
            if not self._is_valid_format(uploaded_file.name):
                return {
                    'valid': False,
                    'error': f"不支持的文件格式。请上传 {', '.join(self._supported_formats)} 格式的文件。"
                }

            # 检查文件大小
            file_size = len(uploaded_file.getvalue())
            if file_size > self._max_file_size:
                return {
                    'valid': False,
                    'error': f"文件大小超过限制（{self._max_file_size / 1024 / 1024:.0f}MB）。"
                }

            # 检查文件是否为空
            if file_size == 0:
                return {
                    'valid': False,
                    'error': "文件为空，请选择有效的数据文件。"
                }

            return {'valid': True}

        except Exception as e:
            return {
                'valid': False,
                'error': f"文件验证失败: {str(e)}"
            }

    def _is_valid_format(self, filename: str) -> bool:
        """检查文件格式是否有效"""
        if not filename:
            return False

        file_extension = filename.lower().split('.')[-1]
        return file_extension in self._supported_formats

    def _save_file_to_dfm_state(self, uploaded_file) -> Dict[str, Any]:
        """保存文件到DFM状态管理"""
        try:
            print(f"🔥 [侧边栏上传] 开始保存文件到DFM状态: {uploaded_file.name}")
            from dashboard.state_management.refactor import get_global_dfm_refactor

            dfm_refactor = get_global_dfm_refactor()
            if dfm_refactor:
                # 保存文件对象和相关信息
                print(f"🔥 [侧边栏上传] 保存文件对象...")
                success1 = dfm_refactor.set_dfm_state('data_prep', 'dfm_training_data_file', uploaded_file)
                print(f"🔥 [侧边栏上传] 保存文件路径...")
                success2 = dfm_refactor.set_dfm_state('data_prep', 'dfm_uploaded_excel_file_path', uploaded_file.name)
                print(f"🔥 [侧边栏上传] 保存配置标志...")
                success3 = dfm_refactor.set_dfm_state('data_prep', 'dfm_use_full_data_preparation', True)
                success4 = dfm_refactor.set_dfm_state('data_prep', 'dfm_file_processed', False)
                success5 = dfm_refactor.set_dfm_state('data_prep', 'dfm_date_detection_needed', True)

                print(f"🔥 [侧边栏上传] 保存结果: {success1}, {success2}, {success3}, {success4}, {success5}")

                if all([success1, success2, success3, success4, success5]):
                    print(f"🔥 [侧边栏上传] 文件保存成功: {uploaded_file.name}")
                    return {
                        'success': True,
                        'message': '文件保存成功'
                    }
                else:
                    print(f"🔥 [侧边栏上传] 文件保存失败，部分状态设置失败")
                    return {
                        'success': False,
                        'error': 'DFM状态管理器保存失败'
                    }
            else:
                print(f"🔥 [侧边栏上传] DFM状态管理器不可用")
                return {
                    'success': False,
                    'error': 'DFM状态管理器不可用'
                }

        except Exception as e:
            print(f"🔥 [侧边栏上传] 保存文件异常: {str(e)}")
            return {
                'success': False,
                'error': f"保存文件失败: {str(e)}"
            }

    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return [
            'dfm_training_data_file',
            'dfm_uploaded_excel_file_path',
            'dfm_use_full_data_preparation',
            'dfm_file_processed',
            'dfm_date_detection_needed'
        ]


def render_complete_sidebar(
    module_config: Dict[str, Any],
    nav_manager: Any,
    key_prefix: str = "sidebar"
) -> Dict[str, Any]:
    """
    渲染完整的侧边栏，包括标题、模块选择器、暂存数据等

    Args:
        module_config: 模块配置字典
        nav_manager: 导航管理器
        key_prefix: 组件key前缀

    Returns:
        Dict[str, Any]: 侧边栏渲染结果
    """
    # 🔥 修复：移除有问题的重复渲染检查，让侧边栏每次都正常渲染
    # 这个检查导致点击模块按钮后侧边栏消失的问题

    if not validate_sidebar_config(module_config):
        return {'error': 'Invalid module configuration'}

    with create_sidebar_container():
        # 渲染侧边栏标题
        st.title(get_sidebar_title())

        # === 第一层：主模块选择器 ===
        st.markdown("### 🏠 主模块")

        main_module_options = list(module_config.keys())

        # 获取当前状态
        current_main_module = nav_manager.get_current_main_module() if nav_manager else '数据预览'

        # 强制清除按钮状态缓存，确保每次都重新计算
        try:
            from dashboard.ui.utils.button_state_manager import clear_button_state_cache
            clear_button_state_cache()
        except Exception as e:
            pass  # 忽略清除缓存失败的错误

        if current_main_module not in main_module_options:
            current_main_module = main_module_options[0]
            if nav_manager:
                nav_manager.set_current_main_module(current_main_module)

        # 渲染主模块选择器
        main_module_result = render_main_module_selector(
            main_module_options, current_main_module, nav_manager, f"{key_prefix}_main"
        )

        # 获取更新后的主模块状态用于子模块选择器
        updated_main_module = main_module_result.get('selected_module', current_main_module)

        # === 视觉分割线 ===
        st.markdown("---")

        # === 第二层：子模块选择器 ===
        current_sub_module = nav_manager.get_current_sub_module() if nav_manager else None
        sub_module_result = None

        sub_config = module_config[updated_main_module]
        if isinstance(sub_config, dict):  # 有子模块
            st.markdown("### 📂 功能模块")
            st.caption(f"当前主模块：{updated_main_module}")

            # 添加一些间距来实现视觉分割
            st.markdown("")  # 空行

            sub_module_options = list(sub_config.keys())

            # 渲染子模块选择器
            sub_module_result = render_sub_module_selector(
                sub_module_options, current_sub_module, updated_main_module,
                nav_manager, f"{key_prefix}_sub"
            )

            st.markdown("")  # 空行
        else:
            # 没有子模块的情况，显示提示
            st.markdown("### 📂 功能模块")
            st.info(f"'{updated_main_module}' 模块内容将在右侧显示")

        # 渲染分隔线
        st.markdown("---")

        # 渲染暂存数据部分 - 仅在数据预处理模块中显示
        staged_data_info = None
        current_sub_module = sub_module_result.get('selected_sub_module') if sub_module_result else None

        if updated_main_module == "应用工具" and current_sub_module == "数据预处理":
            # 获取数据预处理模块的暂存数据
            staged_data = get_preprocessing_staged_data()
            staged_data_info = render_staged_data_section(staged_data)
        else:
            # 其他模块不显示暂存数据，返回默认值
            staged_data_info = {
                'has_data': False,
                'data_count': 0,
                'datasets': {}
            }

        # 渲染数据上传部分（如果适用）
        upload_info = None
        if sub_module_result and sub_module_result.get('selected_sub_module'):
            upload_config = get_upload_section_config(
                updated_main_module, sub_module_result['selected_sub_module']
            )
            if upload_config['show_upload']:
                upload_info = render_data_upload_section(
                    updated_main_module, sub_module_result['selected_sub_module']
                )

    # 🔥 修复：返回侧边栏渲染结果，不再设置有问题的缓存标志
    result = {
        'main_module_result': main_module_result,
        'sub_module_result': sub_module_result,
        'staged_data_info': staged_data_info,
        'upload_info': upload_info
    }

    # 移除有问题的渲染标志设置，让侧边栏每次都能正常渲染
    return result


def render_staged_data_section(staged_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    渲染暂存数据部分

    Args:
        staged_data: 暂存数据字典

    Returns:
        Dict[str, Any]: 暂存数据信息
    """
    st.subheader("暂存数据")

    if not staged_data:
        st.info("暂无暂存数据")
        return {
            'has_data': False,
            'data_count': 0,
            'datasets': {}
        }

    # 显示暂存数据信息
    st.write(f"共有 {len(staged_data)} 个数据集：")
    for name, data_info in staged_data.items():
        if isinstance(data_info, dict) and 'name' in data_info:
            st.write(f"• {data_info['name']}")
        else:
            st.write(f"• {name}")

    return {
        'has_data': True,
        'data_count': len(staged_data),
        'datasets': staged_data
    }


def render_data_upload_section(main_module: str, sub_module: str) -> Dict[str, Any]:
    """
    渲染数据上传部分 - 支持DFM模块和其他模块

    Args:
        main_module: 主模块名称
        sub_module: 子模块名称

    Returns:
        Dict[str, Any]: 上传部分信息
    """
    upload_config = get_upload_section_config(main_module, sub_module)

    if not upload_config['show_upload']:
        return {'show_upload': False}

    # DFM模块使用专用的上传组件
    from dashboard.ui.utils.debug_helpers import debug_log
    debug_log(f"侧边栏选择 - 主模块: '{main_module}', 子模块: '{sub_module}'", "DEBUG")
    if main_module == "模型分析" and sub_module == "DFM 模型":
        debug_log("侧边栏选择 - 使用DFM专用上传组件", "DEBUG")
        dfm_upload_sidebar = DFMDataUploadSidebar()
        # 🔥 修复：使用稳定的key，避免每次渲染都变化
        stable_upload_key = 'dfm_sidebar_upload_stable'
        upload_result = dfm_upload_sidebar.render(st, upload_key=stable_upload_key)
        debug_log(f"侧边栏选择 - DFM上传组件渲染结果: {upload_result.get('has_file', False)}", "DEBUG")

        return {
            'show_upload': True,
            'main_module': main_module,
            'sub_module': sub_module,
            'upload_type': 'dfm_data',
            'upload_result': upload_result,
            'upload_title': upload_config['title'],
            'upload_description': upload_config['description']
        }

    # 数据探索模块不渲染额外标题，因为DataExplorationSidebar已经处理了
    elif main_module == "应用工具" and sub_module == "数据探索":
        # 不渲染标题，避免重复
        pass
    else:
        # 其他模块正常渲染标题
        st.subheader(upload_config['title'])
        st.write(upload_config['description'])

    return {
        'show_upload': True,
        'main_module': main_module,
        'sub_module': sub_module,
        'upload_title': upload_config['title'],
        'upload_description': upload_config['description']
    }


@contextmanager
def create_sidebar_container():
    """
    创建侧边栏容器的上下文管理器

    Yields:
        侧边栏上下文
    """
    with st.sidebar:
        yield st.sidebar


def get_sidebar_title(custom_title: str = None) -> str:
    """
    获取侧边栏标题

    Args:
        custom_title: 自定义标题

    Returns:
        str: 侧边栏标题
    """
    return custom_title or "📈 经济运行分析平台"


def get_module_selector_title(selector_type: str, main_module: str = None) -> str:
    """
    获取模块选择器标题

    Args:
        selector_type: 选择器类型 ('main' 或 'sub')
        main_module: 主模块名称（用于子模块标题）

    Returns:
        str: 选择器标题
    """
    if selector_type == 'main':
        return "选择功能模块"
    elif selector_type == 'sub':
        if main_module:
            return f"{main_module} 子选项"
        else:
            return "子选项"
    else:
        return "模块选择"


def validate_sidebar_config(config: Any) -> bool:
    """
    验证侧边栏配置的有效性

    Args:
        config: 配置对象

    Returns:
        bool: 配置是否有效
    """
    if not config or not isinstance(config, dict):
        return False

    if len(config) == 0:
        return False

    return True


def get_upload_section_config(main_module: str, sub_module: str) -> Dict[str, Any]:
    """
    获取上传部分的配置

    Args:
        main_module: 主模块名称
        sub_module: 子模块名称

    Returns:
        Dict[str, Any]: 上传配置
    """
    # 定义支持上传的模块组合
    upload_configs = {
        ('监测分析', '工业'): {
            'show_upload': True,
            'title': '📁 工业监测分析数据上传',
            'description': '上传一个Excel文件，同时支持宏观运行和企业经营分析'
        },
        ('监测分析', '消费'): {
            'show_upload': True,
            'title': '📁 消费监测分析数据上传',
            'description': '上传一个Excel文件，进行消费数据分析'
        },
        ('应用工具', '数据探索'): {
            'show_upload': True,
            'title': '📁 数据探索 - 数据上传',
            'description': '上传数据文件进行探索性分析'
        },
        ('应用工具', '数据预处理'): {
            'show_upload': True,
            'title': '📁 数据预处理 - 数据上传',
            'description': '上传数据文件进行预处理'
        },
        ('模型分析', 'DFM 模型'): {
            'show_upload': True,
            'title': '📁 DFM 模型数据上传',
            'description': '上传Excel数据文件，用于DFM模型的数据准备、训练和分析',
            'upload_type': 'dfm_data'
        }
    }

    key = (main_module, sub_module)
    return upload_configs.get(key, {'show_upload': False})


def handle_sidebar_state_changes(
    main_result: Dict[str, Any],
    sub_result: Optional[Dict[str, Any]],
    nav_manager: Any
) -> Dict[str, Any]:
    """
    处理侧边栏状态变化

    Args:
        main_result: 主模块选择结果
        sub_result: 子模块选择结果
        nav_manager: 导航管理器

    Returns:
        Dict[str, Any]: 状态变化处理结果
    """
    main_changed = main_result.get('has_change', False)
    sub_changed = sub_result.get('has_change', False) if sub_result else False

    current_main = main_result.get('selected_module')
    current_sub = sub_result.get('selected_sub_module') if sub_result else None

    if main_changed or sub_changed:
        debug_navigation(
            "侧边栏状态变化",
            f"主模块: {current_main}, 子模块: {current_sub}, "
            f"主模块变化: {main_changed}, 子模块变化: {sub_changed}"
        )

    return {
        'main_changed': main_changed,
        'sub_changed': sub_changed,
        'current_main': current_main,
        'current_sub': current_sub,
        'nav_manager_available': nav_manager is not None
    }


def get_preprocessing_staged_data() -> dict:
    """
    获取数据预处理模块的暂存数据

    Returns:
        dict: 暂存数据字典
    """
    try:
        from ..utils.state_helpers import get_tools_refactor_instance

        tools_refactor = get_tools_refactor_instance()
        if tools_refactor:
            # 获取数据输入模块的暂存数据
            data_input_staged = tools_refactor.get_tools_state('data_input', 'staging.staged_data_dict', {})
            # 获取时间序列清洗模块的暂存数据
            time_series_staged = tools_refactor.get_tools_state('time_series_clean', 'staged_data_dict', {})

            # 合并两个来源的暂存数据
            combined_staged = {}
            combined_staged.update(data_input_staged)
            combined_staged.update(time_series_staged)

            return combined_staged
        return {}
    except Exception as e:
        print(f"获取数据预处理暂存数据失败: {e}")
        return {}


__all__ = [
    'SidebarComponent', 'DataUploadSidebar', 'NavigationSidebar', 'FilterSidebar',
    'DataExplorationSidebar', 'DFMDataUploadSidebar',
    'render_complete_sidebar', 'render_staged_data_section', 'render_data_upload_section',
    'create_sidebar_container', 'get_sidebar_title', 'get_module_selector_title',
    'validate_sidebar_config', 'get_upload_section_config', 'handle_sidebar_state_changes',
    'get_preprocessing_staged_data'
]
