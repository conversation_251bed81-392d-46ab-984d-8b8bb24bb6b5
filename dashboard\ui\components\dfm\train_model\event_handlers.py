"""
DFM训练事件处理器

该模块实现了各种训练事件的处理逻辑，负责将事件转换为状态更新。
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

from .events import (
    BaseEvent, TrainingStartedEvent, TrainingProgressEvent, 
    TrainingLogEvent, TrainingCompletedEvent, TrainingFailedEvent,
    TrainingCancelledEvent
)

logger = logging.getLogger(__name__)


class TrainingEventHandler:
    """
    训练事件处理器

    负责处理各种训练事件并更新相应的状态
    """

    def __init__(self, state_setter_func=None, state_getter_func=None):
        """
        初始化事件处理器

        Args:
            state_setter_func: 状态设置函数，用于更新DFM状态
            state_getter_func: 状态获取函数，用于读取DFM状态
        """
        self.state_setter = state_setter_func
        self.state_getter = state_getter_func
        self.processed_events = []
        self.current_training_id = None

        logger.info("TrainingEventHandler initialized")
    
    def set_state_setter(self, state_setter_func) -> None:
        """
        设置状态设置函数
        
        Args:
            state_setter_func: 状态设置函数
        """
        self.state_setter = state_setter_func
    
    def process_events(self, events: List[BaseEvent]) -> Dict[str, Any]:
        """
        批量处理事件列表
        
        Args:
            events: 要处理的事件列表
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        if not events:
            return {'processed_count': 0, 'errors': []}
        
        results = {
            'processed_count': 0,
            'errors': [],
            'ui_update_required': False,
            'training_completed': False
        }
        
        try:
            for event in events:
                try:
                    event_result = self.handle_event(event)
                    results['processed_count'] += 1
                    
                    # 检查是否需要UI更新
                    if event_result.get('ui_update_required', False):
                        results['ui_update_required'] = True
                    
                    # 检查训练是否完成
                    if isinstance(event, (TrainingCompletedEvent, TrainingFailedEvent)):
                        results['training_completed'] = True
                    
                    # 记录处理的事件
                    self.processed_events.append({
                        'event': event,
                        'processed_at': datetime.now(),
                        'result': event_result
                    })
                    
                except Exception as e:
                    error_msg = f"Error processing event {event}: {e}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            logger.info(f"Processed {results['processed_count']} events, "
                       f"errors: {len(results['errors'])}, "
                       f"UI update required: {results['ui_update_required']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch event processing: {e}")
            results['errors'].append(str(e))
            return results
    
    def handle_event(self, event: BaseEvent) -> Dict[str, Any]:
        """
        处理单个事件
        
        Args:
            event: 要处理的事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        handler_map = {
            TrainingStartedEvent: self.handle_training_started,
            TrainingProgressEvent: self.handle_training_progress,
            TrainingLogEvent: self.handle_training_log,
            TrainingCompletedEvent: self.handle_training_completed,
            TrainingFailedEvent: self.handle_training_failed,
            TrainingCancelledEvent: self.handle_training_cancelled
        }
        
        handler = handler_map.get(type(event))
        if handler:
            return handler(event)
        else:
            logger.warning(f"No handler found for event type: {type(event)}")
            return {'handled': False, 'ui_update_required': False}
    
    def handle_training_started(self, event: TrainingStartedEvent) -> Dict[str, Any]:
        """
        处理训练开始事件
        
        Args:
            event: 训练开始事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.current_training_id = event.training_id
            
            if self.state_setter:
                self.state_setter('dfm_training_status', '正在训练...')
                self.state_setter('dfm_training_start_time', event.timestamp.isoformat())
                self.state_setter('dfm_training_log', [])
                self.state_setter('dfm_training_progress', 0)
                
                # 清理之前的结果
                self.state_setter('dfm_model_results_paths', None)
                self.state_setter('dfm_training_error', None)
            
            logger.info(f"Training started: {event.training_id}")
            
            return {
                'handled': True,
                'ui_update_required': True,
                'status_updated': True
            }
            
        except Exception as e:
            logger.error(f"Error handling training started event: {e}")
            return {'handled': False, 'error': str(e)}
    
    def handle_training_progress(self, event: TrainingProgressEvent) -> Dict[str, Any]:
        """
        处理训练进度事件
        
        Args:
            event: 训练进度事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            if self.state_setter:
                if event.progress_percentage is not None:
                    self.state_setter('dfm_training_progress', event.progress_percentage)
                
                if event.current_step:
                    self.state_setter('dfm_current_training_step', event.current_step)
            
            logger.debug(f"Training progress: {event.progress_percentage}% - {event.current_step}")
            
            return {
                'handled': True,
                'ui_update_required': True,
                'progress_updated': True
            }
            
        except Exception as e:
            logger.error(f"Error handling training progress event: {e}")
            return {'handled': False, 'error': str(e)}
    
    def handle_training_log(self, event: TrainingLogEvent) -> Dict[str, Any]:
        """
        处理训练日志事件
        
        Args:
            event: 训练日志事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            if self.state_setter:
                # 获取当前日志
                current_log = self._get_current_log()
                
                # 添加新日志条目
                timestamp = event.timestamp.strftime('%H:%M:%S')
                log_entry = f"[{timestamp}] {event.message}"
                current_log.append(log_entry)
                
                # 限制日志条目数量
                if len(current_log) > 100:
                    current_log = current_log[-100:]
                
                self.state_setter('dfm_training_log', current_log)
            
            logger.debug(f"Training log: {event.message}")
            
            return {
                'handled': True,
                'ui_update_required': True,
                'log_updated': True
            }
            
        except Exception as e:
            logger.error(f"Error handling training log event: {e}")
            return {'handled': False, 'error': str(e)}
    
    def handle_training_completed(self, event: TrainingCompletedEvent) -> Dict[str, Any]:
        """
        处理训练完成事件
        
        Args:
            event: 训练完成事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            if self.state_setter:
                self.state_setter('dfm_training_status', '训练完成')
                self.state_setter('dfm_training_end_time', event.timestamp.isoformat())
                self.state_setter('dfm_model_results_paths', event.result_files)
                self.state_setter('dfm_training_progress', 100)
                
                if event.training_duration:
                    self.state_setter('dfm_training_duration', event.training_duration)
                
                if event.model_metrics:
                    self.state_setter('dfm_model_metrics', event.model_metrics)
            
            logger.info(f"Training completed successfully with {len(event.result_files)} result files")
            
            return {
                'handled': True,
                'ui_update_required': True,
                'training_completed': True,
                'result_files': event.result_files
            }
            
        except Exception as e:
            logger.error(f"Error handling training completed event: {e}")
            return {'handled': False, 'error': str(e)}
    
    def handle_training_failed(self, event: TrainingFailedEvent) -> Dict[str, Any]:
        """
        处理训练失败事件
        
        Args:
            event: 训练失败事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            if self.state_setter:
                self.state_setter('dfm_training_status', f'训练失败: {event.error_message}')
                self.state_setter('dfm_training_end_time', event.timestamp.isoformat())
                self.state_setter('dfm_training_error', {
                    'message': event.error_message,
                    'type': event.error_type,
                    'stack_trace': event.stack_trace,
                    'failed_step': event.failed_step
                })
            
            logger.error(f"Training failed: {event.error_message}")
            
            return {
                'handled': True,
                'ui_update_required': True,
                'training_failed': True,
                'error_message': event.error_message
            }
            
        except Exception as e:
            logger.error(f"Error handling training failed event: {e}")
            return {'handled': False, 'error': str(e)}
    
    def handle_training_cancelled(self, event: TrainingCancelledEvent) -> Dict[str, Any]:
        """
        处理训练取消事件
        
        Args:
            event: 训练取消事件
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            if self.state_setter:
                self.state_setter('dfm_training_status', '训练已取消')
                self.state_setter('dfm_training_end_time', event.timestamp.isoformat())
                
                if event.cancellation_reason:
                    self.state_setter('dfm_cancellation_reason', event.cancellation_reason)
            
            logger.info(f"Training cancelled: {event.cancellation_reason}")
            
            return {
                'handled': True,
                'ui_update_required': True,
                'training_cancelled': True
            }
            
        except Exception as e:
            logger.error(f"Error handling training cancelled event: {e}")
            return {'handled': False, 'error': str(e)}
    
    def _get_current_log(self) -> List[str]:
        """
        获取当前日志列表

        Returns:
            List[str]: 当前日志列表
        """
        if self.state_getter:
            return self.state_getter('dfm_training_log', [])
        return []
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取事件处理统计信息
        
        Returns:
            Dict[str, Any]: 处理统计信息
        """
        return {
            'total_processed': len(self.processed_events),
            'current_training_id': self.current_training_id,
            'last_processed_at': self.processed_events[-1]['processed_at'].isoformat() if self.processed_events else None
        }
