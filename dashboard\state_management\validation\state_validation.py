# -*- coding: utf-8 -*-
"""
状态管理验证工具
用于验证tools模块状态管理的一致性和完整性
"""

import logging
import json
from typing import Dict, Any, List, Tuple
from datetime import datetime
import os

# 导入状态管理相关模块
from dashboard.state_management import get_unified_manager
from dashboard.tools.time_series_pretreat.time_series_clean.shared_state import (
    get_state_manager_health,
    validate_state_consistency,
    get_module_state_summary
)

logger = logging.getLogger(__name__)


class StateValidation:
    """状态管理验证工具类"""
    
    def __init__(self):
        self.unified_manager = get_unified_manager()
        self.validation_results = {}
        
    def validate_unified_manager(self) -> Dict[str, Any]:
        """验证统一状态管理器"""
        validation = {
            'component': 'unified_manager',
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'issues': [],
            'details': {}
        }
        
        try:
            if self.unified_manager is None:
                validation['status'] = 'failed'
                validation['issues'].append('统一状态管理器不可用')
                return validation
            
            # 测试基本功能
            test_key = 'validation.test.key'
            test_value = {'test': True, 'timestamp': datetime.now().isoformat()}
            
            # 测试设置
            set_result = self.unified_manager.set_state(test_key, test_value)
            if not set_result:
                validation['issues'].append('状态设置功能失败')
            
            # 测试获取
            get_result = self.unified_manager.get_state(test_key)
            if get_result != test_value:
                validation['issues'].append('状态获取功能失败')
            
            # 测试删除
            clear_result = self.unified_manager.clear_state(test_key)
            if not clear_result:
                validation['issues'].append('状态清除功能失败')
            
            # 测试获取所有键
            try:
                all_keys = self.unified_manager.get_all_keys()
                validation['details']['total_keys'] = len(all_keys) if all_keys else 0
            except Exception as e:
                validation['issues'].append(f'获取所有键失败: {e}')
            
            # 测试适配器
            tools_adapter = self.unified_manager.get_adapter('tools')
            if tools_adapter is None:
                validation['issues'].append('Tools适配器不可用')
            else:
                validation['details']['tools_adapter_available'] = True
            
            validation['status'] = 'passed' if not validation['issues'] else 'failed'
            
        except Exception as e:
            validation['status'] = 'error'
            validation['issues'].append(f'验证过程出错: {e}')
            logger.error(f"统一状态管理器验证失败: {e}")
        
        return validation
    
    def validate_tools_adapter(self) -> Dict[str, Any]:
        """验证Tools适配器"""
        validation = {
            'component': 'tools_adapter',
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'issues': [],
            'details': {}
        }
        
        try:
            tools_adapter = self.unified_manager.get_adapter('tools')
            if tools_adapter is None:
                validation['status'] = 'failed'
                validation['issues'].append('Tools适配器不可用')
                return validation
            
            # 检查状态键映射
            if hasattr(tools_adapter, 'state_key_mapping'):
                mapping = tools_adapter.state_key_mapping
                validation['details']['modules_mapped'] = list(mapping.keys())
                
                # 检查键冲突
                all_target_keys = []
                for module, keys in mapping.items():
                    all_target_keys.extend(keys.values())
                
                duplicates = [key for key in set(all_target_keys) if all_target_keys.count(key) > 1]
                if duplicates:
                    validation['issues'].append(f'发现重复的目标键: {duplicates}')
                
                validation['details']['total_mapped_keys'] = len(all_target_keys)
                validation['details']['unique_target_keys'] = len(set(all_target_keys))
            
            # 测试状态操作
            test_module = 'time_series_pretreat'
            test_key = 'validation_test'
            test_value = 'validation_value'
            
            # 测试设置状态
            set_result = tools_adapter.set_state(test_module, test_key, test_value)
            if not set_result:
                validation['issues'].append('Tools适配器状态设置失败')
            
            # 测试获取状态
            get_result = tools_adapter.get_state(test_module, test_key)
            if get_result != test_value:
                validation['issues'].append('Tools适配器状态获取失败')
            
            # 清理测试数据
            try:
                tools_adapter.set_state(test_module, test_key, None)
            except:
                pass
            
            validation['status'] = 'passed' if not validation['issues'] else 'failed'
            
        except Exception as e:
            validation['status'] = 'error'
            validation['issues'].append(f'验证过程出错: {e}')
            logger.error(f"Tools适配器验证失败: {e}")
        
        return validation
    
    def validate_shared_state_module(self) -> Dict[str, Any]:
        """验证共享状态模块"""
        validation = {
            'component': 'shared_state_module',
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'issues': [],
            'details': {}
        }
        
        try:
            # 测试健康状态
            health_info = get_state_manager_health()
            validation['details']['health_info'] = health_info
            
            if not health_info.get('overall_health', False):
                validation['issues'].append('共享状态模块健康检查失败')
            
            # 测试状态一致性
            consistency_report = validate_state_consistency()
            validation['details']['consistency_report'] = consistency_report
            
            if consistency_report.get('issues'):
                validation['issues'].extend(consistency_report['issues'])
            
            # 测试模块状态摘要
            modules_to_test = ['pretreat', 'clean', 'compute', 'property', 'comparison']
            module_summaries = {}
            
            for module in modules_to_test:
                try:
                    summary = get_module_state_summary(module)
                    module_summaries[module] = summary
                except Exception as e:
                    validation['issues'].append(f'获取模块 {module} 状态摘要失败: {e}')
            
            validation['details']['module_summaries'] = module_summaries
            
            validation['status'] = 'passed' if not validation['issues'] else 'failed'
            
        except Exception as e:
            validation['status'] = 'error'
            validation['issues'].append(f'验证过程出错: {e}')
            logger.error(f"共享状态模块验证失败: {e}")
        
        return validation
    
    def validate_key_naming_convention(self) -> Dict[str, Any]:
        """验证键命名约定"""
        validation = {
            'component': 'key_naming_convention',
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'issues': [],
            'details': {}
        }
        
        try:
            all_keys = self.unified_manager.get_all_keys()
            tools_keys = [key for key in all_keys if key.startswith('tools.')]
            
            validation['details']['total_tools_keys'] = len(tools_keys)
            
            # 检查命名约定: tools.{module}.{submodule}.{key}
            convention_violations = []
            for key in tools_keys:
                parts = key.split('.')
                if len(parts) < 4:
                    convention_violations.append(key)
            
            if convention_violations:
                validation['issues'].append(f'键命名约定违规: {convention_violations[:10]}...' if len(convention_violations) > 10 else f'键命名约定违规: {convention_violations}')
                validation['details']['convention_violations'] = len(convention_violations)
            
            # 统计模块分布
            module_distribution = {}
            for key in tools_keys:
                parts = key.split('.')
                if len(parts) >= 2:
                    module = parts[1]
                    module_distribution[module] = module_distribution.get(module, 0) + 1
            
            validation['details']['module_distribution'] = module_distribution
            
            validation['status'] = 'passed' if not validation['issues'] else 'failed'
            
        except Exception as e:
            validation['status'] = 'error'
            validation['issues'].append(f'验证过程出错: {e}')
            logger.error(f"键命名约定验证失败: {e}")
        
        return validation
    
    def run_full_validation(self) -> Dict[str, Any]:
        """运行完整验证"""
        logger.info("开始Tools模块状态管理完整验证")
        
        full_report = {
            'validation_timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'components': {},
            'summary': {
                'total_components': 0,
                'passed_components': 0,
                'failed_components': 0,
                'error_components': 0
            }
        }
        
        # 运行各个组件的验证
        validations = [
            ('unified_manager', self.validate_unified_manager),
            ('tools_adapter', self.validate_tools_adapter),
            ('shared_state_module', self.validate_shared_state_module),
            ('key_naming_convention', self.validate_key_naming_convention)
        ]
        
        for component_name, validation_func in validations:
            try:
                result = validation_func()
                full_report['components'][component_name] = result
                full_report['summary']['total_components'] += 1
                
                if result['status'] == 'passed':
                    full_report['summary']['passed_components'] += 1
                elif result['status'] == 'failed':
                    full_report['summary']['failed_components'] += 1
                else:
                    full_report['summary']['error_components'] += 1
                    
            except Exception as e:
                logger.error(f"验证组件 {component_name} 时出错: {e}")
                full_report['components'][component_name] = {
                    'status': 'error',
                    'error': str(e)
                }
                full_report['summary']['error_components'] += 1
        
        # 确定总体状态
        if full_report['summary']['failed_components'] == 0 and full_report['summary']['error_components'] == 0:
            full_report['overall_status'] = 'passed'
        elif full_report['summary']['passed_components'] > 0:
            full_report['overall_status'] = 'partial'
        else:
            full_report['overall_status'] = 'failed'
        
        logger.info(f"验证完成 - 总体状态: {full_report['overall_status']}")
        return full_report
    
    def save_validation_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """保存验证报告"""
        if filename is None:
            filename = f"state_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_dir = "logs"
        os.makedirs(report_dir, exist_ok=True)
        report_path = os.path.join(report_dir, filename)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"验证报告已保存: {report_path}")
        return report_path


def run_validation() -> Dict[str, Any]:
    """运行状态管理验证的便捷函数"""
    validator = StateValidation()
    return validator.run_full_validation()


if __name__ == "__main__":
    # 命令行执行验证
    validator = StateValidation()
    report = validator.run_full_validation()
    report_path = validator.save_validation_report(report)
    
    print(f"验证完成！")
    print(f"总体状态: {report['overall_status']}")
    print(f"报告保存在: {report_path}")
    print(f"通过组件: {report['summary']['passed_components']}/{report['summary']['total_components']}")
