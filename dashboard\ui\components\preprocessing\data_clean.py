# -*- coding: utf-8 -*-
"""
数据清洗组件
整合原有的数据清洗UI组件功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from typing import List, Dict, Any, Optional, Tuple
import logging

from .base import PreprocessingComponent

logger = logging.getLogger(__name__)


class DataCleanComponent(PreprocessingComponent):
    """数据清洗组件"""
    
    def __init__(self):
        super().__init__("data_clean", "数据清洗")

    def render_input_section(self, st_obj, data: pd.DataFrame, data_name: str = "数据") -> Dict[str, Any]:
        """渲染数据清洗输入界面"""
        st_obj.markdown("#### 数据清洗配置")

        # 显示数据基本信息
        st_obj.info(f"正在处理数据: {data_name}，形状: {data.shape}")

        # 清洗选项
        clean_options = st_obj.multiselect(
            "选择清洗操作",
            ["处理缺失值", "删除重复行", "数据类型转换", "异常值检测"],
            default=["处理缺失值"],
            key=f"{self.component_id}_clean_options"
        )

        result = {
            'clean_options': clean_options,
            'data': data,
            'data_name': data_name
        }

        # 根据选择的选项显示相应界面
        if "处理缺失值" in clean_options:
            missing_result = self.render_missing_data_analysis(st_obj, data)
            result.update(missing_result)

        if "删除重复行" in clean_options:
            duplicate_result = self.render_duplicate_analysis(st_obj, data)
            result.update(duplicate_result)

        return result

    def render_missing_data_analysis(self, st_obj, data: pd.DataFrame) -> Dict[str, Any]:
        """渲染缺失数据分析界面"""

        st_obj.markdown("#### 缺失数据分析")

        # 检查缺失数据
        missing_info = data.isnull().sum()
        missing_cols = missing_info[missing_info > 0]

        if missing_cols.empty:
            st_obj.success("✅ 数据中没有缺失值")
            return {'has_missing': False, 'missing_cols': [], 'missing_info': missing_info}

        # 显示缺失数据统计
        st_obj.warning(f"发现 {len(missing_cols)} 列包含缺失值")

        # 详细统计表
        missing_df = pd.DataFrame({
            '列名': missing_cols.index,
            '缺失数量': missing_cols.values,
            '缺失比例(%)': (missing_cols.values / len(data) * 100).round(2),
            '数据类型': [str(data[col].dtype) for col in missing_cols.index]
        })

        col1, col2 = st_obj.columns(2)

        with col1:
            st_obj.markdown("**缺失数据统计表：**")
            st_obj.dataframe(missing_df, use_container_width=True)

        with col2:
            # 缺失数据可视化
            st_obj.markdown("**缺失数据分布图：**")

            fig = px.bar(
                missing_df,
                x='列名',
                y='缺失比例(%)',
                title='各列缺失数据比例',
                color='缺失比例(%)',
                color_continuous_scale='Reds'
            )
            fig.update_layout(height=300)
            st_obj.plotly_chart(fig, use_container_width=True)

        # 缺失数据模式分析
        if len(missing_cols) > 1:
            st_obj.markdown("**缺失数据模式分析：**")

            # 创建缺失数据热力图
            missing_matrix = data[missing_cols.index].isnull().astype(int)

            if len(missing_matrix) > 1000:
                # 对于大数据集，采样显示
                sample_size = 1000
                missing_matrix = missing_matrix.sample(n=sample_size)
                st_obj.info(f"数据量较大，显示随机采样的 {sample_size} 行")

            fig = px.imshow(
                missing_matrix.T,
                title='缺失数据模式热力图 (白色=缺失)',
                color_continuous_scale='RdYlBu_r',
                aspect='auto'
            )
            fig.update_layout(height=200)
            st_obj.plotly_chart(fig, use_container_width=True)

        return {
            'has_missing': True,
            'missing_cols': missing_cols.index.tolist(),
            'missing_info': missing_info,
            'missing_df': missing_df
        }

    def render_missing_data_handler(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染缺失数据处理界面"""

        # 先进行缺失数据分析
        analysis_result = self.render_missing_data_analysis(st_obj, data)

        if not analysis_result['has_missing']:
            return data

        missing_cols = analysis_result['missing_cols']
        
        st_obj.markdown("#### 缺失数据处理选项")

        # 处理方法选择
        col1, col2, col3 = st_obj.columns(3)

        with col1:
            handling_method = st_obj.selectbox(
                "缺失值处理方法:",
                options=[
                    "删除含缺失值的行",
                    "删除含缺失值的列",
                    "前向填充",
                    "后向填充",
                    "均值填充",
                    "中位数填充",
                    "众数填充",
                    "线性插值",
                    "自定义值填充"
                ],
                key=f"{self.component_name}_missing_method"
            )

        with col2:
            # 选择要处理的列
            selected_cols = st_obj.multiselect(
                "选择要处理的列:",
                options=missing_cols,
                default=missing_cols,
                key=f"{self.component_name}_missing_cols"
            )

        with col3:
            # 高级选项
            if handling_method == "自定义值填充":
                custom_value = st_obj.text_input(
                    "自定义填充值:",
                    value="0",
                    key=f"{self.component_name}_custom_value"
                )
            elif handling_method == "删除含缺失值的行":
                min_valid_ratio = st_obj.slider(
                    "最小有效数据比例:",
                    min_value=0.0,
                    max_value=1.0,
                    value=0.5,
                    step=0.1,
                    key=f"{self.component_name}_min_valid_ratio",
                    help="行中有效数据比例低于此值的行将被删除"
                )
            elif handling_method == "删除含缺失值的列":
                max_missing_ratio = st_obj.slider(
                    "最大缺失比例:",
                    min_value=0.0,
                    max_value=1.0,
                    value=0.5,
                    step=0.1,
                    key=f"{self.component_name}_max_missing_ratio",
                    help="缺失比例高于此值的列将被删除"
                )
        
        # 预览处理效果
        col1, col2 = st_obj.columns(2)

        with col1:
            if st_obj.button("预览处理效果", key=f"{self.component_name}_preview_missing"):
                if not selected_cols:
                    st_obj.error("请选择要处理的列")
                else:
                    preview_data = self._apply_missing_data_handling(
                        data.copy(), handling_method, selected_cols,
                        custom_value if handling_method == "自定义值填充" else None,
                        min_valid_ratio if handling_method == "删除含缺失值的行" else None,
                        max_missing_ratio if handling_method == "删除含缺失值的列" else None
                    )

                    if preview_data is not None:
                        st_obj.success(f"预览：数据形状从 {data.shape} 变为 {preview_data.shape}")

                        # 显示处理后的缺失数据统计
                        new_missing = preview_data.isnull().sum()
                        new_missing_cols = new_missing[new_missing > 0]

                        if new_missing_cols.empty:
                            st_obj.success("✅ 处理后无缺失值")
                        else:
                            st_obj.info(f"处理后仍有 {len(new_missing_cols)} 列包含缺失值")

        with col2:
            if st_obj.button("应用缺失值处理", key=f"{self.component_name}_apply_missing"):
                if not selected_cols:
                    st_obj.error("请选择要处理的列")
                    return data
            
            processed_data = data.copy()
            
            try:
                if handling_method == "删除含缺失值的行":
                    processed_data = processed_data.dropna(subset=selected_cols)
                elif handling_method == "前向填充":
                    processed_data[selected_cols] = processed_data[selected_cols].fillna(method='ffill')
                elif handling_method == "后向填充":
                    processed_data[selected_cols] = processed_data[selected_cols].fillna(method='bfill')
                elif handling_method == "均值填充":
                    for col in selected_cols:
                        if pd.api.types.is_numeric_dtype(processed_data[col]):
                            processed_data[col] = processed_data[col].fillna(processed_data[col].mean())
                elif handling_method == "中位数填充":
                    for col in selected_cols:
                        if pd.api.types.is_numeric_dtype(processed_data[col]):
                            processed_data[col] = processed_data[col].fillna(processed_data[col].median())
                elif handling_method == "众数填充":
                    for col in selected_cols:
                        mode_val = processed_data[col].mode()
                        if not mode_val.empty:
                            processed_data[col] = processed_data[col].fillna(mode_val.iloc[0])
                
                # 记录操作
                self.add_to_operation_history(
                    "缺失值处理",
                    f"对列 {selected_cols} 应用 {handling_method}",
                    {"method": handling_method, "columns": selected_cols}
                )
                
                st_obj.success(f"缺失值处理完成！数据形状: {processed_data.shape}")
                return processed_data
                
            except Exception as e:
                st_obj.error(f"缺失值处理失败: {str(e)}")
                return data
        
        return data
    
    def render_outlier_detector(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染异常值检测界面"""
        
        st_obj.markdown("#### 异常值检测与处理")
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        
        if not numeric_cols:
            st_obj.info("没有数值列可进行异常值检测")
            return data
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            detection_method = st_obj.selectbox(
                "异常值检测方法:",
                options=["IQR方法", "Z-Score方法", "修正Z-Score方法"],
                key=f"{self.component_name}_outlier_method"
            )
            
            threshold = st_obj.number_input(
                "阈值:",
                min_value=1.0,
                max_value=5.0,
                value=3.0 if "Z-Score" in detection_method else 1.5,
                step=0.1,
                key=f"{self.component_name}_outlier_threshold"
            )
        
        with col2:
            selected_cols = st_obj.multiselect(
                "选择要检测的列:",
                options=numeric_cols,
                default=numeric_cols[:3] if len(numeric_cols) > 3 else numeric_cols,
                key=f"{self.component_name}_outlier_cols"
            )
            
            handling_action = st_obj.selectbox(
                "异常值处理方式:",
                options=["仅标记", "删除异常值", "用中位数替换", "用均值替换"],
                key=f"{self.component_name}_outlier_action"
            )
        
        if st_obj.button("检测异常值", key=f"{self.component_name}_detect_outliers"):
            if not selected_cols:
                st_obj.error("请选择要检测的列")
                return data
            
            processed_data = data.copy()
            outlier_summary = []
            
            try:
                for col in selected_cols:
                    col_data = processed_data[col].dropna()
                    
                    if detection_method == "IQR方法":
                        Q1 = col_data.quantile(0.25)
                        Q3 = col_data.quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - threshold * IQR
                        upper_bound = Q3 + threshold * IQR
                        outliers = (col_data < lower_bound) | (col_data > upper_bound)
                    
                    elif detection_method == "Z-Score方法":
                        z_scores = np.abs((col_data - col_data.mean()) / col_data.std())
                        outliers = z_scores > threshold
                    
                    elif detection_method == "修正Z-Score方法":
                        median = col_data.median()
                        mad = np.median(np.abs(col_data - median))
                        modified_z_scores = 0.6745 * (col_data - median) / mad
                        outliers = np.abs(modified_z_scores) > threshold
                    
                    outlier_count = outliers.sum()
                    outlier_summary.append({
                        '列名': col,
                        '异常值数量': outlier_count,
                        '异常值比例(%)': round(outlier_count / len(col_data) * 100, 2)
                    })
                    
                    # 处理异常值
                    if handling_action == "删除异常值":
                        processed_data = processed_data[~processed_data[col].isin(col_data[outliers])]
                    elif handling_action == "用中位数替换":
                        processed_data.loc[processed_data[col].isin(col_data[outliers]), col] = col_data.median()
                    elif handling_action == "用均值替换":
                        processed_data.loc[processed_data[col].isin(col_data[outliers]), col] = col_data.mean()
                
                # 显示异常值统计
                if outlier_summary:
                    summary_df = pd.DataFrame(outlier_summary)
                    st_obj.dataframe(summary_df, use_container_width=True)
                    
                    total_outliers = summary_df['异常值数量'].sum()
                    if total_outliers > 0:
                        st_obj.warning(f"共检测到 {total_outliers} 个异常值")
                        
                        # 记录操作
                        self.add_to_operation_history(
                            "异常值检测",
                            f"使用 {detection_method} 检测到 {total_outliers} 个异常值，处理方式: {handling_action}",
                            {"method": detection_method, "threshold": threshold, "action": handling_action}
                        )
                        
                        if handling_action != "仅标记":
                            st_obj.success(f"异常值处理完成！数据形状: {processed_data.shape}")
                            return processed_data
                    else:
                        st_obj.success("✅ 未检测到异常值")
                
            except Exception as e:
                st_obj.error(f"异常值检测失败: {str(e)}")
                return data
        
        return data
    
    def render_data_type_converter(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染数据类型转换界面"""
        
        st_obj.markdown("#### 数据类型转换")
        
        # 显示当前数据类型
        type_info = pd.DataFrame({
            '列名': data.columns,
            '当前类型': [str(dtype) for dtype in data.dtypes],
            '非空值数量': [data[col].notna().sum() for col in data.columns]
        })
        
        st_obj.dataframe(type_info, use_container_width=True)
        
        # 类型转换选择
        col1, col2 = st_obj.columns(2)
        
        with col1:
            selected_col = st_obj.selectbox(
                "选择要转换的列:",
                options=data.columns.tolist(),
                key=f"{self.component_name}_convert_col"
            )
        
        with col2:
            target_type = st_obj.selectbox(
                "目标数据类型:",
                options=["int64", "float64", "object", "datetime64", "bool"],
                key=f"{self.component_name}_target_type"
            )
        
        if st_obj.button("转换数据类型", key=f"{self.component_name}_convert_type"):
            processed_data = data.copy()
            
            try:
                if target_type == "datetime64":
                    processed_data[selected_col] = pd.to_datetime(processed_data[selected_col], errors='coerce')
                elif target_type == "bool":
                    processed_data[selected_col] = processed_data[selected_col].astype(bool)
                else:
                    processed_data[selected_col] = processed_data[selected_col].astype(target_type)
                
                # 记录操作
                self.add_to_operation_history(
                    "数据类型转换",
                    f"将列 {selected_col} 转换为 {target_type}",
                    {"column": selected_col, "target_type": target_type}
                )
                
                st_obj.success(f"数据类型转换完成！列 '{selected_col}' 已转换为 {target_type}")
                return processed_data
                
            except Exception as e:
                st_obj.error(f"数据类型转换失败: {str(e)}")
                return data
        
        return data
    
    def render_processing_interface(self, st_obj, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """渲染数据清洗处理界面"""
        
        # 选择清洗操作
        cleaning_operation = st_obj.selectbox(
            "选择清洗操作:",
            options=["缺失数据处理", "异常值检测", "数据类型转换"],
            key=f"{self.component_name}_operation_selector"
        )
        
        processed_data = data
        
        if cleaning_operation == "缺失数据处理":
            processed_data = self.render_missing_data_handler(st_obj, data)
        elif cleaning_operation == "异常值检测":
            processed_data = self.render_outlier_detector(st_obj, data)
        elif cleaning_operation == "数据类型转换":
            processed_data = self.render_data_type_converter(st_obj, data)
        
        # 如果数据被处理，保存到状态
        if processed_data is not data:
            self.set_state('processed_data', processed_data)
            self.set_state('last_operation', cleaning_operation)
        
        return processed_data

    def _apply_missing_data_handling(self, data: pd.DataFrame, method: str, selected_cols: List[str],
                                   custom_value: str = None, min_valid_ratio: float = None,
                                   max_missing_ratio: float = None) -> Optional[pd.DataFrame]:
        """应用缺失数据处理方法"""

        try:
            if method == "删除含缺失值的行":
                if min_valid_ratio is not None:
                    # 计算每行的有效数据比例
                    valid_ratio = data[selected_cols].notna().sum(axis=1) / len(selected_cols)
                    data = data[valid_ratio >= min_valid_ratio]
                else:
                    data = data.dropna(subset=selected_cols)

            elif method == "删除含缺失值的列":
                if max_missing_ratio is not None:
                    # 删除缺失比例过高的列
                    for col in selected_cols:
                        missing_ratio = data[col].isnull().sum() / len(data)
                        if missing_ratio > max_missing_ratio:
                            data = data.drop(columns=[col])
                else:
                    # 删除所有选中的含缺失值的列
                    cols_to_drop = [col for col in selected_cols if data[col].isnull().any()]
                    data = data.drop(columns=cols_to_drop)

            elif method == "前向填充":
                data[selected_cols] = data[selected_cols].fillna(method='ffill')

            elif method == "后向填充":
                data[selected_cols] = data[selected_cols].fillna(method='bfill')

            elif method == "均值填充":
                for col in selected_cols:
                    if data[col].dtype in ['int64', 'float64']:
                        data[col] = data[col].fillna(data[col].mean())
                    else:
                        # 对于非数值列，使用众数
                        mode_val = data[col].mode()
                        if not mode_val.empty:
                            data[col] = data[col].fillna(mode_val.iloc[0])

            elif method == "中位数填充":
                for col in selected_cols:
                    if data[col].dtype in ['int64', 'float64']:
                        data[col] = data[col].fillna(data[col].median())
                    else:
                        # 对于非数值列，使用众数
                        mode_val = data[col].mode()
                        if not mode_val.empty:
                            data[col] = data[col].fillna(mode_val.iloc[0])

            elif method == "众数填充":
                for col in selected_cols:
                    mode_val = data[col].mode()
                    if not mode_val.empty:
                        data[col] = data[col].fillna(mode_val.iloc[0])

            elif method == "线性插值":
                for col in selected_cols:
                    if data[col].dtype in ['int64', 'float64']:
                        data[col] = data[col].interpolate(method='linear')
                    else:
                        # 对于非数值列，使用前向填充
                        data[col] = data[col].fillna(method='ffill')

            elif method == "自定义值填充":
                if custom_value is not None:
                    # 尝试转换自定义值的类型
                    for col in selected_cols:
                        try:
                            if data[col].dtype == 'int64':
                                fill_value = int(custom_value)
                            elif data[col].dtype == 'float64':
                                fill_value = float(custom_value)
                            else:
                                fill_value = custom_value
                            data[col] = data[col].fillna(fill_value)
                        except ValueError:
                            # 如果转换失败，使用字符串值
                            data[col] = data[col].fillna(custom_value)

            return data

        except Exception as e:
            logger.error(f"缺失数据处理失败: {e}")
            return None

    def render_duplicate_analysis(self, st_obj, data: pd.DataFrame) -> Dict[str, Any]:
        """渲染重复数据分析界面"""
        st_obj.markdown("#### 重复数据分析")

        # 检查重复行
        duplicate_count = data.duplicated().sum()

        if duplicate_count == 0:
            st_obj.success("✅ 数据中没有重复行")
            return {'has_duplicates': False, 'duplicate_count': 0}

        st_obj.warning(f"发现 {duplicate_count} 行重复数据")

        # 显示重复数据处理选项
        remove_duplicates = st_obj.checkbox(
            f"删除重复行 (将删除 {duplicate_count} 行)",
            key=f"{self.component_id}_remove_duplicates"
        )

        return {
            'has_duplicates': True,
            'duplicate_count': duplicate_count,
            'remove_duplicates': remove_duplicates
        }


__all__ = ['DataCleanComponent']
