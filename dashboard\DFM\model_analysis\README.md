# DFM 模型分析模块说明

## 模块概述

`model_analysis` 模块是DFM（Dynamic Factor Model）系统的核心分析组件，专门用于分析已训练完成的DFM模型结果。该模块提供了完整的模型性能评估、可视化分析和数据导出功能。

## 模块结构

```
dashboard/DFM/model_analysis/
├── __init__.py                     # 包初始化文件
├── dfm_backend.py                  # 后端数据处理逻辑
├── dfm_ui.py                       # 主UI模块，包含完整的前端功能
└── README.md                       # 本说明文档
```

## 主要功能

### 1. 文件上传与验证
- **模型文件上传**: 支持 .joblib 格式的训练好的DFM模型
- **元数据文件上传**: 支持 .pkl 格式的训练元数据
- **文件状态检查**: 实时显示上传状态和文件有效性

### 2. 模型概览
- **基本配置信息**: 目标变量、因子数量、变量数量、选择方法
- **训练时期信息**: 训练期和验证期的起止日期
- **优化目标显示**: 模型调优的目标函数

### 3. 性能指标分析
- **样本内指标**: 训练期的胜率、RMSE、MAE
- **样本外指标**: 验证期的胜率、RMSE、MAE
- **修正指标计算**: 基于月度对齐的准确性指标

### 4. Nowcast对比分析
- **时间序列可视化**: Nowcast预测值与实际值的对比图表
- **数据对齐处理**: 根据verify_alignment.py规则进行数据对齐
- **季度刻度显示**: 自动生成季度标记的X轴
- **验证期标记**: 在图表中高亮显示验证期区间

### 5. 因子载荷分析
- **热力图可视化**: 因子载荷矩阵的聚类热力图
- **变量聚类**: 基于载荷相似性的变量聚类排序
- **交互式图表**: 支持鼠标悬停查看详细数值

### 6. 详细分析结果
- **PCA结果**: 主成分分析结果展示
- **R²分析**: 行业整体R²和因子对行业的R²
- **数据下载**: 所有分析结果的CSV格式下载

### 7. 训练结果集成
- **训练产出展示**: 显示从训练模块生成的图表和文件
- **文件下载**: 提供所有训练结果文件的下载链接

## 核心模块文件

### 1. `dfm_backend.py`
**功能**: 后端数据处理和指标计算
- `load_dfm_results_from_uploads()`: 加载和处理上传的模型文件
- `_calculate_revised_monthly_metrics()`: 计算修正后的月度性能指标
- `process_dfm_data()`: 处理相关数据文件

### 2. `dfm_ui.py`
**功能**: 主UI模块，包含完整的前端功能
- `render_dfm_tab()`: 主要渲染函数，包含完整的分析界面
- `render_file_upload_section()`: 文件上传区域
- `create_aligned_nowcast_target_table()`: 创建对齐的对比表格
- `plot_factor_evolution()`: 绘制因子时间演变图
- `plot_loadings_heatmap()`: 绘制因子载荷热力图
- `load_dfm_data()`: 从session_state加载数据（缓存）

## 从dashboard.py的整合

### 代码结构（简化后）
```python
# dashboard.py中的调用
from DFM.model_analysis.dfm_ui import render_dfm_tab
# ...
with tab_results:
    render_dfm_tab(st)
```

### 简化的优势
1. **避免重复**: 移除了重复的UI代码，只保留一个完整的模块
2. **维护简单**: 只需要维护一个UI文件，降低复杂度
3. **功能完整**: 在单一文件中包含所有必需功能，包括文件上传
4. **清晰架构**: 职责明确，一个文件负责一个完整功能

## Session State 依赖

模块依赖以下session state变量：
- `dfm_model_file_indep`: 上传的模型文件对象
- `dfm_metadata_file_indep`: 上传的元数据文件对象
- `dfm_model_results_paths`: 训练模块生成的结果文件路径（可选）

## 数据对齐规则

严格遵循 `verify_alignment.py` 的对齐规则：
- **目标变量**: 对齐到发布日期的最近周五
- **月度变量**: 对齐到发布月的最后一个周五
- **预测对比**: 确保数据在时间维度上的准确对齐

## 技术特性

- **缓存优化**: 使用 `@st.cache_data` 优化数据加载性能
- **错误处理**: 全面的异常处理和用户友好的错误信息
- **响应式设计**: 支持不同屏幕尺寸的布局适配
- **交互式图表**: 基于Plotly的高质量交互图表
- **数据导出**: 支持UTF-8编码的CSV文件导出

## 使用流程

1. **上传文件**: 选择并上传模型文件(.joblib)和元数据文件(.pkl)
2. **数据加载**: 系统自动加载并处理上传的文件
3. **结果展示**: 查看模型概览、性能指标和对比图表
4. **深入分析**: 展开详细分析部分查看PCA、R²等结果
5. **数据导出**: 下载需要的分析数据和图表

## 更新历史

- **2025-06-01**: 简化模块结构，移除重复的UI文件
- **2025-06-01**: 在dfm_ui.py中添加完整的文件上传功能
- **2025-06-01**: 优化代码架构，避免功能重复 