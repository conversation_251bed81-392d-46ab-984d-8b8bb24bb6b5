"""
数据清理模块

负责数据清理相关的功能，包括：
- 重复列处理
- 连续NaN值处理
- 零值处理
- 数据验证
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Set, Tuple, Optional
from collections import Counter

from .config_constants import debug_print

class DataCleaner:
    """数据清理器类"""
    
    def __init__(self):
        self.removed_variables_log = []
    
    def remove_duplicate_columns(self, df: pd.DataFrame, log_prefix: str = "") -> pd.DataFrame:
        """
        移除重复的列
        
        Args:
            df: 输入DataFrame
            log_prefix: 日志前缀
            
        Returns:
            pd.DataFrame: 移除重复列后的DataFrame
        """
        if df.empty:
            return df
            
        original_col_count = len(df.columns)
        duplicate_mask = df.columns.duplicated(keep='first')
        
        if duplicate_mask.any():
            removed_count = duplicate_mask.sum()
            removed_cols = df.columns[duplicate_mask].tolist()
            
            # 高效去除重复列
            df_cleaned = df.iloc[:, ~duplicate_mask]
            
            # 记录移除的列
            for col in removed_cols:
                self.removed_variables_log.append({
                    'Variable': col,
                    'Reason': f'{log_prefix}duplicate_column'
                })
            
            print(f"    {log_prefix}移除重复列: {original_col_count} → {len(df_cleaned.columns)} (减少了 {removed_count} 列)")
            return df_cleaned
        else:
            print(f"    {log_prefix}未发现重复列")
            return df
    
    def handle_consecutive_nans(
        self, 
        df: pd.DataFrame, 
        threshold: int,
        log_prefix: str = ""
    ) -> pd.DataFrame:
        """
        处理连续NaN值超过阈值的列
        
        Args:
            df: 输入DataFrame
            threshold: 连续NaN的阈值
            log_prefix: 日志前缀
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        if df.empty or threshold <= 0:
            return df
            
        print(f"  {log_prefix}开始检查连续缺失值 (阈值 >= {threshold})...")
        cols_to_remove = []
        
        for col in df.columns:
            series = df[col]
            first_valid_idx = series.first_valid_index()
            
            if first_valid_idx is None:
                continue  # 跳过全为NaN的列
                
            series_after_first_valid = series.loc[first_valid_idx:]
            is_na = series_after_first_valid.isna()
            na_blocks = is_na.ne(is_na.shift()).cumsum()[is_na]
            max_consecutive_nan = 0
            
            if not na_blocks.empty:
                try:
                    block_counts = na_blocks.value_counts()
                    if not block_counts.empty:
                        max_consecutive_nan = block_counts.max()
                except Exception as e_nan_count:
                     print(f"    {log_prefix}警告: 计算 '{col}' 的 NaN 块时出错: {e_nan_count}. 跳过此列检查.")
                     continue

            if max_consecutive_nan >= threshold:
                cols_to_remove.append(col)
                print(f"    {log_prefix}标记移除变量: '{col}' (最大连续 NaN: {max_consecutive_nan} >= {threshold})")
                self.removed_variables_log.append({
                    'Variable': col, 
                    'Reason': f'{log_prefix}consecutive_nan'
                })

        if cols_to_remove:
            print(f"    {log_prefix}正在移除 {len(cols_to_remove)} 个连续缺失值超标的变量...")
            df_cleaned = df.drop(columns=cols_to_remove)
            print(f"      {log_prefix}移除后 Shape: {df_cleaned.shape}")
            return df_cleaned
        else:
            print(f"    {log_prefix}所有变量的连续缺失值均低于阈值。")
            return df
    
    def clean_zero_values(self, df: pd.DataFrame, log_prefix: str = "") -> pd.DataFrame:
        """
        将0值替换为NaN
        
        Args:
            df: 输入DataFrame
            log_prefix: 日志前缀
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        if df.empty:
            return df
            
        df_cleaned = df.replace(0, np.nan)
        print(f"      {log_prefix}将 0 值替换为 NaN。")
        return df_cleaned
    
    def remove_unnamed_columns(self, df: pd.DataFrame, log_prefix: str = "") -> pd.DataFrame:
        """
        移除Unnamed列
        
        Args:
            df: 输入DataFrame
            log_prefix: 日志前缀
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        if df.empty:
            return df
            
        unnamed_cols = [col for col in df.columns if isinstance(col, str) and col.startswith('Unnamed:')]
        
        if unnamed_cols:
            print(f"      {log_prefix}发现并移除 Unnamed 列: {unnamed_cols}")
            df_cleaned = df.drop(columns=unnamed_cols)
            
            # 记录移除的列
            for col in unnamed_cols:
                self.removed_variables_log.append({
                    'Variable': col,
                    'Reason': f'{log_prefix}unnamed_column'
                })
            
            return df_cleaned
        
        return df
    
    def remove_all_nan_columns(self, df: pd.DataFrame, log_prefix: str = "") -> pd.DataFrame:
        """
        移除全为NaN的列
        
        Args:
            df: 输入DataFrame
            log_prefix: 日志前缀
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        if df.empty:
            return df
            
        cols_before = set(df.columns)
        df_cleaned = df.dropna(axis=1, how='all')
        removed_cols = cols_before - set(df_cleaned.columns)
        
        if removed_cols:
            print(f"  {log_prefix}移除了 {len(removed_cols)} 个全 NaN 列: {list(removed_cols)[:10]}{'...' if len(removed_cols)>10 else ''}")
            
            # 记录移除的列
            for col in removed_cols:
                self.removed_variables_log.append({
                    'Variable': col,
                    'Reason': f'{log_prefix}all_nan'
                })
        
        return df_cleaned
    
    def remove_all_nan_rows(self, df: pd.DataFrame, log_prefix: str = "") -> pd.DataFrame:
        """
        移除全为NaN的行
        
        Args:
            df: 输入DataFrame
            log_prefix: 日志前缀
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        if df.empty:
            return df
            
        original_rows = df.shape[0]
        df_cleaned = df.dropna(how='all')
        
        if df_cleaned.shape[0] < original_rows:
            print(f"    {log_prefix}移除全NaN行: {original_rows} → {df_cleaned.shape[0]} 行")
        
        return df_cleaned
    
    def get_removed_variables_log(self) -> List[Dict]:
        """获取移除变量的日志"""
        return self.removed_variables_log.copy()
    
    def clear_log(self):
        """清空日志"""
        self.removed_variables_log.clear()

# 便利函数
def clean_dataframe(
    df: pd.DataFrame,
    remove_duplicates: bool = True,
    remove_zeros: bool = True,
    remove_unnamed: bool = True,
    remove_all_nan_cols: bool = True,
    remove_all_nan_rows: bool = True,
    consecutive_nan_threshold: Optional[int] = None,
    log_prefix: str = ""
) -> Tuple[pd.DataFrame, List[Dict]]:
    """
    一站式数据清理函数
    
    Args:
        df: 输入DataFrame
        remove_duplicates: 是否移除重复列
        remove_zeros: 是否将0值替换为NaN
        remove_unnamed: 是否移除Unnamed列
        remove_all_nan_cols: 是否移除全NaN列
        remove_all_nan_rows: 是否移除全NaN行
        consecutive_nan_threshold: 连续NaN阈值，None表示不检查
        log_prefix: 日志前缀
        
    Returns:
        Tuple[pd.DataFrame, List[Dict]]: (清理后的DataFrame, 移除变量日志)
    """
    cleaner = DataCleaner()
    result_df = df.copy()
    
    if remove_zeros:
        result_df = cleaner.clean_zero_values(result_df, log_prefix)
    
    if remove_unnamed:
        result_df = cleaner.remove_unnamed_columns(result_df, log_prefix)
    
    if remove_duplicates:
        result_df = cleaner.remove_duplicate_columns(result_df, log_prefix)
    
    if consecutive_nan_threshold is not None:
        result_df = cleaner.handle_consecutive_nans(result_df, consecutive_nan_threshold, log_prefix)
    
    if remove_all_nan_cols:
        result_df = cleaner.remove_all_nan_columns(result_df, log_prefix)
    
    if remove_all_nan_rows:
        result_df = cleaner.remove_all_nan_rows(result_df, log_prefix)
    
    return result_df, cleaner.get_removed_variables_log()

# 导出的类和函数
__all__ = [
    'DataCleaner',
    'clean_dataframe'
]
