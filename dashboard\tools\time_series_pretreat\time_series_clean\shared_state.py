"""
Tools模块统一状态管理接口
为所有tools子模块提供统一的状态管理接口，作为单一真实来源
重构后支持：时间序列预处理、清洗、计算、属性分析、数据比较等模块
"""

import streamlit as st
import logging
from typing import Any, Optional, Dict, List
from datetime import datetime

# 导入统一状态管理器
try:
    # 首先尝试相对导入（推荐方式）
    import sys
    import os
    # 添加dashboard目录到sys.path
    dashboard_path = os.path.join(os.path.dirname(__file__), '../../..')
    if dashboard_path not in sys.path:
        sys.path.insert(0, dashboard_path)

    # 尝试导入统一状态管理器
    from state_management import get_unified_manager

    # 尝试导入工具重构器
    try:
        from state_management.refactor import get_global_tools_refactor
    except ImportError:
        try:
            # 如果refactor模块导入失败，尝试直接导入
            from state_management.refactor.global_refactor_manager import get_global_tools_refactor
        except ImportError:
            # 如果还是失败，提供空实现
            def get_global_tools_refactor():
                return None

    print("[Time Series Clean] ✅ 统一状态管理器相对导入成功")
except ImportError as e:
    print(f"[Time Series Clean] ❌ 统一状态管理器相对导入失败: {e}")
    try:
        # 备用：尝试绝对导入（如果从项目根目录运行）
        from dashboard.state_management import get_unified_manager
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor
        except ImportError:
            try:
                from dashboard.state_management.refactor.global_refactor_manager import get_global_tools_refactor
            except ImportError:
                # 如果还是失败，提供空实现
                def get_global_tools_refactor():
                    return None
        print("[Time Series Clean] ✅ 统一状态管理器绝对导入成功")
    except ImportError as e2:
        print(f"[Time Series Clean] ❌ 统一状态管理器绝对导入也失败: {e2}")
        # 提供空实现
        def get_global_tools_refactor():
            return None
        def get_unified_manager():
            return None
        print("[Time Series Clean] ⚠️ 使用空实现")

# 配置日志
logger = logging.getLogger(__name__)

# 全局状态管理器实例缓存
_global_tools_refactor = None
_initialization_time = None

def get_shared_tools_refactor():
    """获取共享的工具模块重构器实例（使用全局单例）"""
    global _global_tools_refactor, _initialization_time

    try:
        # 如果已经初始化且时间不超过1小时，直接返回缓存实例
        if (_global_tools_refactor is not None and
            _initialization_time is not None and
            (datetime.now() - _initialization_time).total_seconds() < 3600):
            return _global_tools_refactor

        # 重新初始化
        tools_refactor = get_global_tools_refactor()
        if tools_refactor is None:
            # 尝试直接从统一管理器创建
            unified_manager = get_unified_manager()
            if unified_manager is not None:
                from dashboard.state_management.refactor.tools_module_refactor import ToolsModuleRefactor
                tools_refactor = ToolsModuleRefactor(unified_manager)
            else:
                raise RuntimeError("统一状态管理器和全局工具重构器都不可用")

        # 缓存实例
        _global_tools_refactor = tools_refactor
        _initialization_time = datetime.now()

        logger.info("Tools模块状态管理器初始化成功")
        return tools_refactor

    except Exception as e:
        logger.error(f"初始化Tools状态管理器失败: {e}")
        raise RuntimeError(f"Failed to initialize shared tools refactor: {e}")

def _handle_state_operation_error(operation: str, module: str, key: str, error: Exception) -> None:
    """处理状态操作错误的统一函数"""
    error_msg = f"[Tools State] {operation}失败 - 模块: {module}, 键: {key}, 错误: {error}"
    logger.error(error_msg)
    print(error_msg)  # 同时输出到控制台以便调试

# === 时间序列预处理模块状态管理 ===
def get_pretreat_state(key: str, default: Any = None) -> Any:
    """获取时间序列预处理状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        return tools_refactor.get_tools_state('time_series_pretreat', key, default)
    except Exception as e:
        _handle_state_operation_error("获取状态", "time_series_pretreat", key, e)
        return default

def set_pretreat_state(key: str, value: Any) -> bool:
    """设置时间序列预处理状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        success = tools_refactor.set_tools_state('time_series_pretreat', key, value)
        if not success:
            logger.warning(f"[Tools State] 预处理模块状态设置失败: {key}")
        return success
    except Exception as e:
        _handle_state_operation_error("设置状态", "time_series_pretreat", key, e)
        return False

# === 时间序列清洗模块状态管理 ===
def get_clean_state(key: str, default: Any = None) -> Any:
    """获取时间序列清洗状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        return tools_refactor.get_tools_state('time_series_clean', key, default)
    except Exception as e:
        _handle_state_operation_error("获取状态", "time_series_clean", key, e)
        return default

def set_clean_state(key: str, value: Any) -> bool:
    """设置时间序列清洗状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        success = tools_refactor.set_tools_state('time_series_clean', key, value)
        if not success:
            logger.warning(f"[Tools State] 清洗模块状态设置失败: {key}")
        return success
    except Exception as e:
        _handle_state_operation_error("设置状态", "time_series_clean", key, e)
        return False

# === 时间序列计算模块状态管理 ===
def get_compute_state(key: str, default: Any = None) -> Any:
    """获取计算模块状态值"""
    try:
        tools_refactor = get_shared_tools_refactor()
        return tools_refactor.get_tools_state('time_series_compute', key, default)
    except Exception as e:
        _handle_state_operation_error("获取状态", "time_series_compute", key, e)
        return default

def set_compute_state(key: str, value: Any) -> bool:
    """设置计算模块状态值"""
    try:
        tools_refactor = get_shared_tools_refactor()
        success = tools_refactor.set_tools_state('time_series_compute', key, value)
        if not success:
            logger.warning(f"[Tools State] 计算模块状态设置失败: {key}")
        return success
    except Exception as e:
        _handle_state_operation_error("设置状态", "time_series_compute", key, e)
        return False

# === 时间序列属性分析模块状态管理 ===
def get_property_state(key: str, default: Any = None) -> Any:
    """获取时间序列属性分析状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        return tools_refactor.get_tools_state('time_series_property', key, default)
    except Exception as e:
        _handle_state_operation_error("获取状态", "time_series_property", key, e)
        return default

def set_property_state(key: str, value: Any) -> bool:
    """设置时间序列属性分析状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        success = tools_refactor.set_tools_state('time_series_property', key, value)
        if not success:
            logger.warning(f"[Tools State] 属性分析模块状态设置失败: {key}")
        return success
    except Exception as e:
        _handle_state_operation_error("设置状态", "time_series_property", key, e)
        return False

# === 数据比较模块状态管理 ===
def get_comparison_state(key: str, default: Any = None) -> Any:
    """获取数据比较模块状态值"""
    try:
        tools_refactor = get_shared_tools_refactor()
        return tools_refactor.get_tools_state('data_comparison', key, default)
    except Exception as e:
        _handle_state_operation_error("获取状态", "data_comparison", key, e)
        return default

def set_comparison_state(key: str, value: Any) -> bool:
    """设置数据比较模块状态值"""
    try:
        tools_refactor = get_shared_tools_refactor()
        success = tools_refactor.set_tools_state('data_comparison', key, value)
        if not success:
            logger.warning(f"[Tools State] 数据比较模块状态设置失败: {key}")
        return success
    except Exception as e:
        _handle_state_operation_error("设置状态", "data_comparison", key, e)
        return False

# === 兼容性函数（向后兼容旧代码） ===
# 注意：get_tools_state和set_tools_state已移至dashboard.ui.utils.state_helpers模块
# 这里保留映射到预处理模块的便捷函数
def get_tools_state_legacy(key: str, default: Any = None) -> Any:
    """获取工具状态的便捷函数（兼容性函数，映射到预处理模块）"""
    return get_pretreat_state(key, default)

def set_tools_state_legacy(key: str, value: Any) -> bool:
    """设置工具状态的便捷函数（兼容性函数，映射到预处理模块）"""
    return set_pretreat_state(key, value)

# === 通用工具函数 ===
def clear_module_state(module_name: str) -> bool:
    """清除指定模块的所有状态"""
    try:
        tools_refactor = get_shared_tools_refactor()
        # 获取所有键，如果失败则返回空列表
        try:
            all_keys = tools_refactor.get_all_keys() if hasattr(tools_refactor, 'get_all_keys') else []
        except Exception as key_error:
            logger.warning(f"获取所有键失败: {key_error}，使用空列表")
            all_keys = []

        # 过滤出指定模块的键
        module_keys = [key for key in all_keys if key.startswith(f'tools.{module_name}.')]

        # 清除键
        cleared_count = 0
        for key in module_keys:
            try:
                if tools_refactor.delete_state(key):
                    cleared_count += 1
            except Exception as delete_error:
                logger.warning(f"删除键 {key} 失败: {delete_error}")

        logger.info(f"清除模块 {module_name} 的 {cleared_count} 个状态键")
        return True

    except Exception as e:
        logger.error(f"清除模块 {module_name} 状态失败: {e}")
        return False

def get_module_state_summary(module_name: str) -> Dict[str, Any]:
    """获取指定模块的状态摘要"""
    try:
        tools_refactor = get_shared_tools_refactor()
        all_keys = tools_refactor.get_all_keys() if hasattr(tools_refactor, 'get_all_keys') else []

        # 过滤出指定模块的键
        module_keys = [key for key in all_keys if key.startswith(f'tools.{module_name}.')]

        summary = {
            'module': module_name,
            'total_keys': len(module_keys),
            'keys': module_keys,
            'timestamp': datetime.now().isoformat()
        }

        # 统计不同类型的键
        key_types = {}
        for key in module_keys:
            parts = key.split('.')
            if len(parts) >= 3:
                key_type = parts[2]  # tools.{module}.{type}.{key}
                key_types[key_type] = key_types.get(key_type, 0) + 1

        summary['key_types'] = key_types
        return summary

    except Exception as e:
        logger.error(f"获取模块 {module_name} 状态摘要失败: {e}")
        return {'error': str(e)}

def validate_state_consistency() -> Dict[str, Any]:
    """验证状态一致性"""
    try:
        tools_refactor = get_shared_tools_refactor()
        # 安全地获取所有键
        try:
            all_keys = tools_refactor.get_all_keys() if hasattr(tools_refactor, 'get_all_keys') else []
        except Exception as key_error:
            logger.warning(f"获取所有键失败: {key_error}，使用空列表")
            all_keys = []

        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'total_keys': len(all_keys),
            'issues': [],
            'modules': {}
        }

        # 按模块分组键
        for key in all_keys:
            if key.startswith('tools.'):
                parts = key.split('.')
                if len(parts) >= 2:
                    module = parts[1]
                    if module not in validation_report['modules']:
                        validation_report['modules'][module] = []
                    validation_report['modules'][module].append(key)
                else:
                    validation_report['issues'].append(f"键格式不正确: {key}")

        # 检查命名约定
        for key in all_keys:
            if key.startswith('tools.'):
                parts = key.split('.')
                if len(parts) < 4:
                    validation_report['issues'].append(f"键层级不足: {key}")

        return validation_report

    except Exception as e:
        logger.error(f"状态一致性验证失败: {e}")
        return {'error': str(e)}

# === 调试和监控函数 ===
def get_state_manager_health() -> Dict[str, Any]:
    """获取状态管理器健康状态"""
    try:
        tools_refactor = get_shared_tools_refactor()

        health_info = {
            'timestamp': datetime.now().isoformat(),
            'manager_available': tools_refactor is not None,
            'initialization_time': _initialization_time.isoformat() if _initialization_time else None,
            'uptime_seconds': (datetime.now() - _initialization_time).total_seconds() if _initialization_time else 0
        }

        if tools_refactor:
            # 尝试基本操作
            test_key = 'tools.health.test'
            test_value = 'health_check'

            # 测试设置和获取
            set_success = tools_refactor.set_state(test_key, test_value)
            get_value = tools_refactor.get_state(test_key)
            delete_success = tools_refactor.delete_state(test_key)

            health_info.update({
                'set_operation': set_success,
                'get_operation': get_value == test_value,
                'delete_operation': delete_success,
                'overall_health': set_success and (get_value == test_value) and delete_success
            })

        return health_info

    except Exception as e:
        logger.error(f"获取状态管理器健康状态失败: {e}")
        return {
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'overall_health': False
        }
