import streamlit as st
import pandas as pd
import numpy as np
import io
from typing import Dict, Any, Optional, List

# 导入新的统一UI组件
from dashboard.ui.components.data_input import (
    UnifiedDataUploadComponent,
    DataPreviewComponent,
    DataStagingComponent,
    DataUploadSidebar
)
from dashboard.ui.components.preprocessing import (
    DataCleanComponent,
    TimeProcessingComponent
)

# --- 导入统一状态管理接口 ---
from .shared_state import get_clean_state, set_clean_state

print("[Time Series Clean] ✅ 统一状态管理器导入成功")

# 初始化新UI组件实例
def initialize_ui_components():
    """初始化新的UI组件实例"""
    print("[Time Series Clean] 使用新的统一UI组件")
    
    return {
        'file_upload': UnifiedDataUploadComponent(
            accepted_types=['csv', 'xlsx'],
            help_text="上传CSV或Excel格式的时间序列数据文件",
            component_id="clean_file_upload"
        ),
        'data_preview': DataPreviewComponent(),
        'data_clean': DataCleanComponent(),
        'time_processing': TimeProcessingComponent(),
        'data_staging': DataStagingComponent(),
        'upload_sidebar': DataUploadSidebar(
            title="数据上传",
            accepted_types=['csv', 'xlsx'],
            help_text="上传时间序列数据文件"
        )
    }

# 初始化UI组件
ui_components = initialize_ui_components()

def display_time_series_tool_tab(st):
    """Displays the Time Series Calculation Tool tab with new UI components."""

    try:
        # 初始化基本状态
        if get_clean_state('ts_tool_data_raw') is None: 
            set_clean_state('ts_tool_data_raw', None)
        if get_clean_state('ts_tool_data_processed') is None: 
            set_clean_state('ts_tool_data_processed', None)
        if get_clean_state('staged_data') is None: 
            set_clean_state('staged_data', {})

        # 显示标题
        st.markdown("## 时间序列数据清洗工具")
        
        # 文件上传
        st.markdown("### 1. 数据上传")
        uploaded_data = ui_components['file_upload'].render_input_section(
            st,
            upload_key="clean_tab_upload",
            show_preview=True
        )
        
        if uploaded_data is not None:
            set_clean_state('ts_tool_data_raw', uploaded_data)
            st.success(f"文件上传成功！数据形状: {uploaded_data.shape}")
            
            # 数据预览
            st.markdown("### 2. 数据预览")
            ui_components['data_preview'].render_input_section(
                st,
                data=uploaded_data
            )
            
            # 数据清洗
            st.markdown("### 3. 数据清洗")
            cleaned_data = ui_components['data_clean'].render_input_section(
                st,
                data=uploaded_data
            )
            
            if cleaned_data is not None:
                set_clean_state('ts_tool_data_processed', cleaned_data)
                
                # 时间处理
                st.markdown("### 4. 时间处理")
                time_processed_data = ui_components['time_processing'].render_input_section(
                    st,
                    data=cleaned_data
                )
                
                if time_processed_data is not None:
                    # 数据暂存
                    st.markdown("### 5. 数据暂存")
                    ui_components['data_staging'].render_input_section(
                        st,
                        mode='add',
                        data=time_processed_data,
                        default_name=f"clean_data_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}"
                    )
        else:
            st.info("请上传文件以开始数据清洗流程。")

    except Exception as e:
        st.error(f"加载数据清洗模块时出错: {e}")
        import traceback
        st.error(f"详细错误信息: {traceback.format_exc()}")
