"""
主数据处理器模块

协调各个模块的工作，实现完整的数据准备流程
这是重构后的主要接口，保持与原始prepare_data函数的兼容性
"""

import pandas as pd
import numpy as np
import unicodedata
import os
import sys
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict, Counter

from .config_constants import debug_print, get_global_config, CREATE_REDUCED_TEST_SET
from .format_detection import parse_sheet_info
from .mapping_manager import load_mappings, create_industry_map_from_data
from .stationarity_processor import ensure_stationarity, apply_stationarity_transforms
from .data_loader import DataLoader
from .data_aligner import DataAligner
from .data_cleaner import DataCleaner, clean_dataframe
from .final_processor import apply_final_stationarity_check

def prepare_data(
    excel_path: str,
    target_freq: str,
    target_sheet_name: str,
    target_variable_name: str,
    consecutive_nan_threshold: Optional[int] = None,
    data_start_date: Optional[str] = None,
    data_end_date: Optional[str] = None,
    reference_sheet_name: str = '指标体系',
    reference_column_name: str = '高频指标'
) -> Tuple[Optional[pd.DataFrame], Optional[Dict], Optional[Dict], Optional[List[Dict]]]:
    """
    重构后的数据准备主函数
    
    加载数据，执行平稳性检查，对齐所有数据到目标频率，执行NaN检查和最终平稳性检查
    
    Args:
        excel_path: Excel文件路径
        target_freq: 目标频率（如'W-FRI'）
        target_sheet_name: 目标表格名称
        target_variable_name: 目标变量名称
        consecutive_nan_threshold: 连续NaN阈值
        data_start_date: 数据开始日期
        data_end_date: 数据结束日期
        reference_sheet_name: 参考表格名称
        reference_column_name: 参考列名称
        
    Returns:
        Tuple[Optional[pd.DataFrame], Optional[Dict], Optional[Dict], Optional[List[Dict]]]:
            - 最终对齐的周度数据 (DataFrame)
            - 变量到行业的映射 (Dict)
            - 合并的转换日志 (Dict)
            - 详细的移除日志 (List[Dict])
    """
    print(f"\n--- [Data Prep V3 Refactored] 开始加载和处理数据 (目标频率: {target_freq}) ---")
    
    if CREATE_REDUCED_TEST_SET:
        print("  [!] 已启用缩小版测试集生成模式。")
    
    # 验证目标频率
    if not target_freq.upper().endswith('-FRI'):
        print(f"错误: [Data Prep] 当前目标对齐逻辑仅支持周五 (W-FRI)。提供的目标频率 '{target_freq}' 无效。")
        return None, None, None, None
    
    print(f"  [Data Prep] 目标 Sheet: '{target_sheet_name}', 目标变量名(预期B列): '{target_variable_name}'")
    
    try:
        # 初始化组件
        data_loader = DataLoader()
        data_aligner = DataAligner(target_freq)
        data_cleaner = DataCleaner()
        
        # 存储各种数据和日志
        removed_variables_detailed_log = []
        all_indices_for_range = []
        
        # 加载Excel文件
        excel_file = pd.ExcelFile(excel_path)
        available_sheets = excel_file.sheet_names
        print(f"  [Data Prep] Excel 文件中可用的 Sheets: {available_sheets}")
        
        # 加载参考变量
        reference_predictor_variables = set()
        if reference_sheet_name in available_sheets:
            try:
                ref_df = pd.read_excel(excel_file, sheet_name=reference_sheet_name)
                ref_df.columns = ref_df.columns.str.strip()
                clean_reference_column_name = reference_column_name.strip()
                
                if clean_reference_column_name in ref_df.columns:
                    raw_reference_vars = (
                        ref_df[clean_reference_column_name]
                        .astype(str).str.strip().replace('nan', np.nan).dropna().unique()
                    )
                    raw_reference_vars = [v for v in raw_reference_vars if v]
                    reference_predictor_variables = set(
                        unicodedata.normalize('NFKC', var).strip().lower()
                        for var in raw_reference_vars
                    )
                    print(f"  [Data Prep] 从 '{reference_sheet_name}' 加载并规范化了 {len(reference_predictor_variables)} 个参考变量名。")
                else:
                    print(f"  [Data Prep] 警告: 在 '{reference_sheet_name}' 未找到参考列 '{clean_reference_column_name}'。")
            except Exception as e_ref:
                print(f"  [Data Prep] 警告: 读取参考 Sheet '{reference_sheet_name}' 出错: {e_ref}。")
        else:
             print(f"  [Data Prep] 警告: 未找到参考 Sheet '{reference_sheet_name}'。")
        
        # 数据存储
        data_parts = defaultdict(list)
        publication_dates_from_target = None
        raw_target_values = None
        df_target_sheet_predictors_pubdate = pd.DataFrame()
        df_other_monthly_predictors_pubdate = pd.DataFrame()
        target_sheet_industry_name = "Macro"
        actual_target_variable_name = None
        target_sheet_cols = set()
        
        # 步骤1: 按频率加载数据
        print("\n--- [Data Prep V3 Refactored] 步骤 1: 加载数据 ---")
        
        for sheet_name in available_sheets:
            print(f"    [Data Prep] 正在检查 Sheet: {sheet_name}...")
            is_target_sheet = (sheet_name == target_sheet_name)
            sheet_info = parse_sheet_info(sheet_name, target_sheet_name)
            freq_type = sheet_info['freq_type']
            industry_name = sheet_info['industry'] if sheet_info['industry'] else "Uncategorized"
            
            if is_target_sheet:
                # 处理目标表格
                pub_dates, target_vals, target_preds, target_cols = data_loader.load_target_sheet(
                    excel_file, sheet_name, target_variable_name, industry_name
                )
                
                if pub_dates is not None and target_vals is not None:
                    publication_dates_from_target = pub_dates
                    raw_target_values = target_vals
                    actual_target_variable_name = target_vals.name
                    target_sheet_industry_name = industry_name
                    target_sheet_cols = target_cols
                    
                    if not target_preds.empty:
                        df_target_sheet_predictors_pubdate = target_preds
                        
            elif freq_type in ['daily', 'weekly']:
                # 处理日度/周度数据
                df_loaded = data_loader.load_daily_weekly_sheet(excel_file, sheet_name, freq_type, industry_name)
                if df_loaded is not None:
                    data_parts[freq_type].append(df_loaded)
                    
            elif freq_type == 'monthly_predictor':
                # 处理其他月度预测变量
                df_monthly = data_loader.load_monthly_predictor_sheet(excel_file, sheet_name, industry_name)
                if df_monthly is not None:
                    if df_other_monthly_predictors_pubdate.empty:
                        df_other_monthly_predictors_pubdate = df_monthly
                    else:
                        df_other_monthly_predictors_pubdate = pd.concat([df_other_monthly_predictors_pubdate, df_monthly], axis=1, join='outer')
            else:
                if sheet_name != reference_sheet_name:
                    print(f"      Sheet '{sheet_name}' 不符合要求或非目标 Sheet，已跳过。")
                continue
        
        # 检查是否加载了必要数据
        if raw_target_values is None or raw_target_values.empty or publication_dates_from_target is None:
             print(f"错误：[Data Prep] 未能成功加载目标变量 '{target_variable_name}' 或其发布日期。")
             return None, None, None, None
        
        # 获取加载器的映射和日志
        var_industry_map = data_loader.get_var_industry_map()
        raw_columns_across_all_sheets = data_loader.get_raw_columns_set()
        removed_variables_detailed_log.extend(data_loader.get_removed_variables_log())
        
        print(f"\n--- [Data Prep V3 Refactored] 数据加载完成 ---")
        print(f"  目标变量: {actual_target_variable_name}")
        print(f"  目标Sheet预测变量: {df_target_sheet_predictors_pubdate.shape[1]} 个")
        print(f"  其他月度预测变量: {df_other_monthly_predictors_pubdate.shape[1]} 个")
        print(f"  日度数据表格: {len(data_parts['daily'])} 个")
        print(f"  周度数据表格: {len(data_parts['weekly'])} 个")
        
        # 步骤2: 数据对齐
        print("\n--- [Data Prep V3 Refactored] 步骤 2: 数据对齐 ---")

        # 2a: 目标变量对齐到最近周五
        target_series_aligned = data_aligner.align_target_to_nearest_friday(raw_target_values)
        if not target_series_aligned.empty:
            # 添加目标变量的原始索引（对齐前）到日期范围计算中，因为它包含重要的历史数据
            all_indices_for_range.append(raw_target_values.index)

        # 2b: 目标Sheet预测变量对齐到最近周五
        target_sheet_predictors_aligned = data_aligner.align_target_sheet_predictors_to_nearest_friday(df_target_sheet_predictors_pubdate)
        if not target_sheet_predictors_aligned.empty:
            all_indices_for_range.append(target_sheet_predictors_aligned.index)

        # 2c: 其他月度预测变量对齐到月末最后周五
        # 先添加月度数据的原始索引到日期范围计算中
        if not df_other_monthly_predictors_pubdate.empty:
            all_indices_for_range.append(df_other_monthly_predictors_pubdate.index)

        monthly_predictors_aligned, monthly_transform_log, monthly_removed_info = data_aligner.align_monthly_to_last_friday(
            df_other_monthly_predictors_pubdate, apply_stationarity=True
        )

        # 记录月度处理的移除变量
        for reason, cols in monthly_removed_info.items():
            for col in cols:
                removed_variables_detailed_log.append({'Variable': col, 'Reason': f'monthly_stationarity_{reason}'})

        # 2d: 日度数据转换为周度 (先添加原始索引再转换)
        if data_parts['daily']:
            # 添加原始日度索引到范围计算中
            for df in data_parts['daily']:
                if not df.empty:
                    all_indices_for_range.append(df.index)

        df_daily_weekly = data_aligner.convert_daily_to_weekly(data_parts['daily'])

        # 2e: 周度数据对齐 (先添加原始索引再对齐)
        if data_parts['weekly']:
            # 添加原始周度索引到范围计算中
            for df in data_parts['weekly']:
                if not df.empty:
                    all_indices_for_range.append(df.index)

        df_weekly_aligned = data_aligner.align_weekly_data(data_parts['weekly'])

        # 步骤3: 合并日度和周度数据并进行NaN检查
        print("\n--- [Data Prep V3 Refactored] 步骤 3: 合并日度/周度数据 ---")

        parts_to_combine_dw = []
        if not df_daily_weekly.empty: parts_to_combine_dw.append(df_daily_weekly)
        if not df_weekly_aligned.empty: parts_to_combine_dw.append(df_weekly_aligned)

        df_combined_dw_weekly = pd.DataFrame()
        if parts_to_combine_dw:
            df_combined_dw_weekly = pd.concat(parts_to_combine_dw, axis=1)
            df_combined_dw_weekly, dw_removed_log = clean_dataframe(
                df_combined_dw_weekly,
                consecutive_nan_threshold=consecutive_nan_threshold,
                log_prefix="[日度/周度合并] "
            )
            removed_variables_detailed_log.extend(dw_removed_log)
            print(f"    合并后日度/周度数据 Shape: {df_combined_dw_weekly.shape}")

        # 继续到最终合并步骤
        return _finalize_data_processing(
            target_series_aligned, target_sheet_predictors_aligned, monthly_predictors_aligned,
            df_combined_dw_weekly, actual_target_variable_name, target_sheet_cols,
            all_indices_for_range, data_start_date, data_end_date, target_freq,
            monthly_transform_log, removed_variables_detailed_log, var_industry_map,
            raw_columns_across_all_sheets, reference_predictor_variables
        )
        
    except FileNotFoundError:
        print(f"错误: [Data Prep] Excel 数据文件 {excel_path} 未找到。")
        return None, None, None, None
    except Exception as err:
        print(f"错误: [Data Prep] 数据准备过程中发生意外错误: {err}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def _finalize_data_processing(
    target_series_aligned, target_sheet_predictors_aligned, monthly_predictors_aligned,
    df_combined_dw_weekly, actual_target_variable_name, target_sheet_cols,
    all_indices_for_range, data_start_date, data_end_date, target_freq,
    monthly_transform_log, removed_variables_detailed_log, var_industry_map,
    raw_columns_across_all_sheets, reference_predictor_variables
):
    """最终数据处理和合并"""

    print("\n--- [Data Prep V3 Refactored] 步骤 4: 最终合并和处理 ---")

    # 合并所有对齐后的数据
    all_final_weekly_parts = []
    target_variable_added = False

    # 添加目标变量
    if target_series_aligned is not None and not target_series_aligned.empty:
        target_series_aligned.name = actual_target_variable_name
        all_final_weekly_parts.append(target_series_aligned)
        target_variable_added = True
        print(f"  添加目标变量 '{actual_target_variable_name}' (最近周五对齐)...")

    # 添加目标Sheet预测变量（检查重复）
    if target_sheet_predictors_aligned is not None and not target_sheet_predictors_aligned.empty:
        if actual_target_variable_name in target_sheet_predictors_aligned.columns:
            print(f"  ⚠️ 警告：目标Sheet预测变量中包含目标变量 '{actual_target_variable_name}'")
            if target_variable_added:
                target_sheet_predictors_aligned = target_sheet_predictors_aligned.drop(columns=[actual_target_variable_name])

        if not target_sheet_predictors_aligned.empty:
            all_final_weekly_parts.append(target_sheet_predictors_aligned)
            print(f"  添加目标 Sheet 预测变量 ({target_sheet_predictors_aligned.shape[1]} 个)...")

    # 添加日度/周度数据（检查重复）
    if df_combined_dw_weekly is not None and not df_combined_dw_weekly.empty:
        if actual_target_variable_name in df_combined_dw_weekly.columns:
            print(f"  ⚠️ 警告：日度/周度数据中包含目标变量 '{actual_target_variable_name}'")
            if target_variable_added:
                df_combined_dw_weekly = df_combined_dw_weekly.drop(columns=[actual_target_variable_name])

        if not df_combined_dw_weekly.empty:
            all_final_weekly_parts.append(df_combined_dw_weekly)
            print(f"  添加日度/周度预测变量 (Shape: {df_combined_dw_weekly.shape})...")

    # 添加月度预测变量（检查重复）
    if monthly_predictors_aligned is not None and not monthly_predictors_aligned.empty:
        if actual_target_variable_name in monthly_predictors_aligned.columns:
            print(f"  ⚠️ 警告：月度预测变量中包含目标变量 '{actual_target_variable_name}'")
            if target_variable_added:
                monthly_predictors_aligned = monthly_predictors_aligned.drop(columns=[actual_target_variable_name])

        if not monthly_predictors_aligned.empty:
            all_final_weekly_parts.append(monthly_predictors_aligned)
            print(f"  添加其他月度预测变量 (Shape: {monthly_predictors_aligned.shape})...")

    if not all_final_weekly_parts:
        print("错误：[Data Prep] 没有成功处理的数据部分可以合并。无法继续。")
        return None, None, None, None

    # 创建完整日期范围
    try:
        data_aligner = DataAligner(target_freq)
        full_date_range = data_aligner.create_full_date_range(all_indices_for_range, data_start_date, data_end_date)
    except Exception as e:
        print(f"错误: 创建日期范围失败: {e}")
        return None, None, None, None

    # 先将所有数据部分重新索引到完整的周五日期范围，然后再合并
    aligned_parts = []
    for i, part in enumerate(all_final_weekly_parts):
        if part is not None and not part.empty:
            print(f"  数据部分 {i+1} 重新索引前: Shape {part.shape}, 索引范围 {part.index.min()} 到 {part.index.max()}")
            aligned_part = part.reindex(full_date_range)
            aligned_parts.append(aligned_part)
            print(f"  数据部分 {i+1} 重新索引后: Shape {aligned_part.shape}, 索引范围 {aligned_part.index.min()} 到 {aligned_part.index.max()}")
        else:
            print(f"  数据部分 {i+1} 为空，跳过")

    # 合并已对齐的数据部分
    if aligned_parts:
        combined_data_weekly_final = pd.concat(aligned_parts, axis=1)
        print(f"  合并所有 {len(aligned_parts)} 个已对齐的周度数据部分. 合并后 Shape: {combined_data_weekly_final.shape}")
    else:
        print("错误：[Data Prep] 没有有效的数据部分可以合并。")
        return None, None, None, None

    # 处理重复列
    data_cleaner = DataCleaner()
    combined_data_weekly_final = data_cleaner.remove_duplicate_columns(combined_data_weekly_final, "[最终合并] ")
    removed_variables_detailed_log.extend(data_cleaner.get_removed_variables_log())

    # 数据已经对齐到完整日期范围
    all_data_aligned_weekly = combined_data_weekly_final

    # 恢复频率信息（重新索引会丢失频率）
    if hasattr(full_date_range, 'freq') and full_date_range.freq is not None:
        all_data_aligned_weekly.index.freq = full_date_range.freq

    print(f"  最终周度数据对齐完成. Shape: {all_data_aligned_weekly.shape}")
    print(f"  最终索引频率: {all_data_aligned_weekly.index.freq}")

    # 移除全NaN列（但保留全NaN行以匹配原始版本行为）
    all_data_aligned_weekly, final_clean_log = clean_dataframe(
        all_data_aligned_weekly,
        remove_all_nan_cols=True,
        remove_all_nan_rows=False,  # 原始版本不移除全NaN行
        log_prefix="[最终清理] "
    )
    removed_variables_detailed_log.extend(final_clean_log)

    if all_data_aligned_weekly is None or all_data_aligned_weekly.empty:
        print("错误: [Data Prep] 最终合并和对齐后的数据为空。")
        return None, None, None, None

    # 继续到平稳性检查
    return apply_final_stationarity_check(
        all_data_aligned_weekly, actual_target_variable_name, target_sheet_cols,
        monthly_transform_log, removed_variables_detailed_log, var_industry_map,
        raw_columns_across_all_sheets, reference_predictor_variables
    )

def prepare_data_from_ui_input(
    input_df: pd.DataFrame,
    target_variable: str,
    selected_variables: List[str] = None,
    training_start_date: str = None,
    validation_end_date: str = None,
    skip_stationarity_check: bool = False
) -> Tuple[pd.DataFrame, Dict[str, Any], Dict[str, Any]]:
    """
    从UI输入准备训练数据

    Args:
        input_df: 输入数据DataFrame
        target_variable: 目标变量名
        selected_variables: 选择的变量列表
        training_start_date: 训练开始日期
        validation_end_date: 验证结束日期
        skip_stationarity_check: 是否跳过平稳性检查

    Returns:
        Tuple[pd.DataFrame, Dict[str, Any], Dict[str, Any]]: (处理后的数据, 转换详情, 移除变量日志)
    """
    print(f"\n--- [UI数据准备] 开始处理UI输入数据 ---")

    try:
        # 验证输入数据
        if input_df is None or input_df.empty:
            raise ValueError("输入数据为空")

        if target_variable not in input_df.columns:
            raise ValueError(f"目标变量 '{target_variable}' 不在数据中")

        # 构建最终变量列表
        final_variables = [target_variable]
        if selected_variables:
            print(f"  🔍 [UI数据准备] 开始变量名映射:")
            print(f"    UI选择的变量: {selected_variables}")

            # 创建变量名映射（处理大小写差异）
            available_columns = list(input_df.columns)
            variable_mapping = {}

            for ui_var in selected_variables:
                # 直接匹配
                if ui_var in available_columns:
                    variable_mapping[ui_var] = ui_var
                    continue

                # 标准化匹配
                ui_var_normalized = unicodedata.normalize('NFKC', str(ui_var)).strip().lower()
                found_match = False

                for col in available_columns:
                    col_normalized = unicodedata.normalize('NFKC', str(col)).strip().lower()
                    if ui_var_normalized == col_normalized:
                        variable_mapping[ui_var] = col
                        found_match = True
                        break

                if not found_match:
                    print(f"    ⚠️ 警告: UI变量 '{ui_var}' 在数据中未找到匹配列")

            # 添加成功映射的变量
            for ui_var, actual_col in variable_mapping.items():
                if actual_col not in final_variables:
                    final_variables.append(actual_col)

            print(f"    ✅ 成功映射的变量: {variable_mapping}")
            print(f"    📋 最终变量列表: {final_variables}")

        # 筛选数据
        missing_vars = [var for var in final_variables if var not in input_df.columns]
        if missing_vars:
            raise ValueError(f"以下变量在数据中不存在: {missing_vars}")

        filtered_df = input_df[final_variables].copy()
        print(f"  📊 [UI数据准备] 筛选后数据形状: {filtered_df.shape}")

        # 日期范围筛选
        if training_start_date or validation_end_date:
            print(f"  📅 [UI数据准备] 应用日期范围筛选:")
            original_shape = filtered_df.shape[0]

            if training_start_date:
                try:
                    start_date = pd.to_datetime(training_start_date)
                    filtered_df = filtered_df[filtered_df.index >= start_date]
                    print(f"    应用开始日期后剩余: {filtered_df.shape[0]} 行")
                except Exception as e:
                    print(f"    ⚠️ 开始日期解析失败: {e}")

            if validation_end_date:
                try:
                    end_date = pd.to_datetime(validation_end_date)
                    filtered_df = filtered_df[filtered_df.index <= end_date]
                    print(f"    应用结束日期后剩余: {filtered_df.shape[0]} 行")
                except Exception as e:
                    print(f"    ⚠️ 结束日期解析失败: {e}")

            print(f"    日期筛选: {original_shape} → {filtered_df.shape[0]} 行")

        # 平稳性检查和转换
        transform_details = {}
        removed_variables_log = {}

        if not skip_stationarity_check:
            print(f"  🔄 [UI数据准备] 执行平稳性检查和转换...")

            try:
                transformed_df, transform_log, removed_info = ensure_stationarity(
                    filtered_df,
                    skip_cols={target_variable} if target_variable in filtered_df.columns else set()
                )

                # 整理转换详情
                for var, details in transform_log.items():
                    if isinstance(details, dict) and 'status' in details:
                        transform_details[var] = details
                    else:
                        transform_details[var] = {'status': str(details)}

                filtered_df = transformed_df
                print(f"    ✅ 平稳性转换完成，处理了 {len(transform_details)} 个变量")

            except Exception as e:
                print(f"    ⚠️ 平稳性检查失败: {e}")
                print(f"    将跳过平稳性转换，使用原始数据")
        else:
            print(f"  ⏭️ [UI数据准备] 跳过平稳性检查")

        # 最终数据清理 - 注意：原始版本不移除全NaN行，只移除全NaN列
        # filtered_df = filtered_df.dropna(how='all')  # 移除此行以匹配原始版本行为

        # 生成处理摘要
        print(f"\n  📋 [UI数据准备] 处理摘要:")
        print(f"    最终数据形状: {filtered_df.shape}")
        print(f"    目标变量: {target_variable}")
        print(f"    预测变量数量: {len(final_variables) - 1}")
        print(f"    转换的变量数量: {len(transform_details)}")

        if not filtered_df.empty:
            print(f"    数据时间范围: {filtered_df.index.min()} 到 {filtered_df.index.max()}")

        return filtered_df, transform_details, removed_variables_log

    except Exception as e:
        print(f"\n❌ [UI数据准备] 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

        # 返回空结果
        empty_df = pd.DataFrame()
        return empty_df, {}, {'error': str(e)}

# 导出的函数
__all__ = [
    'prepare_data',
    'prepare_data_from_ui_input'
]
