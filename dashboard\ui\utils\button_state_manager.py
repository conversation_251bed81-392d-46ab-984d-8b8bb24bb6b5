# -*- coding: utf-8 -*-
"""
按钮状态管理工具
提供按钮状态的优化管理，包括缓存机制以提高性能
"""

import streamlit as st
import time
import logging
from typing import List, Dict, Any

# 设置日志
logger = logging.getLogger(__name__)


def optimize_button_state_management(
    main_module_options: List[str],
    current_main_module: str,
    cache_duration: float = 1.0
) -> Dict[str, str]:
    """
    优化按钮状态管理，强制每次重新计算（禁用缓存）

    Args:
        main_module_options: 主模块选项列表
        current_main_module: 当前选中的主模块
        cache_duration: 缓存持续时间（秒）- 已禁用

    Returns:
        Dict[str, str]: 按钮状态字典，键为模块名，值为按钮类型
    """
    try:
        # 强制每次重新计算按钮状态，不使用缓存
        button_states = {}
        for module in main_module_options:
            button_states[module] = get_button_state_for_module(module, current_main_module)

        return button_states

    except Exception as e:
        logger.error(f"按钮状态管理失败: {e}")
        # 降级到简单计算
        button_states = {}
        for module in main_module_options:
            button_states[module] = get_button_state_for_module(module, current_main_module)
        return button_states


def _fallback_button_state_management(
    main_module_options: List[str],
    current_main_module: str,
    cache_duration: float = 1.0
) -> Dict[str, str]:
    """Fallback按钮状态管理 - 强制每次重新计算"""
    try:
        # 强制每次重新计算按钮状态，不使用缓存
        button_states = {}
        for module in main_module_options:
            button_states[module] = get_button_state_for_module(module, current_main_module)

        return button_states

    except Exception as e:
        logger.error(f"Fallback按钮状态管理失败: {e}")
        # 最简单的降级实现
        button_states = {}
        for module in main_module_options:
            button_states[module] = "primary" if module == current_main_module else "secondary"
        return button_states


def get_button_state_for_module(module: str, current_module: str) -> str:
    """
    获取特定模块的按钮状态

    Args:
        module: 模块名
        current_module: 当前选中的模块

    Returns:
        str: 按钮类型 ("primary" 或 "secondary")
    """
    result = "primary" if module == current_module else "secondary"

    # 详细调试信息

    return result


def is_unified_button_cache_valid(tools_refactor, current_main_module: str, cache_duration: float = 1.0) -> bool:
    """检查统一缓存系统中的按钮状态缓存是否有效"""
    try:
        cache_key = f'button_states_{current_main_module}'
        cached_state = tools_refactor.get_tools_state('ui_cache', f'button_states.{cache_key}')

        if not cached_state:
            return False

        # 检查模块是否匹配
        if cached_state.get('current_module') != current_main_module:
            return False

        # 检查时间是否在有效期内
        cached_time = cached_state.get('timestamp', 0)
        current_time = time.time()

        return (current_time - cached_time) < cache_duration

    except Exception as e:
        logger.error(f"检查统一按钮缓存失败: {e}")
        return False


def is_button_state_cache_valid(
    current_main_module: str, 
    cache_duration: float = 1.0
) -> bool:
    """
    检查按钮状态缓存是否有效
    
    Args:
        current_main_module: 当前主模块
        cache_duration: 缓存持续时间（秒）
        
    Returns:
        bool: 缓存是否有效
    """
    try:
        # 优先使用统一状态管理器
        from dashboard.state_management import get_unified_manager

        state_manager = get_unified_manager()
        if state_manager:
            current_time = time.time()

            cached_state = state_manager.get_state('ui.button_state_cache')
            cached_time = state_manager.get_state('ui.button_state_time', 0)

            if not cached_state or not cached_time:
                return False

            # 检查缓存是否过期
            if current_time - cached_time >= cache_duration:
                return False

            # 检查模块是否匹配
            if cached_state.get('current_module') != current_main_module:
                return False

            return True

        # 降级到session_state
        logger.warning("统一状态管理器不可用，使用session_state检查缓存")

    except Exception as e:
        logger.error(f"缓存检查失败: {e}")

    # 降级实现
    button_state_cache_key = 'button_state_cache'
    button_state_time_key = 'button_state_time'
    current_time = time.time()

    # 检查缓存是否存在
    if (button_state_cache_key not in st.session_state or
        button_state_time_key not in st.session_state):
        return False

    cached_time = st.session_state[button_state_time_key]
    cached_state = st.session_state[button_state_cache_key]

    # 检查缓存是否过期
    if current_time - cached_time >= cache_duration:
        return False

    # 检查模块是否匹配
    if cached_state.get('current_module') != current_main_module:
        return False

    return True


def clear_button_state_cache() -> None:
    """
    清除按钮状态缓存 - 增强版，清除所有相关缓存
    """
    try:
        # 1. 使用统一状态管理器清理缓存
        from dashboard.state_management import get_unified_manager

        state_manager = get_unified_manager()
        if state_manager:
            # 只清除存在的状态，避免不必要的警告
            cache_keys = [
                'ui.button_state_cache',
                'ui.button_state_time',
                'navigation.cache',
                'ui.navigation_cache'
            ]

            # 获取所有现有键
            existing_keys = set(state_manager.get_all_keys())

            # 只清除存在的键
            for key in cache_keys:
                if key in existing_keys:
                    state_manager.clear_state(key)

        # 2. 清除工具模块缓存
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor
            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 清除所有按钮状态缓存
                for module in ['数据预览', '应用工具', '模型分析', '策略回测', '实时监控']:
                    cache_key = f'button_states_{module}'
                    tools_refactor.clear_tools_state('ui_cache', f'button_states.{cache_key}')
                tools_refactor.clear_tools_state('ui_cache', 'button_states.last_update')
        except:
            pass

        # 3. 降级到session_state清理
        cache_keys_to_clear = [
            'button_state_cache',
            'button_state_time',
            'navigation_cache',
            'module_selector_cache',
            'ui_button_cache',
            'sidebar_cache'
        ]

        for key in cache_keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]

        logger.debug("按钮状态缓存已清除")

    except Exception as e:
        logger.error(f"清除按钮状态缓存失败: {e}")


def get_cached_button_states() -> Dict[str, Any]:
    """
    获取缓存的按钮状态信息 - 使用统一状态管理器

    Returns:
        Dict[str, Any]: 缓存信息，包括状态和时间戳
    """
    try:
        from dashboard.state_management import get_unified_manager
        state_manager = get_unified_manager()

        if state_manager:
            # 使用统一状态管理器
            cache_data = state_manager.get_state('ui.button_state_cache')
            cache_time = state_manager.get_state('ui.button_state_time')

            return {
                'cache': cache_data,
                'timestamp': cache_time,
                'exists': cache_data is not None and cache_time is not None
            }
    except ImportError:
        logger.warning("统一状态管理器不可用，使用降级模式")

    # 统一状态管理器不可用时，返回空缓存
    return {
        'cache': None,
        'timestamp': None,
        'exists': False
    }


def update_button_state_cache(
    main_module_options: List[str],
    current_main_module: str
) -> None:
    """
    强制更新按钮状态缓存
    
    Args:
        main_module_options: 主模块选项列表
        current_main_module: 当前选中的主模块
    """
    # 清除现有缓存
    clear_button_state_cache()
    
    # 重新计算并缓存
    optimize_button_state_management(main_module_options, current_main_module)


def get_button_states_for_modules(
    modules: List[str], 
    current_module: str
) -> Dict[str, str]:
    """
    批量获取多个模块的按钮状态
    
    Args:
        modules: 模块列表
        current_module: 当前选中的模块
        
    Returns:
        Dict[str, str]: 按钮状态字典
    """
    return {
        module: get_button_state_for_module(module, current_module)
        for module in modules
    }


# 为了向后兼容，保持原有的函数名
def _optimize_button_state_management(main_module_options, current_main_module):
    """向后兼容的函数名"""
    return optimize_button_state_management(main_module_options, current_main_module)


__all__ = [
    'optimize_button_state_management',
    'get_button_state_for_module',
    'is_button_state_cache_valid',
    'clear_button_state_cache',
    'get_cached_button_states',
    'update_button_state_cache',
    'get_button_states_for_modules',
    '_optimize_button_state_management'  # 向后兼容
]
