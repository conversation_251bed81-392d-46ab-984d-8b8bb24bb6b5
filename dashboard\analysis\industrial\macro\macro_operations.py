"""
Industrial Macro Operations Analysis Module
工业宏观运行分析模块 - 主入口文件
"""

# 导入新的模块化组件
from .macro_operations_ui import render_macro_operations_tab, render_macro_operations_analysis

# 保持向后兼容性
__all__ = ['render_macro_operations_tab', 'render_macro_operations_analysis']

# 导入必要的模块
import streamlit as st
import pandas as pd
from typing import Any, Optional, List
import plotly.graph_objects as go

# 导入统一状态管理器
try:
    from dashboard.state_management import get_unified_manager
except ImportError:
    def get_unified_manager():
        return None

def get_monitoring_state(key: str, default: Any = None):
    """获取监测分析状态"""
    try:
        unified_manager = get_unified_manager()
        if unified_manager:
            return unified_manager.get_state(f'monitoring.industrial.macro.{key}', default)
        else:
            return st.session_state.get(f'monitoring_industrial_macro_{key}', default)
    except Exception:
        return default


def set_monitoring_state(key: str, value: Any, is_initialization: bool = False):
    """设置监测分析状态"""
    try:
        unified_manager = get_unified_manager()
        if unified_manager:
            return unified_manager.set_state(f'monitoring.industrial.macro.{key}', value, is_initialization=is_initialization)
        else:
            st.session_state[f'monitoring_industrial_macro_{key}'] = value
            return True
    except Exception:
        return False

def initialize_monitoring_states():
    """预初始化监测分析状态，避免第一次点击时刷新"""
    try:
        unified_manager = get_unified_manager()
        if unified_manager:
            # 静默初始化所有时间筛选相关的状态
            time_range_keys = [
                'macro_time_range_chart1',
                'macro_time_range_chart2',
                'macro_time_range_chart3'
            ]

            for key in time_range_keys:
                full_key = f'monitoring.industrial.macro.{key}'
                # 只有在状态不存在时才初始化
                if unified_manager.get_state(full_key) is None:
                    unified_manager.silent_initialize_state(full_key, "3年")

            return True
    except Exception:
        return False


def load_template_data(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Load and parse the uploaded Excel template file

    Args:
        uploaded_file: Streamlit uploaded file object

    Returns:
        DataFrame with parsed data or None if error
    """
    try:
        # Get column names from first row
        first_row_data = pd.read_excel(uploaded_file, sheet_name='分行业工业增加值同比增速', header=0, nrows=1)
        column_names = first_row_data.iloc[0].tolist()

        # Read the data starting from row 3 (skip header rows and unit row)
        df_final = pd.read_excel(uploaded_file, sheet_name='分行业工业增加值同比增速', header=None, skiprows=3)
        df_final.columns = column_names

        # Remove the first row if it contains units (单位, %, etc.)
        if len(df_final) > 0 and str(df_final.iloc[0, 0]).strip() == '单位':
            df_final = df_final.iloc[1:].reset_index(drop=True)

        # Convert the first column to datetime if it contains dates
        if len(df_final.columns) > 0 and len(df_final) > 0:
            try:
                # Try to convert the first column to datetime
                first_col = df_final.iloc[:, 0]

                # Check if the first column contains date-like data
                sample_values = first_col.dropna().head(5).astype(str)
                has_dates = any(
                    bool(pd.to_datetime(val, errors='coerce'))
                    for val in sample_values
                    if val and val != 'nan'
                )

                if has_dates:
                    # Convert to datetime with specific format handling
                    df_final.iloc[:, 0] = pd.to_datetime(df_final.iloc[:, 0], errors='coerce', format='%Y-%m-%d')

                    # Remove rows where date conversion failed
                    valid_dates = df_final.iloc[:, 0].notna()
                    df_final = df_final[valid_dates]

                    if not df_final.empty:
                        df_final.set_index(df_final.columns[0], inplace=True)

            except Exception:
                # If date conversion fails, keep the first column as is
                pass

        return df_final

    except Exception as e:
        st.error(f"Error loading template data: {e}")
        return None


def load_weights_data(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Load and parse the weights data from Excel template file

    Args:
        uploaded_file: Streamlit uploaded file object

    Returns:
        DataFrame with weights data or None if error
    """
    try:
        df_weights = pd.read_excel(uploaded_file, sheet_name='工业增加值分行业指标权重')
        return df_weights

    except Exception as e:
        st.error(f"Error loading weights data: {e}")
        return None


def clean_overall_variable_name_for_legend(var: str) -> str:
    """
    Clean overall industrial variable name for display in legend
    将完整的列名转换为简洁的显示名称
    """
    # 定义变量名称映射
    name_mapping = {
        '规模以上工业增加值:当月同比': '工业总体',
        '制造业PMI': '制造业',
        '工业生产者出厂价格指数:当月同比': '采矿业',
        '工业企业利润总额:累计同比': '水电燃气供应业'
    }

    # 如果在映射中找到，返回映射名称，否则返回原名称
    return name_mapping.get(var, var)


def load_overall_industrial_data(uploaded_file) -> Optional[pd.DataFrame]:
    """
    Load and parse the overall industrial value-added data from Excel template file

    Args:
        uploaded_file: Streamlit uploaded file object

    Returns:
        DataFrame with overall industrial data or None if error
    """
    try:
        # Read column names from the second row (index 1) - where the actual column headers are
        header_row = pd.read_excel(uploaded_file, sheet_name='总体工业增加值同比增速', header=None, nrows=1, skiprows=1)
        column_names = header_row.iloc[0].tolist()

        # Read the data starting from row 5 (skip title, header, frequency, and unit rows)
        df_final = pd.read_excel(uploaded_file, sheet_name='总体工业增加值同比增速', header=None, skiprows=4)

        # Check if column names and data columns match
        if len(column_names) != len(df_final.columns):
            # Adjust column names to match data columns
            if len(column_names) > len(df_final.columns):
                column_names = column_names[:len(df_final.columns)]
            else:
                # Pad with generic names if needed
                for i in range(len(column_names), len(df_final.columns)):
                    column_names.append(f"Column_{i}")

        df_final.columns = column_names

        # Remove the first row if it contains units (单位, %, etc.)
        if len(df_final) > 0 and str(df_final.iloc[0, 0]).strip() == '单位':
            df_final = df_final.iloc[1:].reset_index(drop=True)

        # Convert the first column to datetime if it contains dates
        if len(df_final.columns) > 0 and len(df_final) > 0:
            try:
                # Try to convert the first column to datetime
                first_col = df_final.iloc[:, 0]

                # Check if the first column contains date-like data
                sample_values = first_col.dropna().head(5).astype(str)
                has_dates = any(
                    bool(pd.to_datetime(val, errors='coerce'))
                    for val in sample_values
                    if val and val != 'nan'
                )

                if has_dates:
                    # Try multiple date formats for better compatibility
                    date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y-%m', '%Y/%m', None]  # None for auto-detection
                    converted_dates = None

                    for fmt in date_formats:
                        try:
                            if fmt is None:
                                # Auto-detection without format specification
                                converted_dates = pd.to_datetime(df_final.iloc[:, 0], errors='coerce')
                            else:
                                converted_dates = pd.to_datetime(df_final.iloc[:, 0], errors='coerce', format=fmt)

                            # Check if conversion was successful (at least 50% of values converted)
                            valid_count = converted_dates.notna().sum()
                            total_count = len(converted_dates)
                            if valid_count > 0 and (valid_count / total_count) >= 0.5:
                                break
                        except:
                            continue

                    if converted_dates is not None and converted_dates.notna().sum() > 0:
                        # Use the successfully converted dates
                        df_final.iloc[:, 0] = converted_dates

                        # Remove rows where date conversion failed
                        valid_dates = df_final.iloc[:, 0].notna()
                        df_final = df_final[valid_dates]

                        if not df_final.empty:
                            # Ensure the index is properly set as DatetimeIndex
                            df_final.set_index(df_final.columns[0], inplace=True)

                            # Verify the index is DatetimeIndex
                            if not isinstance(df_final.index, pd.DatetimeIndex):
                                pass  # Index is not DatetimeIndex
                    else:
                        pass  # Date conversion failed for all formats, keeping original data

            except Exception:
                # If date conversion fails, keep the first column as is
                pass

        # Special processing: Set January and February values to NaN for "规模以上工业增加值:当月同比"
        target_column = "规模以上工业增加值:当月同比"
        if target_column in df_final.columns and hasattr(df_final.index, 'month'):
            # Create a mask for January (month=1) and February (month=2)
            jan_feb_mask = (df_final.index.month == 1) | (df_final.index.month == 2)
            # Set January and February values to NaN for the target column
            import numpy as np
            df_final.loc[jan_feb_mask, target_column] = np.nan

        return df_final

    except Exception as e:
        st.error(f"Error loading overall industrial data: {e}")
        return None



def validate_data_format(df_macro: pd.DataFrame, df_weights: pd.DataFrame, target_columns: List[str]) -> tuple:
    """
    验证数据格式并返回详细的诊断信息

    Returns:
        (is_valid: bool, error_message: str, debug_info: dict)
    """
    debug_info = {}

    # 检查基本数据
    if df_macro.empty:
        return False, "分行业工业增加值同比增速数据为空", debug_info
    if df_weights.empty:
        return False, "权重数据为空", debug_info

    debug_info['macro_shape'] = df_macro.shape
    debug_info['weights_shape'] = df_weights.shape
    debug_info['target_columns_count'] = len(target_columns)

    # 检查权重数据必要列
    required_weight_columns = ['指标名称', '出口依赖', '上中下游']
    weight_year_columns = ['权重_2012', '权重_2018', '权重_2020']

    missing_columns = [col for col in required_weight_columns if col not in df_weights.columns]
    if missing_columns:
        return False, f"权重数据缺少必要列: {missing_columns}", debug_info

    # 检查是否至少有一个权重年份列
    available_weight_columns = [col for col in weight_year_columns if col in df_weights.columns]
    if not available_weight_columns:
        return False, f"权重数据缺少权重列，需要以下任一列: {weight_year_columns}", debug_info

    debug_info['available_weight_columns'] = available_weight_columns

    debug_info['weights_columns'] = list(df_weights.columns)

    # 检查目标列匹配情况
    available_columns = [col for col in target_columns if col in df_macro.columns]
    debug_info['available_columns_count'] = len(available_columns)
    debug_info['available_columns'] = available_columns[:5]  # 只显示前5个

    if not available_columns:
        return False, "目标列在分行业工业增加值同比增速数据中未找到匹配项", debug_info

    # 检查权重数据中的指标名称匹配
    weight_indicators = df_weights['指标名称'].dropna().tolist()
    matched_indicators = [ind for ind in weight_indicators if ind in available_columns]
    debug_info['matched_indicators_count'] = len(matched_indicators)
    debug_info['matched_indicators'] = matched_indicators[:5]  # 只显示前5个

    if not matched_indicators:
        return False, "权重数据中的指标名称与分行业工业增加值同比增速数据列名无匹配项", debug_info

    return True, "数据格式验证通过", debug_info


def get_weight_for_year(df_weights_row: pd.Series, year: int) -> float:
    """
    根据年份选择合适的权重

    Args:
        df_weights_row: 权重数据行
        year: 年份

    Returns:
        对应年份的权重值
    """
    if year >= 2020 and '权重_2020' in df_weights_row.index and pd.notna(df_weights_row['权重_2020']):
        return df_weights_row['权重_2020']
    elif year >= 2018 and '权重_2018' in df_weights_row.index and pd.notna(df_weights_row['权重_2018']):
        return df_weights_row['权重_2018']
    elif year >= 2012 and '权重_2012' in df_weights_row.index and pd.notna(df_weights_row['权重_2012']):
        return df_weights_row['权重_2012']
    else:
        # 2012年之前不计算加权增速，返回0
        return 0.0


def filter_data_from_2012(df: pd.DataFrame) -> pd.DataFrame:
    """
    过滤数据，只保留2012年及以后的数据

    Args:
        df: 输入数据框

    Returns:
        过滤后的数据框
    """
    if df.empty:
        return df

    # 找到日期类型的索引
    date_indices = []
    for idx in df.index:
        if hasattr(idx, 'year') and idx.year >= 2012:
            date_indices.append(idx)

    if date_indices:
        return df.loc[date_indices]
    else:
        return pd.DataFrame()


def calculate_weighted_groups(df_macro: pd.DataFrame, df_weights: pd.DataFrame, target_columns: List[str]) -> pd.DataFrame:
    """
    Calculate weighted groups based on export dependency and upstream/downstream categories
    支持按年份动态选择权重：2012年使用2012年权重，2018年开始使用2018年权重，2020年开始使用2020年权重
    丢弃2012年以前的数据

    Args:
        df_macro: DataFrame with macro operations data
        df_weights: DataFrame with weights data
        target_columns: List of target column names to process

    Returns:
        DataFrame with weighted group time series
    """
    try:
        # 过滤数据，只保留2012年及以后的数据
        df_macro_filtered = filter_data_from_2012(df_macro)

        if df_macro_filtered.empty:
            return pd.DataFrame()

        # 验证数据格式
        is_valid, error_msg, debug_info = validate_data_format(df_macro_filtered, df_weights, target_columns)
        if not is_valid:
            return pd.DataFrame()

        # Initialize result DataFrame with the same index as filtered macro data
        result_df = pd.DataFrame(index=df_macro_filtered.index)

        # Filter target columns that exist in both datasets
        available_columns = [col for col in target_columns if col in df_macro_filtered.columns]

        # Create mapping from indicator name to weights info (保存整行数据以便动态选择权重)
        weights_mapping = {}
        for _, row in df_weights.iterrows():
            indicator_name = row['指标名称']
            if pd.notna(indicator_name) and indicator_name in available_columns:
                weights_mapping[indicator_name] = {
                    '出口依赖': row['出口依赖'],
                    '上中下游': row['上中下游'],
                    'weights_row': row  # 保存整行数据以便动态选择权重
                }

        if len(weights_mapping) == 0:
            return pd.DataFrame()

        # Group by export dependency
        export_groups = {}
        # Group by upstream/downstream
        stream_groups = {}

        # Categorize indicators (不再预先绑定权重)
        for indicator, info in weights_mapping.items():
            export_dep = info['出口依赖']
            stream_type = info['上中下游']

            # 检查数据有效性
            if pd.notna(export_dep):
                if export_dep not in export_groups:
                    export_groups[export_dep] = []
                export_groups[export_dep].append(indicator)

            if pd.notna(stream_type):
                if stream_type not in stream_groups:
                    stream_groups[stream_type] = []
                stream_groups[stream_type].append(indicator)

        if len(export_groups) == 0 and len(stream_groups) == 0:
            return pd.DataFrame()

        # Calculate weighted sums for export dependency groups
        for group_name, indicators in export_groups.items():
            if indicators:  # Only if group has indicators
                # Initialize with NaN to preserve missing data
                weighted_series = pd.Series(index=df_macro_filtered.index, dtype=float)

                for indicator in indicators:
                    if indicator in df_macro_filtered.columns:
                        # Convert to numeric, keep NaN values (no filling)
                        series = pd.to_numeric(df_macro_filtered[indicator], errors='coerce')
                        weights_row = weights_mapping[indicator]['weights_row']

                        # 对每个时间点使用对应年份的权重
                        for idx in df_macro_filtered.index:
                            if hasattr(idx, 'year'):  # 确保是日期索引
                                year = idx.year
                                weight = get_weight_for_year(weights_row, year)
                                if not pd.isna(series.loc[idx]) and weight > 0:
                                    # 如果weighted_series在该位置是NaN，初始化为0
                                    if pd.isna(weighted_series.loc[idx]):
                                        weighted_series.loc[idx] = 0.0
                                    weighted_series.loc[idx] += series.loc[idx] * weight

                result_df[f'出口依赖_{group_name}'] = weighted_series

        # Calculate weighted sums for upstream/downstream groups
        for group_name, indicators in stream_groups.items():
            if indicators:  # Only if group has indicators
                # Initialize with NaN to preserve missing data
                weighted_series = pd.Series(index=df_macro_filtered.index, dtype=float)

                for indicator in indicators:
                    if indicator in df_macro_filtered.columns:
                        # Convert to numeric, keep NaN values (no filling)
                        series = pd.to_numeric(df_macro_filtered[indicator], errors='coerce')
                        weights_row = weights_mapping[indicator]['weights_row']

                        # 对每个时间点使用对应年份的权重
                        for idx in df_macro_filtered.index:
                            if hasattr(idx, 'year'):  # 确保是日期索引
                                year = idx.year
                                weight = get_weight_for_year(weights_row, year)
                                if not pd.isna(series.loc[idx]) and weight > 0:
                                    # 如果weighted_series在该位置是NaN，初始化为0
                                    if pd.isna(weighted_series.loc[idx]):
                                        weighted_series.loc[idx] = 0.0
                                    weighted_series.loc[idx] += series.loc[idx] * weight

                result_df[f'上中下游_{group_name}'] = weighted_series

        return result_df

    except Exception as e:
        return pd.DataFrame()


def create_single_axis_chart(df: pd.DataFrame, variables: List[str], title: str, time_range: str = "全部", custom_start_date: str = None, custom_end_date: str = None) -> go.Figure:
    """
    Create single Y-axis time series chart for specified variables using Plotly

    Args:
        df: DataFrame with time series data
        variables: List of variable names to plot
        title: Chart title
        time_range: Time range selection ("1年", "3年", "5年", "全部", "自定义")
        custom_start_date: Custom start date (YYYY-MM format)
        custom_end_date: Custom end date (YYYY-MM format)

    Returns:
        Plotly figure object with single Y-axis
    """
    # Apply time range filtering first
    filtered_df = df.copy()

    if time_range != "全部" and not df.empty:
        # Get the latest date in the dataset
        latest_date = df.index.max()

        if time_range == "自定义" and custom_start_date and custom_end_date:
            # Handle custom date range
            try:
                start_date = pd.to_datetime(custom_start_date + "-01")
                end_date = pd.to_datetime(custom_end_date + "-01") + pd.offsets.MonthEnd(0)
            except:
                start_date = df.index.min()
                end_date = df.index.max()
        else:
            # Calculate start date based on time range selection - from latest date backwards
            if time_range == "1年":
                start_date = latest_date - pd.DateOffset(years=1)
            elif time_range == "3年":
                start_date = latest_date - pd.DateOffset(years=3)
            elif time_range == "5年":
                start_date = latest_date - pd.DateOffset(years=5)
            elif time_range == "自定义":
                # Fallback for custom range when dates are not provided - use full range
                start_date = df.index.min()
                end_date = df.index.max()
            else:
                # Default fallback
                start_date = df.index.min()
                end_date = df.index.max()

            # Set end_date for non-custom ranges
            if time_range != "自定义":
                end_date = latest_date

        # Filter data using date range
        try:
            # Ensure the index is datetime
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)

            # Filter using boolean indexing - include data within date range
            if time_range == "自定义":
                mask = (df.index >= start_date) & (df.index <= end_date)
            else:
                mask = df.index >= start_date
            filtered_df = df.loc[mask]

        except Exception as e:
            # Fallback: if date filtering fails, use tail method
            if time_range == "1年":
                filtered_df = df.tail(12)
            elif time_range == "3年":
                filtered_df = df.tail(36)
            elif time_range == "5年":
                filtered_df = df.tail(60)
            else:
                filtered_df = df.copy()

    # Define colors for each variable
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

    # Create figure with single y-axis
    fig = go.Figure()

    for i, var in enumerate(variables):
        if var in filtered_df.columns:
            # Clean the data - remove non-numeric values
            series = filtered_df[var].copy()

            series = pd.to_numeric(series, errors='coerce')
            series = series.dropna()

            if not series.empty:
                # Get color for this variable
                color = colors[i % len(colors)]

                # Create display name for legend (remove prefix and add suffix for export dependency)
                display_name = var
                if var.startswith('出口依赖_'):
                    display_name = var.replace('出口依赖_', '') + '行业'
                elif var.startswith('上中下游_'):
                    display_name = var.replace('上中下游_', '')

                # Add trace to single y-axis
                line = go.Scatter(
                    x=series.index,
                    y=series.values,
                    showlegend=True,
                    line=dict(color=color, width=1.5),
                    connectgaps=True,
                    mode='lines+markers',
                    marker=dict(size=3),
                    name=display_name
                )
                fig.add_trace(line)

    # Calculate actual data range for x-axis
    all_dates = []
    for var in variables:
        if var in filtered_df.columns:
            series = pd.to_numeric(filtered_df[var], errors='coerce').dropna()
            if not series.empty:
                all_dates.extend(series.index.tolist())

    if all_dates:
        min_date = min(all_dates)
        max_date = max(all_dates)
    else:
        min_date = max_date = None

    # Configure x-axis with 3-month intervals
    xaxis_config = dict(
        title="",  # No x-axis title as requested
        type="date",
        showgrid=True,
        gridwidth=1,
        gridcolor='lightgray',
        # Set tick intervals to 3 months
        dtick="M3",  # 3-month intervals
        tickformat="%Y-%m"  # Format as YYYY-MM
    )

    # Set the range to actual data if available
    if min_date and max_date:
        xaxis_config['range'] = [min_date, max_date]

    # Update layout with single y-axis configuration
    layout_config = dict(
        title="",  # Empty title
        showlegend=True,
        xaxis=xaxis_config,
        # Configure single y-axis
        yaxis=dict(
            title="加权工业增加值同比增速 (%)",
            side='left'
        ),
        legend=dict(
            orientation="h",
            yanchor="top",
            y=-0.25,  # Position legend below the time axis
            xanchor="center",
            x=0.5,
            font=dict(family="SimHei, Microsoft YaHei, Arial")
        ),
        hovermode='x unified',
        height=350,  # Smaller chart height
        margin=dict(l=50, r=50, t=40, b=150),  # Large bottom margin for legend
        font=dict(family="SimHei, Microsoft YaHei, Arial"),
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    fig.update_layout(layout_config)

    return fig





def create_overall_industrial_chart(df: pd.DataFrame, variables: List[str], var_name_mapping: dict, title: str, time_range: str = "全部", custom_start_date: str = None, custom_end_date: str = None) -> go.Figure:
    """
    Create single Y-axis time series chart for overall industrial variables with custom legend names

    Args:
        df: DataFrame with time series data
        variables: List of variable names to plot
        var_name_mapping: Dictionary mapping original variable names to display names
        title: Chart title
        time_range: Time range selection ("1年", "3年", "5年", "全部", "自定义")
        custom_start_date: Custom start date (YYYY-MM format)
        custom_end_date: Custom end date (YYYY-MM format)

    Returns:
        Plotly figure object with single Y-axis
    """
    # Apply time range filtering first
    filtered_df = df.copy()

    if time_range != "全部" and not df.empty:
        # Get the latest date in the dataset
        latest_date = df.index.max()

        if time_range == "自定义" and custom_start_date and custom_end_date:
            # Handle custom date range
            try:
                start_date = pd.to_datetime(custom_start_date + "-01")
                end_date = pd.to_datetime(custom_end_date + "-01") + pd.offsets.MonthEnd(0)
            except:
                start_date = df.index.min()
                end_date = df.index.max()
        else:
            # Calculate start date based on time range selection - from latest date backwards
            if time_range == "1年":
                start_date = latest_date - pd.DateOffset(years=1)
            elif time_range == "3年":
                start_date = latest_date - pd.DateOffset(years=3)
            elif time_range == "5年":
                start_date = latest_date - pd.DateOffset(years=5)
            elif time_range == "自定义":
                # Fallback for custom range when dates are not provided - use full range
                start_date = df.index.min()
                end_date = df.index.max()
            else:
                # Default fallback
                start_date = df.index.min()
                end_date = df.index.max()

            # Set end_date for non-custom ranges
            if time_range != "自定义":
                end_date = latest_date

        # Filter data using date range
        try:
            # Ensure the index is datetime
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)

            # Filter using boolean indexing - include data within date range
            if time_range == "自定义":
                mask = (df.index >= start_date) & (df.index <= end_date)
            else:
                mask = df.index >= start_date
            filtered_df = df.loc[mask]

        except Exception as e:
            # Fallback: if date filtering fails, use tail method
            if time_range == "1年":
                filtered_df = df.tail(12)
            elif time_range == "3年":
                filtered_df = df.tail(36)
            elif time_range == "5年":
                filtered_df = df.tail(60)
            else:
                filtered_df = df.copy()

    # Define colors for each variable
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

    # Create figure with single y-axis
    fig = go.Figure()

    for i, var in enumerate(variables):
        if var in filtered_df.columns:
            # Clean the data - remove non-numeric values
            series = filtered_df[var].copy()

            series = pd.to_numeric(series, errors='coerce')
            series = series.dropna()

            if not series.empty:
                # Get color for this variable
                color = colors[i % len(colors)]

                # Use mapped display name for legend
                display_name = var_name_mapping.get(var, var)

                # Ensure data types are compatible with Plotly
                try:
                    # Convert index to list for Plotly compatibility
                    if isinstance(series.index, pd.DatetimeIndex):
                        x_data = series.index.tolist()
                    else:
                        # Try to convert to datetime if possible
                        try:
                            x_data = pd.to_datetime(series.index).tolist()
                        except:
                            # Fallback to original index as list
                            x_data = series.index.tolist()

                    # Ensure y values are numeric and convert to list
                    y_data = series.values.tolist()

                    # Validate data before creating trace
                    if len(x_data) == len(y_data) and len(x_data) > 0:
                        # Add trace to single y-axis
                        line = go.Scatter(
                            x=x_data,
                            y=y_data,
                            showlegend=True,
                            line=dict(color=color, width=1.5),
                            connectgaps=True,
                            mode='lines+markers',
                            marker=dict(size=3),
                            name=display_name
                        )
                        fig.add_trace(line)
                    else:
                        pass  # Data length mismatch

                except Exception:
                    # Skip this variable if there's an error
                    continue

    # Calculate actual data range for x-axis
    all_dates = []
    for var in variables:
        if var in filtered_df.columns:
            series = pd.to_numeric(filtered_df[var], errors='coerce').dropna()
            if not series.empty:
                try:
                    # Ensure dates are properly formatted
                    if isinstance(series.index, pd.DatetimeIndex):
                        all_dates.extend(series.index.tolist())
                    else:
                        # Try to convert to datetime
                        try:
                            date_index = pd.to_datetime(series.index)
                            all_dates.extend(date_index.tolist())
                        except:
                            # Skip if conversion fails
                            continue
                except Exception:
                    continue

    if all_dates:
        min_date = min(all_dates)
        max_date = max(all_dates)
    else:
        min_date = max_date = None

    # Configure x-axis with 3-month intervals
    xaxis_config = dict(
        title="",  # No x-axis title as requested
        type="date",
        showgrid=True,
        gridwidth=1,
        gridcolor='lightgray',
        # Set tick intervals to 3 months
        dtick="M3",
        tickformat="%Y-%m"
    )

    if min_date and max_date:
        xaxis_config['range'] = [min_date, max_date]

    # Configure y-axis with title
    yaxis_config = dict(
        title="同比增速(%)",  # Y-axis title as requested
        showgrid=True,
        gridwidth=1,
        gridcolor='lightgray',
        zeroline=True,
        zerolinewidth=1,
        zerolinecolor='gray'
    )

    # Update layout
    fig.update_layout(
        title=title,
        xaxis=xaxis_config,
        yaxis=yaxis_config,
        hovermode='x unified',
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="top",
            y=-0.15,
            xanchor="center",
            x=0.5
        ),
        margin=dict(l=50, r=50, t=50, b=50),
        plot_bgcolor='white'
    )

    return fig

def render_macro_operations_analysis_with_data(st_obj, df_macro: pd.DataFrame, df_weights: pd.DataFrame):
    """
    使用预加载数据渲染分行业工业增加值同比增速分析（用于统一模块）

    Args:
        st_obj: Streamlit对象
        df_macro: 分行业工业增加值同比增速数据
        df_weights: 权重数据
    """
    if df_macro is None or df_weights is None:
        st_obj.error("数据未正确加载，无法进行分行业工业增加值同比增速分析")
        return

    # 直接使用传入的数据进行分析
    _render_macro_operations_analysis(st_obj, df_macro, df_weights)


def _render_macro_operations_analysis(st_obj, df: pd.DataFrame, df_weights: pd.DataFrame):
    """
    内部函数：执行分行业工业增加值同比增速分析的核心逻辑
    """
    if df is not None and df_weights is not None:
        # 预初始化状态以避免第一次点击时刷新
        initialize_monitoring_states()

        # Store the data in state
        set_monitoring_state('uploaded_data', df)
        set_monitoring_state('weights_data', df_weights)
        set_monitoring_state('file_name', 'shared_data')

        # First chart - Overall industrial value-added data from "总体工业增加值同比增速" sheet
        try:
            # Get the uploaded file from unified state management with proper namespace
            from ..industrial_analysis import get_industrial_state
            uploaded_file = get_industrial_state('macro.uploaded_file')
            if uploaded_file is not None:
                # Load overall industrial data
                df_overall = load_overall_industrial_data(uploaded_file)

                if df_overall is not None and not df_overall.empty:
                    # Extract the four variables from the overall industrial data
                    # Assuming the four variables are in columns 1-4 (after the date column)
                    overall_vars = [col for col in df_overall.columns if pd.notna(col)][:4]

                    if overall_vars:
                        # Create variable name mapping for display
                        var_name_mapping = {}
                        for var in overall_vars:
                            if var == "规模以上工业增加值:当月同比":
                                var_name_mapping[var] = "工业总体"
                            elif "制造业" in var and "当月同比" in var:
                                var_name_mapping[var] = "制造业"
                            elif "采矿业" in var and "当月同比" in var:
                                var_name_mapping[var] = "采矿业"
                            elif ("电力" in var or "热力" in var or "燃气" in var or "水生产" in var or "供应业" in var) and "当月同比" in var:
                                var_name_mapping[var] = "水电燃气供应业"
                            else:
                                # Fallback to original name if no mapping found
                                var_name_mapping[var] = var



                        # 使用fragment封装第一个图表，实现真正的局部更新
                        @st_obj.fragment
                        def render_chart1_with_controls():
                            # 添加第1个图的标题
                            st_obj.markdown("#### 工业增加值总体")

                            # Time range selector for first chart
                            current_time_range_1 = get_monitoring_state('macro_time_range_chart1', "3年")
                            time_range_options = ["1年", "3年", "5年", "全部", "自定义"]
                            default_index_1 = time_range_options.index(current_time_range_1) if current_time_range_1 in time_range_options else 1

                            time_range_1 = st_obj.radio(
                                "时间范围",
                                time_range_options,
                                index=default_index_1,
                                horizontal=True,
                                key="macro_time_range_chart1_fragment",
                                label_visibility="collapsed"
                            )

                            # 更新状态
                            if time_range_1 != current_time_range_1:
                                set_monitoring_state('macro_time_range_chart1', time_range_1)
                                current_time_range_1 = time_range_1

                            # Custom date range inputs when "自定义" is selected
                            custom_start_1 = None
                            custom_end_1 = None
                            if time_range_1 == "自定义":
                                col_start, col_end = st_obj.columns([1, 1])
                                with col_start:
                                    custom_start_1 = st_obj.text_input("开始年月", placeholder="2020-01", key="macro_custom_start_1_fragment")
                                with col_end:
                                    custom_end_1 = st_obj.text_input("结束年月", placeholder="2024-12", key="macro_custom_end_1_fragment")

                            # 渲染图表
                            fig1 = create_overall_industrial_chart(df_overall, overall_vars, var_name_mapping, "", time_range_1, custom_start_1, custom_end_1)
                            st_obj.plotly_chart(fig1, use_container_width=True)

                            return time_range_1, custom_start_1, custom_end_1

                        # 调用fragment
                        current_time_range_1, custom_start_1, custom_end_1 = render_chart1_with_controls()

                        # Data download functionality for first chart
                        from .data_processor import filter_data_by_time_range

                        # Validate that all variables exist in the dataframe
                        valid_vars = [var for var in overall_vars if var in df_overall.columns]
                        if len(valid_vars) != len(overall_vars):
                            pass  # Some variables are missing

                        if valid_vars:
                            filtered_df1 = filter_data_by_time_range(df_overall[valid_vars], current_time_range_1, custom_start_1, custom_end_1)
                        else:
                            filtered_df1 = pd.DataFrame()  # Empty dataframe if no valid variables
                        if not filtered_df1.empty:
                            # Create Excel file for download
                            import io
                            excel_buffer = io.BytesIO()
                            try:
                                with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                                    filtered_df1.to_excel(writer, sheet_name='数据', index=True)

                                excel_data1 = excel_buffer.getvalue()
                            except Exception:
                                # Create a simple fallback Excel with just the data
                                try:
                                    # Convert to simple format
                                    simple_df = filtered_df1.copy()
                                    simple_df.index = simple_df.index.astype(str)  # Convert index to string
                                    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                                        simple_df.to_excel(writer, sheet_name='数据', index=True)
                                    excel_data1 = excel_buffer.getvalue()
                                except Exception:
                                    excel_data1 = None

                            if excel_data1 is not None:
                                col_download1, col_spacer1 = st_obj.columns([1, 4])
                                with col_download1:
                                    st_obj.download_button(
                                        label="下载数据",
                                        data=excel_data1,
                                        file_name=f"总体工业增加值_{current_time_range_1}.xlsx",
                                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                        key="download_chart1"
                                    )
                            else:
                                st_obj.warning("无法创建下载文件")
                else:
                    st_obj.warning("无法加载总体工业增加值同比增速数据，请检查文件是否包含'总体工业增加值同比增速'工作表")
            else:
                st_obj.warning("未找到上传的文件，无法加载总体工业增加值数据")
        except Exception as e:
            st_obj.error(f"加载总体工业增加值数据时出错: {e}")

        # Define target columns for weighted analysis
        # 根据用户说明：分行业工业增加值数据在第二列到最后一列
        column_names = df.columns.tolist()

        # 使用第二列到最后一列作为目标列（索引从1开始）
        if len(column_names) > 1:
            target_columns = column_names[1:]  # 从第二列开始到最后一列
            target_columns = [col for col in target_columns if pd.notna(col)]  # 过滤掉空值

        if len(column_names) > 1 and target_columns:
            # Check if weighted groups are already calculated and cached
            cached_weighted_df = get_monitoring_state('cached_weighted_df')
            cached_data_hash = get_monitoring_state('cached_data_hash')

            # Create a simple hash of the input data to detect changes
            import hashlib
            current_data_str = f"{df.shape}_{df_weights.shape}_{len(target_columns)}"
            current_data_hash = hashlib.md5(current_data_str.encode()).hexdigest()

            # Only recalculate if data has changed or cache is empty
            if cached_weighted_df is None or cached_data_hash != current_data_hash:
                # Calculate weighted groups
                with st_obj.spinner("正在计算加权分组数据..."):
                    weighted_df = calculate_weighted_groups(df, df_weights, target_columns)

                # Cache the results
                set_monitoring_state('cached_weighted_df', weighted_df)
                set_monitoring_state('cached_data_hash', current_data_hash)
            else:
                # Use cached results
                weighted_df = cached_weighted_df

            if not weighted_df.empty:
                # 添加横线分隔符
                st_obj.markdown("---")

                # 添加第2个图的标题
                st_obj.markdown("#### 出口依赖分组")

                # Second chart - Export dependency groups with independent time selector
                export_vars = [col for col in weighted_df.columns if col.startswith('出口依赖_')]
                if export_vars:


                    # 使用fragment封装第二个图表
                    @st_obj.fragment
                    def render_chart2_with_controls():
                        # Time range selector for second chart
                        current_time_range_2 = get_monitoring_state('macro_time_range_chart2', "3年")
                        time_range_options = ["1年", "3年", "5年", "全部", "自定义"]
                        default_index_2 = time_range_options.index(current_time_range_2) if current_time_range_2 in time_range_options else 1

                        time_range_2 = st_obj.radio(
                            "时间范围",
                            time_range_options,
                            index=default_index_2,
                            horizontal=True,
                            key="macro_time_range_chart2_fragment",
                            label_visibility="collapsed"
                        )

                        # 更新状态
                        if time_range_2 != current_time_range_2:
                            set_monitoring_state('macro_time_range_chart2', time_range_2)
                            current_time_range_2 = time_range_2

                        # Custom date range inputs when "自定义" is selected
                        custom_start_2 = None
                        custom_end_2 = None
                        if time_range_2 == "自定义":
                            col_start, col_end = st_obj.columns([1, 1])
                            with col_start:
                                custom_start_2 = st_obj.text_input("开始年月", placeholder="2020-01", key="macro_custom_start_2_fragment")
                            with col_end:
                                custom_end_2 = st_obj.text_input("结束年月", placeholder="2024-12", key="macro_custom_end_2_fragment")

                        # 渲染图表
                        fig2 = create_single_axis_chart(weighted_df, export_vars, "", time_range_2, custom_start_2, custom_end_2)
                        st_obj.plotly_chart(fig2, use_container_width=True)

                        return time_range_2, custom_start_2, custom_end_2

                    # 调用fragment
                    current_time_range_2, custom_start_2, custom_end_2 = render_chart2_with_controls()

                    # 数据下载功能
                    from .data_processor import filter_data_by_time_range
                    filtered_df2 = filter_data_by_time_range(weighted_df[export_vars], current_time_range_2, custom_start_2, custom_end_2)
                    if not filtered_df2.empty:
                        # 创建Excel文件，包含数据和注释两个sheet
                        from .data_processor import create_grouping_mappings
                        export_groups, _ = create_grouping_mappings(df_weights)

                        # 创建注释表格数据
                        annotation_data = []
                        if export_groups:
                            # 创建权重映射
                            weights_mapping = {}
                            for _, row in df_weights.iterrows():
                                indicator_name = row['指标名称']
                                if pd.notna(indicator_name):
                                    weights_mapping[indicator_name] = row

                            for group_name, indicators in export_groups.items():
                                for indicator in indicators:
                                    if indicator in weights_mapping:
                                        # 使用2020年权重作为显示权重（最新权重）
                                        weights_row = weights_mapping[indicator]
                                        display_weight = weights_row.get('权重_2020', 0.0)
                                        annotation_data.append({
                                            "分组": group_name,
                                            "指标名称": indicator,
                                            "权重_2012": f"{weights_row.get('权重_2012', 0.0):.4f}",
                                            "权重_2018": f"{weights_row.get('权重_2018', 0.0):.4f}",
                                            "权重_2020": f"{weights_row.get('权重_2020', 0.0):.4f}"
                                        })

                        # 创建Excel文件
                        import io
                        excel_buffer = io.BytesIO()
                        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                            # 写入数据sheet
                            filtered_df2.to_excel(writer, sheet_name='数据', index=True)

                            # 写入注释sheet
                            if annotation_data:
                                annotation_df = pd.DataFrame(annotation_data)
                                annotation_df.to_excel(writer, sheet_name='注释说明', index=False)

                                # 在注释sheet中添加说明文字
                                worksheet = writer.sheets['注释说明']
                                last_row = len(annotation_data) + 2
                                worksheet.cell(row=last_row + 1, column=1, value="注：权重按年份动态选择")
                                worksheet.cell(row=last_row + 2, column=1, value="2012-2017年使用权重_2012，2018-2019年使用权重_2018，2020年及以后使用权重_2020")
                                worksheet.cell(row=last_row + 3, column=1, value="权重根据对应年份投入产出表各行业增加值占比计算")

                        excel_data2 = excel_buffer.getvalue()

                        col_download2, col_spacer2 = st_obj.columns([1, 4])
                        with col_download2:
                            st_obj.download_button(
                                label="下载数据",
                                data=excel_data2,
                                file_name=f"出口依赖分组_{current_time_range_2}.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                key="download_chart2"
                            )

                # 添加横线分隔符
                st_obj.markdown("---")

                # 添加第3个图的标题
                st_obj.markdown("#### 上中下游分组")

                # Third chart - Upstream/downstream groups with independent time selector
                stream_vars = [col for col in weighted_df.columns if col.startswith('上中下游_')]

                # 按照指定顺序排序图例：上游XX、中游XX、下游XX
                def get_sort_key(col):
                    # 提取列名中的行业类型
                    industry_type = col.replace('上中下游_', '')

                    # 按照上游、中游、下游的顺序排序
                    if industry_type.startswith('上游'):
                        return (0, industry_type)  # 上游排在最前
                    elif industry_type.startswith('中游'):
                        return (1, industry_type)  # 中游排在中间
                    elif industry_type.startswith('下游'):
                        return (2, industry_type)  # 下游排在最后
                    else:
                        return (3, industry_type)  # 未知类型排在最后

                stream_vars = sorted(stream_vars, key=get_sort_key)
                if stream_vars:
                    # 使用fragment封装第三个图表
                    @st_obj.fragment
                    def render_chart3_with_controls():
                        # Time range selector for third chart
                        current_time_range_3 = get_monitoring_state('macro_time_range_chart3', "3年")
                        time_range_options = ["1年", "3年", "5年", "全部", "自定义"]
                        default_index_3 = time_range_options.index(current_time_range_3) if current_time_range_3 in time_range_options else 1

                        time_range_3 = st_obj.radio(
                            "时间范围",
                            time_range_options,
                            index=default_index_3,
                            horizontal=True,
                            key="macro_time_range_chart3_fragment",
                            label_visibility="collapsed"
                        )

                        # 更新状态
                        if time_range_3 != current_time_range_3:
                            set_monitoring_state('macro_time_range_chart3', time_range_3)
                            current_time_range_3 = time_range_3

                        # Custom date range inputs when "自定义" is selected
                        custom_start_3 = None
                        custom_end_3 = None
                        if time_range_3 == "自定义":
                            col_start, col_end = st_obj.columns([1, 1])
                            with col_start:
                                custom_start_3 = st_obj.text_input("开始年月", placeholder="2020-01", key="macro_custom_start_3_fragment")
                            with col_end:
                                custom_end_3 = st_obj.text_input("结束年月", placeholder="2024-12", key="macro_custom_end_3_fragment")

                        # 渲染图表
                        fig3 = create_single_axis_chart(weighted_df, stream_vars, "", time_range_3, custom_start_3, custom_end_3)
                        st_obj.plotly_chart(fig3, use_container_width=True)

                        return time_range_3, custom_start_3, custom_end_3

                    # 调用fragment
                    current_time_range_3, custom_start_3, custom_end_3 = render_chart3_with_controls()

                    # 数据下载功能
                    from .data_processor import filter_data_by_time_range
                    filtered_df3 = filter_data_by_time_range(weighted_df[stream_vars], current_time_range_3, custom_start_3, custom_end_3)
                    if not filtered_df3.empty:
                        # 创建Excel文件，包含数据和注释两个sheet
                        _, stream_groups = create_grouping_mappings(df_weights)

                        # 创建注释表格数据
                        annotation_data = []
                        if stream_groups:
                            # 创建权重映射
                            weights_mapping = {}
                            for _, row in df_weights.iterrows():
                                indicator_name = row['指标名称']
                                if pd.notna(indicator_name):
                                    weights_mapping[indicator_name] = row

                            for group_name, indicators in stream_groups.items():
                                for indicator in indicators:
                                    if indicator in weights_mapping:
                                        # 显示所有年份的权重
                                        weights_row = weights_mapping[indicator]
                                        annotation_data.append({
                                            "分组": group_name,
                                            "指标名称": indicator,
                                            "权重_2012": f"{weights_row.get('权重_2012', 0.0):.4f}",
                                            "权重_2018": f"{weights_row.get('权重_2018', 0.0):.4f}",
                                            "权重_2020": f"{weights_row.get('权重_2020', 0.0):.4f}"
                                        })

                        # 创建Excel文件
                        excel_buffer = io.BytesIO()
                        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                            # 写入数据sheet
                            filtered_df3.to_excel(writer, sheet_name='数据', index=True)

                            # 写入注释sheet
                            if annotation_data:
                                annotation_df = pd.DataFrame(annotation_data)
                                annotation_df.to_excel(writer, sheet_name='注释说明', index=False)

                                # 在注释sheet中添加说明文字
                                worksheet = writer.sheets['注释说明']
                                last_row = len(annotation_data) + 2
                                worksheet.cell(row=last_row + 1, column=1, value="注：权重按年份动态选择")
                                worksheet.cell(row=last_row + 2, column=1, value="2012-2017年使用权重_2012，2018-2019年使用权重_2018，2020年及以后使用权重_2020")
                                worksheet.cell(row=last_row + 3, column=1, value="权重根据对应年份投入产出表各行业增加值占比计算")

                        excel_data3 = excel_buffer.getvalue()

                        col_download3, col_spacer3 = st_obj.columns([1, 4])
                        with col_download3:
                            st_obj.download_button(
                                label="下载数据",
                                data=excel_data3,
                                file_name=f"上中下游分组_{current_time_range_3}.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                key="download_chart3"
                            )
            else:
                st_obj.error("加权分组计算失败，请检查数据格式。")

                # 提供详细的诊断信息
                with st_obj.expander("📋 数据格式诊断信息"):
                    st_obj.markdown("### 可能的问题原因：")

                    # 添加目标列范围诊断
                    if target_columns:
                        st_obj.markdown(f"**目标列范围检查：**")
                        st_obj.markdown(f"- 目标列总数: {len(target_columns)}")
                        st_obj.markdown(f"- 前5个目标列: {target_columns[:5]}")

                        # 检查目标列在宏观数据中的存在情况
                        available_in_macro = [col for col in target_columns if col in df.columns]
                        st_obj.markdown(f"- 在宏观数据中找到的目标列数: {len(available_in_macro)}")
                        if len(available_in_macro) > 0:
                            st_obj.markdown(f"- 前5个匹配的目标列: {available_in_macro[:5]}")
                        else:
                            st_obj.error("❌ 没有找到任何匹配的目标列！")
                    else:
                        st_obj.error("❌ 目标列范围为空！")

                        # 显示实际的数据列名以帮助诊断
                        st_obj.markdown("**数据列名诊断：**")
                        column_names = df.columns.tolist()
                        st_obj.markdown(f"- 数据总列数: {len(column_names)}")
                        st_obj.markdown(f"- 前10个列名: {column_names[:10]}")

                        if len(column_names) > 1:
                            potential_targets = column_names[1:]  # 第二列到最后一列
                            potential_targets = [col for col in potential_targets if pd.notna(col)]
                            st_obj.markdown(f"- 潜在目标列数(第2列到最后): {len(potential_targets)}")
                            if potential_targets:
                                st_obj.markdown(f"- 前5个潜在目标列: {potential_targets[:5]}")

                                # 寻找包含工业增加值的列
                                industrial_columns = [col for col in potential_targets if '工业增加值' in str(col) and '当月同比' in str(col)]
                                st_obj.markdown(f"- 工业增加值当月同比列数: {len(industrial_columns)}")
                                if industrial_columns:
                                    st_obj.markdown(f"- 前5个工业增加值列: {industrial_columns[:5]}")
                                else:
                                    st_obj.warning("⚠️ 在潜在目标列中未找到工业增加值当月同比数据")
                            else:
                                st_obj.warning("⚠️ 第2列到最后一列都是空值")
                        else:
                            st_obj.error("❌ 数据只有1列或更少，无法确定目标列范围")

                        # 显示实际的数据列名以帮助诊断
                        st_obj.markdown("**数据列名诊断：**")
                        column_names = df.columns.tolist()
                        st_obj.markdown(f"- 数据总列数: {len(column_names)}")
                        st_obj.markdown(f"- 前10个列名: {column_names[:10]}")

                        # 寻找包含工业增加值的列
                        industrial_columns = [col for col in column_names if '工业增加值' in str(col) and '当月同比' in str(col)]
                        st_obj.markdown(f"- 工业增加值当月同比列数: {len(industrial_columns)}")
                        if industrial_columns:
                            st_obj.markdown(f"- 前5个工业增加值列: {industrial_columns[:5]}")

                        # 检查是否存在预期的起始和结束列
                        start_col_found = any('规模以上工业增加值:电气机械和器材制造业:当月同比' in str(col) for col in column_names)
                        end_col_found = any('规模以上工业增加值:专用设备制造业:当月同比' in str(col) for col in column_names)

                        st_obj.markdown(f"- 找到起始列(电气机械和器材制造业): {'✅' if start_col_found else '❌'}")
                        st_obj.markdown(f"- 找到结束列(专用设备制造业): {'✅' if end_col_found else '❌'}")

                        if not start_col_found or not end_col_found:
                            st_obj.warning("⚠️ 未找到预期的起始或结束列名，请检查数据格式是否正确")

                    # 检查权重数据格式
                    if df_weights is not None and not df_weights.empty:
                        st_obj.markdown("**权重数据状态：** ✅ 已加载")
                        st_obj.markdown(f"**权重数据形状：** {df_weights.shape}")
                        st_obj.markdown(f"**权重数据列：** {list(df_weights.columns)}")

                        required_cols = ['指标名称', '出口依赖', '上中下游']
                        weight_year_columns = ['权重_2012', '权重_2018', '权重_2020']

                        missing_cols = [col for col in required_cols if col not in df_weights.columns]
                        available_weight_cols = [col for col in weight_year_columns if col in df_weights.columns]

                        if missing_cols:
                            st_obj.error(f"❌ 权重数据缺少必要列: {missing_cols}")
                        elif not available_weight_cols:
                            st_obj.error(f"❌ 权重数据缺少权重列，需要以下任一列: {weight_year_columns}")
                        else:
                            st_obj.success("✅ 权重数据包含所有必要列")
                            st_obj.info(f"✅ 找到权重列: {available_weight_cols}")

                            # 检查数据内容
                            indicator_count = df_weights['指标名称'].notna().sum()
                            st_obj.info(f"权重数据中有效指标数量: {indicator_count}")

                            if indicator_count > 0:
                                # 显示前几个指标名称
                                sample_indicators = df_weights['指标名称'].dropna().head(3).tolist()
                                st_obj.markdown(f"**示例指标名称：** {sample_indicators}")

                                # 检查指标名称匹配情况
                                if target_columns:
                                    available_in_macro = [col for col in target_columns if col in df.columns]
                                    weight_indicators = df_weights['指标名称'].dropna().tolist()
                                    matched_indicators = [ind for ind in weight_indicators if ind in available_in_macro]

                                    st_obj.markdown(f"**指标匹配检查：**")
                                    st_obj.markdown(f"- 权重数据中的指标数: {len(weight_indicators)}")
                                    st_obj.markdown(f"- 宏观数据中的目标列数: {len(available_in_macro)}")
                                    st_obj.markdown(f"- 匹配的指标数: {len(matched_indicators)}")

                                    if len(matched_indicators) == 0:
                                        st_obj.error("❌ 权重数据中的指标名称与宏观数据列名完全不匹配！")
                                        st_obj.markdown("**权重数据前5个指标：**")
                                        for i, ind in enumerate(weight_indicators[:5]):
                                            st_obj.text(f"{i+1}. {ind}")
                                        st_obj.markdown("**宏观数据前5个目标列：**")
                                        for i, col in enumerate(available_in_macro[:5]):
                                            st_obj.text(f"{i+1}. {col}")
                                    else:
                                        st_obj.success(f"✅ 找到 {len(matched_indicators)} 个匹配指标")
                                        st_obj.markdown(f"**前5个匹配指标：** {matched_indicators[:5]}")
                    else:
                        st_obj.error("❌ 权重数据未加载或为空")

                    # 检查目标列匹配（这部分已经在上面处理了，这里不重复）

                    st_obj.markdown("### 数据格式要求：")
                    st_obj.markdown("""
                    **权重数据表（工作表名：'工业增加值分行业指标权重'）应包含以下列：**
                    - `指标名称`: 与分行业工业增加值同比增速数据中的列名完全匹配
                    - `出口依赖`: 分类标签（如：高出口依赖、低出口依赖）
                    - `上中下游`: 分类标签（如：上游行业、中游行业、下游行业）
                    - `权重_2012`: 2012年权重值（用于2012-2017年数据）
                    - `权重_2018`: 2018年权重值（用于2018-2019年数据）
                    - `权重_2020`: 2020年权重值（用于2020年及以后数据）

                    注：至少需要包含上述权重列中的一列

                    **分行业工业增加值同比增速数据应包含：**
                    - 从"规模以上工业增加值:电气机械和器材制造业:当月同比"到"规模以上工业增加值:专用设备制造业:当月同比"的列
                    """)
        else:
            st_obj.error("未找到目标列范围，请检查Excel文件格式。")
    else:
        st_obj.error("数据加载失败，无法进行分行业工业增加值同比增速分析。")


def render_macro_operations_tab(st_obj):
    """
    原始的分行业工业增加值同比增速分析标签页（保持向后兼容）

    Args:
        st_obj: Streamlit对象
    """
    # File upload in sidebar
    with st_obj.sidebar:
        st_obj.markdown("#### 📁 数据文件上传")
        uploaded_file = st_obj.file_uploader(
            "上传监测分析Excel模板文件",
            type=['xlsx', 'xls'],
            key="macro_operations_file_uploader",
            help="请上传包含'分行业工业增加值同比增速'工作表的Excel文件"
        )

    if uploaded_file is not None:
        # Load and process the data
        with st_obj.spinner("正在处理数据..."):
            df = load_template_data(uploaded_file)
            df_weights = load_weights_data(uploaded_file)

        if df is not None and df_weights is not None:
            _render_macro_operations_analysis(st_obj, df, df_weights)
        else:
            if df is None:
                st_obj.error("分行业工业增加值同比增速数据加载失败，请检查文件格式。")
            if df_weights is None:
                st_obj.error("权重数据加载失败，请检查文件是否包含'工业增加值分行业指标权重'工作表。")
    else:
        st_obj.info("请在左侧上传Excel数据文件以开始分行业工业增加值同比增速分析。")

