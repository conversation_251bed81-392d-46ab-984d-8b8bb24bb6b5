# -*- coding: utf-8 -*-
"""
状态迁移工具
自动检测和迁移现有session_state数据到unified_manager
"""

import logging
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
from dataclasses import dataclass

from .key_standards import get_key_standards
from .interface import get_state_interface


@dataclass
class MigrationResult:
    """迁移结果数据类"""
    success: bool
    old_key: str
    new_key: str
    value_type: str
    error_message: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class StateMigrationTool:
    """状态迁移工具"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.key_standards = get_key_standards()
        self.state_interface = get_state_interface()
        
        # 迁移状态跟踪
        self.migration_results: List[MigrationResult] = []
        self.backup_data: Dict[str, Any] = {}
        self.migration_start_time: Optional[datetime] = None
        self.migration_end_time: Optional[datetime] = None
        
        # 迁移配置
        self.config = {
            'dry_run': False,
            'backup_enabled': True,
            'validate_after_migration': True,
            'rollback_on_error': True,
            'batch_size': 100,
            'max_retries': 3
        }
    
    def scan_session_state_keys(self) -> List[str]:
        """
        扫描当前session_state中的所有键
        
        Returns:
            session_state中的键列表
        """
        try:
            import streamlit as st
            if hasattr(st, 'session_state'):
                return list(st.session_state.keys())
            return []
        except Exception as e:
            self.logger.error(f"Failed to scan session_state keys: {e}")
            return []
    
    def analyze_migration_needs(self) -> Dict[str, Any]:
        """
        分析迁移需求
        
        Returns:
            迁移分析报告
        """
        session_keys = self.scan_session_state_keys()
        unified_keys = self.state_interface.get_all_keys()
        
        # 分析键的分布
        analysis = {
            'total_session_keys': len(session_keys),
            'total_unified_keys': len(unified_keys),
            'keys_needing_migration': [],
            'keys_already_migrated': [],
            'keys_with_conflicts': [],
            'module_distribution': {},
            'migration_complexity': 'low'
        }
        
        # 检查每个session_state键
        for key in session_keys:
            # 检查是否已经在unified_manager中
            if key in unified_keys:
                analysis['keys_already_migrated'].append(key)
                continue
            
            # 检查是否有标准化映射
            normalized_key = self.key_standards.normalize_key(key)
            if normalized_key != key:
                if normalized_key in unified_keys:
                    analysis['keys_with_conflicts'].append({
                        'old_key': key,
                        'new_key': normalized_key,
                        'conflict_type': 'already_exists'
                    })
                else:
                    analysis['keys_needing_migration'].append({
                        'old_key': key,
                        'new_key': normalized_key,
                        'module': self.key_standards.get_module_from_key(normalized_key)
                    })
            else:
                analysis['keys_needing_migration'].append({
                    'old_key': key,
                    'new_key': key,
                    'module': 'unknown'
                })
        
        # 统计模块分布
        for item in analysis['keys_needing_migration']:
            module = item.get('module', 'unknown')
            analysis['module_distribution'][module] = analysis['module_distribution'].get(module, 0) + 1
        
        # 评估迁移复杂度
        if len(analysis['keys_needing_migration']) > 50:
            analysis['migration_complexity'] = 'high'
        elif len(analysis['keys_needing_migration']) > 20:
            analysis['migration_complexity'] = 'medium'
        
        return analysis
    
    def create_backup(self) -> bool:
        """
        创建session_state的备份
        
        Returns:
            是否备份成功
        """
        if not self.config['backup_enabled']:
            return True
        
        try:
            import streamlit as st
            if hasattr(st, 'session_state'):
                # 创建深拷贝备份
                self.backup_data = {}
                for key, value in st.session_state.items():
                    try:
                        # 尝试序列化以确保可以备份
                        json.dumps(value, default=str)
                        self.backup_data[key] = value
                    except (TypeError, ValueError):
                        # 对于不能序列化的对象，存储其类型信息
                        self.backup_data[key] = {
                            '_type': str(type(value)),
                            '_repr': repr(value)[:100],
                            '_backup_failed': True
                        }
                
                self.logger.info(f"Created backup of {len(self.backup_data)} session_state keys")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
            return False
    
    def migrate_single_key(self, old_key: str, new_key: str, value: Any) -> MigrationResult:
        """
        迁移单个键
        
        Args:
            old_key: 原始键名
            new_key: 新键名
            value: 值
            
        Returns:
            迁移结果
        """
        try:
            # 验证新键
            is_valid, errors = self.key_standards.validate_key(new_key)
            if not is_valid:
                return MigrationResult(
                    success=False,
                    old_key=old_key,
                    new_key=new_key,
                    value_type=str(type(value)),
                    error_message=f"Invalid new key: {errors}"
                )
            
            # 执行迁移
            if not self.config['dry_run']:
                success = self.state_interface.set_state(new_key, value, is_initialization=True)
                if not success:
                    return MigrationResult(
                        success=False,
                        old_key=old_key,
                        new_key=new_key,
                        value_type=str(type(value)),
                        error_message="Failed to set state in unified manager"
                    )
            
            return MigrationResult(
                success=True,
                old_key=old_key,
                new_key=new_key,
                value_type=str(type(value))
            )
            
        except Exception as e:
            return MigrationResult(
                success=False,
                old_key=old_key,
                new_key=new_key,
                value_type=str(type(value)),
                error_message=str(e)
            )
    
    def migrate_all_keys(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        迁移所有需要迁移的键
        
        Args:
            dry_run: 是否为试运行
            
        Returns:
            迁移报告
        """
        self.config['dry_run'] = dry_run
        self.migration_start_time = datetime.now()
        self.migration_results.clear()
        
        try:
            # 创建备份
            if not self.create_backup():
                return {
                    'success': False,
                    'error': 'Failed to create backup',
                    'results': []
                }
            
            # 分析迁移需求
            analysis = self.analyze_migration_needs()
            keys_to_migrate = analysis['keys_needing_migration']
            
            self.logger.info(f"Starting migration of {len(keys_to_migrate)} keys (dry_run={dry_run})")
            
            # 获取session_state数据
            import streamlit as st
            session_state = st.session_state if hasattr(st, 'session_state') else {}
            
            # 批量迁移
            successful_migrations = 0
            failed_migrations = 0
            
            for i, migration_item in enumerate(keys_to_migrate):
                old_key = migration_item['old_key']
                new_key = migration_item['new_key']
                
                if old_key in session_state:
                    value = session_state[old_key]
                    result = self.migrate_single_key(old_key, new_key, value)
                    self.migration_results.append(result)
                    
                    if result.success:
                        successful_migrations += 1
                    else:
                        failed_migrations += 1
                        self.logger.error(f"Migration failed for {old_key}: {result.error_message}")
                
                # 批量处理进度报告
                if (i + 1) % self.config['batch_size'] == 0:
                    self.logger.info(f"Processed {i + 1}/{len(keys_to_migrate)} keys")
            
            self.migration_end_time = datetime.now()
            
            # 验证迁移结果
            validation_results = {}
            if self.config['validate_after_migration'] and not dry_run:
                validation_results = self.validate_migration()
            
            # 生成迁移报告
            report = {
                'success': failed_migrations == 0,
                'dry_run': dry_run,
                'total_keys': len(keys_to_migrate),
                'successful_migrations': successful_migrations,
                'failed_migrations': failed_migrations,
                'migration_time': (self.migration_end_time - self.migration_start_time).total_seconds(),
                'validation_results': validation_results,
                'results': [
                    {
                        'old_key': r.old_key,
                        'new_key': r.new_key,
                        'success': r.success,
                        'error': r.error_message
                    }
                    for r in self.migration_results
                ]
            }
            
            self.logger.info(f"Migration completed: {successful_migrations} successful, {failed_migrations} failed")
            return report
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': []
            }
    
    def validate_migration(self) -> Dict[str, Any]:
        """
        验证迁移结果
        
        Returns:
            验证报告
        """
        validation_results = {
            'data_consistency': True,
            'key_mapping_correct': True,
            'value_integrity': True,
            'issues': []
        }
        
        try:
            import streamlit as st
            session_state = st.session_state if hasattr(st, 'session_state') else {}
            
            for result in self.migration_results:
                if not result.success:
                    continue
                
                old_key = result.old_key
                new_key = result.new_key
                
                # 检查数据一致性
                if old_key in session_state:
                    old_value = session_state[old_key]
                    new_value = self.state_interface.get_state(new_key)
                    
                    if old_value != new_value:
                        validation_results['data_consistency'] = False
                        validation_results['issues'].append(
                            f"Data mismatch for {old_key} -> {new_key}"
                        )
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Validation failed: {e}")
            validation_results['data_consistency'] = False
            validation_results['issues'].append(f"Validation error: {e}")
            return validation_results
    
    def rollback_migration(self) -> bool:
        """
        回滚迁移
        
        Returns:
            是否回滚成功
        """
        if not self.backup_data:
            self.logger.error("No backup data available for rollback")
            return False
        
        try:
            import streamlit as st
            if not hasattr(st, 'session_state'):
                return False
            
            # 清除迁移的键
            for result in self.migration_results:
                if result.success:
                    self.state_interface.clear_state(result.new_key)
            
            # 恢复备份数据 - 只使用统一状态管理器
            for key, value in self.backup_data.items():
                if not isinstance(value, dict) or not value.get('_backup_failed', False):
                    try:
                        # 只使用统一状态管理器
                        if self.state_interface:
                            success = self.state_interface.set_state(key, value)
                            if not success:
                                self.logger.error(f"Failed to restore {key} via state manager")
                        else:
                            self.logger.error(f"State interface not available for restoring {key}")
                    except Exception as e:
                        self.logger.error(f"Failed to restore {key}: {e}")
            
            self.logger.info("Migration rollback completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return False
    
    def get_migration_statistics(self) -> Dict[str, Any]:
        """获取迁移统计信息"""
        if not self.migration_results:
            return {'no_migration_data': True}
        
        successful = sum(1 for r in self.migration_results if r.success)
        failed = len(self.migration_results) - successful
        
        return {
            'total_migrations': len(self.migration_results),
            'successful_migrations': successful,
            'failed_migrations': failed,
            'success_rate': successful / len(self.migration_results) if self.migration_results else 0,
            'migration_start_time': self.migration_start_time,
            'migration_end_time': self.migration_end_time,
            'migration_duration': (
                (self.migration_end_time - self.migration_start_time).total_seconds()
                if self.migration_start_time and self.migration_end_time else None
            ),
            'backup_size': len(self.backup_data)
        }


# 全局实例
_migration_tool = None

def get_migration_tool() -> StateMigrationTool:
    """获取状态迁移工具实例"""
    global _migration_tool
    if _migration_tool is None:
        _migration_tool = StateMigrationTool()
    return _migration_tool


# 便捷函数
def analyze_migration_needs() -> Dict[str, Any]:
    """便捷函数：分析迁移需求"""
    return get_migration_tool().analyze_migration_needs()


def migrate_session_state(dry_run: bool = True) -> Dict[str, Any]:
    """便捷函数：迁移session_state"""
    return get_migration_tool().migrate_all_keys(dry_run=dry_run)


__all__ = [
    'MigrationResult',
    'StateMigrationTool',
    'get_migration_tool',
    'analyze_migration_needs',
    'migrate_session_state'
]
