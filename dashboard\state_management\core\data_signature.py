# -*- coding: utf-8 -*-
"""
数据签名和变化检测
提供高效的数据变化检测功能
"""

import hashlib
import pandas as pd
import numpy as np
from typing import Any, Dict, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class DataSignature:
    """数据签名，用于变化检测"""
    content_hash: str
    shape_info: str
    timestamp: datetime
    version: int = 1
    
    def __eq__(self, other):
        """比较两个签名是否相等"""
        if not isinstance(other, DataSignature):
            return False
        return (self.content_hash == other.content_hash and 
                self.shape_info == other.shape_info)
    
    def __hash__(self):
        """计算签名的哈希值"""
        return hash((self.content_hash, self.shape_info))

class DataSignatureCalculator:
    """数据签名计算器"""
    
    @staticmethod
    def calculate_dataframe_signature(df: pd.DataFrame) -> DataSignature:
        """计算DataFrame签名"""
        if df.empty:
            return DataSignature(
                content_hash="empty_dataframe",
                shape_info="0x0",
                timestamp=datetime.now()
            )
        
        # 基础形状信息
        shape_info = f"{df.shape[0]}x{df.shape[1]}"
        
        # 列信息
        cols_str = "_".join(str(col) for col in df.columns[:20])  # 限制列数避免过长
        dtypes_str = "_".join(str(dtype) for dtype in df.dtypes[:20])
        
        # 采样策略：取前5行、后5行和中间5行
        sample_parts = []
        
        # 前5行
        head_sample = df.head(5)
        sample_parts.append(head_sample.to_string())
        
        # 后5行（如果数据足够大）
        if len(df) > 10:
            tail_sample = df.tail(5)
            sample_parts.append(tail_sample.to_string())
        
        # 中间5行（如果数据足够大）
        if len(df) > 20:
            mid_idx = len(df) // 2
            mid_sample = df.iloc[max(0, mid_idx-2):mid_idx+3]
            sample_parts.append(mid_sample.to_string())
        
        # 组合所有信息
        content_str = f"{cols_str}_{dtypes_str}_{'_'.join(sample_parts)}"
        content_hash = hashlib.md5(content_str.encode()).hexdigest()[:16]
        
        return DataSignature(
            content_hash=content_hash,
            shape_info=shape_info,
            timestamp=datetime.now()
        )
    
    @staticmethod
    def calculate_dict_signature(data: dict) -> DataSignature:
        """计算字典签名"""
        if not data:
            return DataSignature(
                content_hash="empty_dict",
                shape_info="dict_0",
                timestamp=datetime.now()
            )
        
        # 键信息
        keys_str = "_".join(sorted(str(k) for k in data.keys())[:50])  # 限制键数量
        
        # 值采样
        values_sample = []
        for i, (k, v) in enumerate(data.items()):
            if i >= 10:  # 只采样前10个值
                break
            if isinstance(v, (str, int, float, bool)):
                values_sample.append(str(v)[:100])  # 限制值长度
            elif isinstance(v, pd.DataFrame):
                values_sample.append(f"df_{v.shape[0]}x{v.shape[1]}")
            else:
                values_sample.append(f"{type(v).__name__}")
        
        values_str = "_".join(values_sample)
        content_str = f"{keys_str}_{values_str}"
        content_hash = hashlib.md5(content_str.encode()).hexdigest()[:16]
        
        return DataSignature(
            content_hash=content_hash,
            shape_info=f"dict_{len(data)}",
            timestamp=datetime.now()
        )
    
    @staticmethod
    def calculate_list_signature(data: list) -> DataSignature:
        """计算列表签名"""
        if not data:
            return DataSignature(
                content_hash="empty_list",
                shape_info="list_0",
                timestamp=datetime.now()
            )
        
        # 采样前10个元素
        sample_items = []
        for i, item in enumerate(data[:10]):
            if isinstance(item, (str, int, float, bool)):
                sample_items.append(str(item)[:50])  # 限制长度
            else:
                sample_items.append(f"{type(item).__name__}")
        
        content_str = "_".join(sample_items)
        content_hash = hashlib.md5(content_str.encode()).hexdigest()[:16]
        
        return DataSignature(
            content_hash=content_hash,
            shape_info=f"list_{len(data)}",
            timestamp=datetime.now()
        )
    
    @staticmethod
    def calculate_scalar_signature(data: Any) -> DataSignature:
        """计算标量签名"""
        content_str = str(data)[:1000]  # 限制长度
        content_hash = hashlib.md5(content_str.encode()).hexdigest()[:16]
        
        return DataSignature(
            content_hash=content_hash,
            shape_info=f"{type(data).__name__}",
            timestamp=datetime.now()
        )
    
    @classmethod
    def calculate_signature(cls, data: Any) -> DataSignature:
        """计算任意数据的签名"""
        if isinstance(data, pd.DataFrame):
            return cls.calculate_dataframe_signature(data)
        elif isinstance(data, dict):
            return cls.calculate_dict_signature(data)
        elif isinstance(data, list):
            return cls.calculate_list_signature(data)
        else:
            return cls.calculate_scalar_signature(data)

class ChangeDetector:
    """变化检测器"""

    def __init__(self):
        self._signatures: Dict[str, DataSignature] = {}
        self._change_count = 0
        self._no_change_count = 0
        self._initialized_keys: set = set()  # 跟踪已初始化的键

    def silent_initialize(self, key: str, data: Any) -> bool:
        """静默初始化键值，不算作变化"""
        current_signature = DataSignatureCalculator.calculate_signature(data)
        self._signatures[key] = current_signature
        self._initialized_keys.add(key)
        return True

    def has_changed(self, key: str, data: Any, is_initialization: bool = False) -> bool:
        """检查数据是否发生变化"""
        current_signature = DataSignatureCalculator.calculate_signature(data)
        previous_signature = self._signatures.get(key)

        if previous_signature is None:
            # 第一次设置
            self._signatures[key] = current_signature
            if is_initialization:
                # 初始化模式：不算作变化，避免触发重新运行
                self._initialized_keys.add(key)
                return False
            else:
                # 正常模式：算作变化
                self._change_count += 1
                return True

        if current_signature != previous_signature:
            # 数据发生变化
            current_signature.version = previous_signature.version + 1
            self._signatures[key] = current_signature
            self._change_count += 1
            return True
        else:
            # 数据未变化
            self._no_change_count += 1
            return False

    def is_initialized(self, key: str) -> bool:
        """检查键是否已初始化"""
        return key in self._initialized_keys

    def get_signature(self, key: str) -> Optional[DataSignature]:
        """获取指定键的签名"""
        return self._signatures.get(key)
    
    def remove_signature(self, key: str) -> bool:
        """移除指定键的签名"""
        if key in self._signatures:
            del self._signatures[key]
            return True
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取变化检测统计信息"""
        total_checks = self._change_count + self._no_change_count
        change_rate = self._change_count / total_checks if total_checks > 0 else 0
        
        return {
            'total_signatures': len(self._signatures),
            'initialized_keys': len(self._initialized_keys),
            'total_checks': total_checks,
            'changes_detected': self._change_count,
            'no_changes_detected': self._no_change_count,
            'change_rate': change_rate
        }
    
    def clear_all_signatures(self):
        """清除所有签名"""
        self._signatures.clear()
        self._change_count = 0
        self._no_change_count = 0

# 便捷函数
def calculate_data_signature(data: Any) -> DataSignature:
    """计算数据签名的便捷函数"""
    return DataSignatureCalculator.calculate_signature(data)

def has_data_changed(key: str, data: Any, detector: ChangeDetector) -> bool:
    """检查数据是否变化的便捷函数"""
    return detector.has_changed(key, data)
