# -*- coding: utf-8 -*-
"""
Tools模块重构适配器
将Tools模块迁移到统一状态管理系统
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from .module_refactor_base import ModuleRefactorBase
from ..unified_state_manager import UnifiedStateManager


class ToolsModuleRefactor(ModuleRefactorBase):
    """Tools模块重构适配器"""
    
    def __init__(self, unified_manager: UnifiedStateManager = None):
        # 先初始化Tools特定的状态键映射
        self.tools_state_mapping = {
            # time_series_pretreat子模块
            'time_series_pretreat': {
                'uploaded_file': 'uploaded_file',
                'processed_data': 'processed_data',
                'processing_steps': 'processing_steps',
                'current_step': 'current_step',
                'progress': 'progress',
                'results': 'results',
                'parameters': 'parameters',
                'cleaning_config': 'cleaning_config',
                'transformation_config': 'transformation_config'
            },
            # time_series_property子模块
            'time_series_property': {
                'input_data': 'input_data',
                'analysis_results': 'analysis_results',
                'selected_columns': 'selected_columns',
                'analysis_type': 'analysis_type',
                'parameters': 'parameters',
                'charts': 'charts',
                'statistics': 'statistics',
                'correlation_results': 'correlation_results',
                'stationarity_results': 'stationarity_results',
                'dtw_results': 'dtw_results'
            },
            # 时间滞后相关性分析UI状态
            'time_lag_correlation': {
                'processed_flag': 'tlc_processed_flag',
                'lagged_variable': 'tlc_lagged_variable',
                'leading_variables': 'tlc_leading_variables',
                'max_periods': 'tlc_max_periods',
                'batch_results': 'tlc_batch_results',
                'selected_for_plot': 'tlc_selected_for_plot',
                'calc_triggered': 'tlc_calc_triggered',
                'lagged_var_tracker': 'tlc_lagged_var_tracker'
            },
            # 胜率分析UI状态
            'win_rate_analysis': {
                'target_series': 'win_rate_target_series',
                'ref_series_list': 'win_rate_ref_series_list',
                'time_ranges': 'win_rate_time_ranges',
                'results': 'win_rate_results',
                'dt_conversion_done': 'win_rate_dt_conversion_done',
                'dt_index_available': 'win_rate_dt_index_available',
                'processed_df': 'win_rate_processed_df'
            }
        }
        
        # 工具类型映射
        self.tool_type_mapping = {
            'time_series_pretreat': 'time_series_pretreat',
            'time_series_property': 'time_series_property',
            'correlation_analysis': 'statistical_analysis',
            'stationarity_test': 'statistical_analysis',
            'dtw_analysis': 'data_analysis'
        }

        # 调用父类初始化
        super().__init__("tools", unified_manager)
    
    def _initialize_module(self):
        """初始化Tools模块 - 优化版本，避免重复初始化"""
        import time
        import os
        import threading

        # 添加进程级别和时间戳检查
        current_time = time.time()
        process_id = os.getpid()
        thread_id = threading.get_ident()

        # 创建唯一的初始化标识
        init_key = f"_module_initialized_{process_id}_{thread_id}"
        init_time_key = f"_module_init_time_{process_id}_{thread_id}"

        # 使用统一状态管理器检查初始化状态
        if self.unified_manager:
            state_init_key = f"tools.module_initialized.{process_id}.{thread_id}"
            state_init_time_key = f"tools.module_init_time.{process_id}.{thread_id}"

            last_init_time = self.unified_manager.get_state(state_init_time_key, 0)
            if (self.unified_manager.get_state(state_init_key, False) and
                current_time - last_init_time < 300):
                self.log_debug(f"Tools module already initialized in state manager (PID: {process_id}, TID: {thread_id})")
                # 同步本地标记
                setattr(self, init_key, True)
                setattr(self, init_time_key, last_init_time)
                self._module_initialized = True
                return

        # 检查本地初始化状态（备用检查）
        if (hasattr(self, init_key) and getattr(self, init_key) and
            hasattr(self, init_time_key) and
            current_time - getattr(self, init_time_key) < 300):  # 5分钟缓存
            self.log_debug(f"Tools module already initialized locally (PID: {process_id}, TID: {thread_id})")
            return

        self.log_info(f"Initializing Tools module refactor (PID: {process_id}, TID: {thread_id})")

        # 注册Tools导航项
        self._setup_navigation()

        # 初始化默认状态
        self._setup_default_states()

        # 标记为已初始化（本地和统一状态管理器）
        setattr(self, init_key, True)
        setattr(self, init_time_key, current_time)
        self._module_initialized = True

        # 保存到统一状态管理器
        if self.unified_manager:
            self.unified_manager.set_state(f"tools.module_initialized.{process_id}.{thread_id}", True)
            self.unified_manager.set_state(f"tools.module_init_time.{process_id}.{thread_id}", current_time)

        self.log_info(f"Tools module refactor initialization completed (PID: {process_id}, TID: {thread_id})")
    
    def _setup_navigation(self):
        """设置Tools模块导航"""
        # 添加主Tools导航项
        self.add_navigation_item(
            item_id="tools",
            label="数据工具",
            path="/tools",
            icon="🔧",
            order=2
        )
        
        # 添加子模块导航项
        submodules = [
            ("tools_pretreat", "时间序列预处理", "/tools/pretreat", "🧹"),
            ("tools_property", "时间序列属性", "/tools/property", "📊"),
            ("tools_correlation", "相关性分析", "/tools/correlation", "🔗"),
            ("tools_stationarity", "平稳性检验", "/tools/stationarity", "📈"),
            ("tools_dtw", "DTW分析", "/tools/dtw", "🌊")
        ]
        
        for i, (item_id, label, path, icon) in enumerate(submodules):
            self.add_navigation_item(
                item_id=item_id,
                label=label,
                path=path,
                icon=icon,
                parent_id="tools",
                order=i + 1
            )
    
    def _setup_default_states(self):
        """设置默认状态"""
        # 设置默认的工具配置
        default_configs = {
            'pretreat_config': {
                'remove_outliers': True,
                'fill_missing': True,
                'standardize': False,
                'detrend': False
            },
            'property_config': {
                'confidence_level': 0.95,
                'max_lags': 20,
                'test_type': 'adf'
            }
        }
        
        for config_key, config_value in default_configs.items():
            if not self.get_state(config_key):
                self.set_state(config_key, config_value)
    
    def _get_from_unified_manager(self, key: str, default: Any = None) -> Any:
        """从统一状态管理器获取Tools状态"""
        if self.unified_manager:
            try:
                # 尝试多个可能的键格式，使用统一状态管理器而不是直接访问session_state
                for possible_key in [f"tools.{key}", key, f"tools_{key}"]:
                    result = self.unified_manager.get_state(possible_key, None)
                    if result is not None:
                        return result
                return default
            except Exception as e:
                self.log_warning(f"Failed to get state from unified manager for key {key}: {e}")
                return default
        return default
    
    def _set_to_unified_manager(self, key: str, value: Any) -> bool:
        """设置Tools状态到统一状态管理器"""
        if self.unified_manager:
            try:
                # 使用统一状态管理器设置状态，保持命名空间
                self.unified_manager.set_state(f"tools.{key}", value)
                # 为兼容性保留无前缀的键
                self.unified_manager.set_state(key, value)
                return True
            except Exception as e:
                self.log_warning(f"Failed to set state to unified manager for key {key}: {e}")
                return False
        return False
    
    def _delete_from_unified_manager(self, key: str) -> bool:
        """从统一状态管理器删除Tools状态"""
        if not self.unified_manager:
            return False

        # 尝试从Tools命名空间删除
        if hasattr(self.unified_manager, 'delete_tools_state'):
            return self.unified_manager.delete_tools_state(key)

        # 回退到全局状态删除
        if hasattr(self.unified_manager, 'delete_state'):
            return self.unified_manager.delete_state(f"tools.{key}")

        return True  # 假设删除成功

    def _clear_unified_manager_state(self):
        """清空统一状态管理器中的Tools状态"""
        if not self.unified_manager:
            return

        # 尝试清空Tools命名空间
        if hasattr(self.unified_manager, 'clear_tools_states'):
            self.unified_manager.clear_tools_states()
        elif hasattr(self.unified_manager, 'clear_module_states'):
            self.unified_manager.clear_module_states('tools')
    
    def _determine_tool_type(self, key: str) -> str:
        """根据键确定工具类型"""
        # 检查键是否属于特定的工具类型
        for tool_type, keys in self.tools_state_mapping.items():
            if key in keys.values():
                return tool_type
        
        # 默认返回通用类型
        return 'common'
    
    # Tools特定的状态管理方法
    def get_tools_state(self, tool_type: str, key: str, default: Any = None) -> Any:
        """获取工具状态"""
        if self.unified_manager and hasattr(self.unified_manager, 'get_tools_state'):
            return self.unified_manager.get_tools_state(tool_type, key, default)

        # 回退到本地状态
        full_key = f"{tool_type}.{key}"
        return self.get_state(full_key, default)

    def set_tools_state(self, tool_type: str, key: str, value: Any) -> bool:
        """设置工具状态"""
        if self.unified_manager and hasattr(self.unified_manager, 'set_tools_state'):
            return self.unified_manager.set_tools_state(tool_type, key, value)

        # 回退到本地状态
        full_key = f"{tool_type}.{key}"
        return self.set_state(full_key, value)

    def delete_tools_state(self, tool_type: str, key: str) -> bool:
        """删除工具状态"""
        if self.unified_manager and hasattr(self.unified_manager, 'clear_state'):
            # 使用统一状态管理器的clear_state方法删除状态
            full_key = f"tools.{tool_type}.{key}"
            return self.unified_manager.clear_state(full_key)

        # 回退到本地状态删除
        full_key = f"{tool_type}.{key}"
        return self.delete_state(full_key)

    def get_tool_state(self, tool_type: str, key: str, default: Any = None) -> Any:
        """获取工具状态（兼容性方法）"""
        return self.get_tools_state(tool_type, key, default)
    
    def set_tool_state(self, tool_type: str, key: str, value: Any) -> bool:
        """设置工具状态"""
        if self.unified_manager:
            success = self.unified_manager.set_tools_state(tool_type, key, value)
            if success:
                self.record_metric(f"tools_{tool_type}_state_update", 1)
            return success

        # 回退到本地状态
        full_key = f"{tool_type}.{key}"
        return self.set_state(full_key, value)

    # 时间滞后相关性分析专用方法
    def get_tlc_state(self, dataset_name: str, key: str, default: Any = None) -> Any:
        """获取时间滞后相关性分析状态"""
        full_key = f"tlc_{dataset_name}_{key}_v4"
        return self.get_tools_state('time_lag_correlation', full_key, default)

    def set_tlc_state(self, dataset_name: str, key: str, value: Any) -> bool:
        """设置时间滞后相关性分析状态"""
        full_key = f"tlc_{dataset_name}_{key}_v4"
        return self.set_tools_state('time_lag_correlation', full_key, value)

    def clear_tlc_state(self, dataset_name: str) -> bool:
        """清空指定数据集的时间滞后相关性分析状态"""
        keys_to_clear = [
            'processed_flag', 'lagged_variable', 'leading_variables',
            'max_periods', 'batch_results', 'selected_for_plot',
            'calc_triggered', 'lagged_var_tracker'
        ]

        success = True
        for key in keys_to_clear:
            full_key = f"tlc_{dataset_name}_{key}_v4"
            if not self._delete_from_unified_manager(full_key):
                success = False

        return success

    # 胜率分析专用方法
    def get_win_rate_state(self, dataset_name: str, key: str, default: Any = None) -> Any:
        """获取胜率分析状态"""
        full_key = f"win_rate_{dataset_name}_{key}_v2"
        return self.get_tools_state('win_rate_analysis', full_key, default)

    def set_win_rate_state(self, dataset_name: str, key: str, value: Any) -> bool:
        """设置胜率分析状态"""
        full_key = f"win_rate_{dataset_name}_{key}_v2"
        return self.set_tools_state('win_rate_analysis', full_key, value)

    def clear_win_rate_state(self, dataset_name: str) -> bool:
        """清空指定数据集的胜率分析状态"""
        keys_to_clear = [
            'target_series', 'ref_series_list', 'time_ranges', 'results',
            'dt_conversion_done', 'dt_index_available', 'processed_df'
        ]

        success = True
        for key in keys_to_clear:
            full_key = f"win_rate_{dataset_name}_{key}_v2"
            if not self._delete_from_unified_manager(full_key):
                success = False

        return success
    
    # 数据处理流水线方法
    def create_processing_pipeline(
        self,
        pipeline_name: str,
        tool_type: str,
        steps: List[str],
        parameters: Dict[str, Any] = None
    ) -> str:
        """创建数据处理流水线"""
        if self.unified_manager:
            # 构建步骤配置
            step_configs = []
            for i, step_name in enumerate(steps):
                step_config = {
                    'name': step_name,
                    'tool_type': self.tool_type_mapping.get(tool_type, 'data_analysis'),
                    'parameters': parameters or {}
                }
                step_configs.append(step_config)
            
            pipeline_id = self.unified_manager.create_processing_pipeline(
                pipeline_name,
                step_configs,
                priority="normal",
                cache_strategy="smart"
            )
            
            if pipeline_id:
                self.log_info(f"Created processing pipeline: {pipeline_name}")
                self.record_metric("tools_pipeline_created", 1)
            
            return pipeline_id
        return ""
    
    def start_processing_pipeline(self, pipeline_id: str) -> bool:
        """启动数据处理流水线"""
        if self.unified_manager:
            success = self.unified_manager.start_processing_pipeline(pipeline_id)
            if success:
                self.log_info(f"Started processing pipeline: {pipeline_id}")
                self.record_metric("tools_pipeline_started", 1)
            return success
        return False
    
    # 时间序列预处理方法
    def upload_pretreat_data(self, data: Any, filename: str = None):
        """上传预处理数据"""
        self.set_tool_state('time_series_pretreat', 'uploaded_file', filename)
        self.set_tool_state('time_series_pretreat', 'raw_data', data)
        
        self.log_info(f"Pretreat data uploaded: {filename}")
        self.record_metric("tools_pretreat_data_uploaded", 1)
    
    def run_pretreatment(self, config: Dict[str, Any] = None):
        """运行预处理"""
        timer_id = self.start_timer("pretreatment")
        
        try:
            # 创建预处理流水线
            steps = []
            if config:
                if config.get('remove_outliers'):
                    steps.append('异常值处理')
                if config.get('fill_missing'):
                    steps.append('缺失值填充')
                if config.get('standardize'):
                    steps.append('数据标准化')
                if config.get('detrend'):
                    steps.append('去趋势')
            
            if steps:
                pipeline_id = self.create_processing_pipeline(
                    "时间序列预处理",
                    "time_series_pretreat",
                    steps,
                    config
                )
                
                if pipeline_id:
                    self.start_processing_pipeline(pipeline_id)
                    self.set_tool_state('time_series_pretreat', 'current_pipeline', pipeline_id)
            
            self.log_info("Pretreatment pipeline started")
            
        except Exception as e:
            self.handle_error(e, {'operation': 'pretreatment'})
        finally:
            self.end_timer(timer_id)
    
    def get_pretreat_results(self) -> Optional[Dict[str, Any]]:
        """获取预处理结果"""
        return {
            'processed_data': self.get_tool_state('time_series_pretreat', 'processed_data'),
            'processing_steps': self.get_tool_state('time_series_pretreat', 'processing_steps'),
            'progress': self.get_tool_state('time_series_pretreat', 'progress'),
            'results': self.get_tool_state('time_series_pretreat', 'results')
        }
    
    # 时间序列属性分析方法
    def run_property_analysis(
        self,
        analysis_type: str,
        data: Any = None,
        parameters: Dict[str, Any] = None
    ):
        """运行属性分析"""
        timer_id = self.start_timer(f"property_analysis_{analysis_type}")
        
        try:
            self.set_tool_state('time_series_property', 'analysis_type', analysis_type)
            
            if data is not None:
                self.set_tool_state('time_series_property', 'input_data', data)
            
            # 创建分析流水线
            pipeline_id = self.create_processing_pipeline(
                f"时间序列{analysis_type}分析",
                "time_series_property",
                [f"{analysis_type}分析"],
                parameters
            )
            
            if pipeline_id:
                self.start_processing_pipeline(pipeline_id)
                self.set_tool_state('time_series_property', 'current_pipeline', pipeline_id)
            
            self.log_info(f"Property analysis started: {analysis_type}")
            self.record_metric(f"tools_property_{analysis_type}", 1)
            
        except Exception as e:
            self.handle_error(e, {'operation': 'property_analysis', 'type': analysis_type})
        finally:
            self.end_timer(timer_id)
    
    def get_property_analysis_results(self, analysis_type: str = None) -> Optional[Dict[str, Any]]:
        """获取属性分析结果"""
        results = {
            'analysis_results': self.get_tool_state('time_series_property', 'analysis_results'),
            'charts': self.get_tool_state('time_series_property', 'charts'),
            'statistics': self.get_tool_state('time_series_property', 'statistics')
        }
        
        if analysis_type:
            # 获取特定类型的结果
            specific_results = self.get_tool_state('time_series_property', f'{analysis_type}_results')
            if specific_results:
                results[f'{analysis_type}_results'] = specific_results
        
        return results
    
    # 相关性分析方法
    def run_correlation_analysis(
        self,
        data: Any,
        method: str = 'pearson',
        max_lags: int = 20
    ):
        """运行相关性分析"""
        parameters = {
            'method': method,
            'max_lags': max_lags
        }
        
        self.run_property_analysis('correlation', data, parameters)
    
    def get_correlation_results(self) -> Optional[Dict[str, Any]]:
        """获取相关性分析结果"""
        return self.get_property_analysis_results('correlation')
    
    # 平稳性检验方法
    def run_stationarity_test(
        self,
        data: Any,
        test_type: str = 'adf',
        confidence_level: float = 0.95
    ):
        """运行平稳性检验"""
        parameters = {
            'test_type': test_type,
            'confidence_level': confidence_level
        }
        
        self.run_property_analysis('stationarity', data, parameters)
    
    def get_stationarity_results(self) -> Optional[Dict[str, Any]]:
        """获取平稳性检验结果"""
        return self.get_property_analysis_results('stationarity')
    
    # DTW分析方法
    def run_dtw_analysis(
        self,
        data: Any,
        reference_series: Any = None,
        window_size: int = None
    ):
        """运行DTW分析"""
        parameters = {
            'reference_series': reference_series,
            'window_size': window_size
        }
        
        self.run_property_analysis('dtw', data, parameters)
    
    def get_dtw_results(self) -> Optional[Dict[str, Any]]:
        """获取DTW分析结果"""
        return self.get_property_analysis_results('dtw')
    
    # 兼容性方法
    def migrate_legacy_state(self):
        """迁移旧版本的状态"""
        # 定义旧状态键到新状态键的映射
        legacy_mapping = {
            'tools.uploaded_file': 'uploaded_file',
            'tools.processed_data': 'processed_data',
            'tools.analysis_results': 'analysis_results',
            'tools.correlation_results': 'correlation_results',
            'tools.stationarity_results': 'stationarity_results',
            # 添加更多映射...
        }
        
        self.migrate_session_state(legacy_mapping)
        self.log_info("Legacy state migration completed")
