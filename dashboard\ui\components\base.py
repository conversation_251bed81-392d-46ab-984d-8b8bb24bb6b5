# -*- coding: utf-8 -*-
"""
UI组件基类
提供所有UI组件的基础接口和通用功能
"""

import streamlit as st
from typing import List, Dict, Any, Optional, Callable
from abc import ABC, abstractmethod
import logging
import time

# 导入UI状态适配器
try:
    from ui.utils.state_adapter import get_ui_state_adapter
except ImportError:
    # 如果导入失败，使用简单的替代函数
    def get_ui_state_adapter():
        return None

logger = logging.getLogger(__name__)


class UnifiedBaseComponent:
    """统一状态管理的UI组件基类"""

    def __init__(self, component_name: str = None):
        self.component_name = component_name or self.__class__.__name__
        self.logger = logging.getLogger(f"UI.{self.component_name}")
        # 延迟初始化state_manager以避免循环导入
        self.state_manager = None
        self._state_manager_initialized = False

    def _get_state_manager(self):
        """获取统一状态管理器（延迟初始化）"""
        if not self._state_manager_initialized:
            try:
                # 延迟导入避免循环导入
                from ...state_management import get_unified_manager
                self.state_manager = get_unified_manager()
            except Exception as e:
                # 使用简单的替代方案
                self.state_manager = None
            self._state_manager_initialized = True
        return self.state_manager

    def get_component_state(self, key: str, default=None):
        """获取组件状态"""
        state_manager = self._get_state_manager()
        if state_manager:
            full_key = f"ui.{self.component_name}.{key}"
            return state_manager.get_state(full_key, default)
        else:
            self.logger.warning(f"状态管理器不可用，返回默认值: {key}")
            return default

    def set_component_state(self, key: str, value) -> bool:
        """设置组件状态"""
        state_manager = self._get_state_manager()
        if state_manager:
            full_key = f"ui.{self.component_name}.{key}"
            success = state_manager.set_state(full_key, value)
            if success:
                self.logger.debug(f"状态设置成功: {key}")
            else:
                self.logger.error(f"状态设置失败: {key}")
            return success
        else:
            self.logger.error(f"状态管理器不可用，无法设置: {key}")
            return False

    def clear_component_state(self, key: str = None) -> bool:
        """清理组件状态"""
        state_manager = self._get_state_manager()
        if state_manager:
            if key:
                full_key = f"ui.{self.component_name}.{key}"
                return state_manager.clear_state(full_key)
            else:
                # 清理所有组件状态
                prefix = f"ui.{self.component_name}."
                return state_manager.clear_states_by_prefix(prefix)
        return False

    def get_component_logs(self) -> list:
        """获取组件日志 - 使用统一状态管理"""
        return self.get_component_state('logs', [])

    def add_component_log(self, action: str, details: dict = None, level: str = "INFO"):
        """添加组件日志 - 使用统一状态管理"""
        logs = self.get_component_logs()
        log_entry = {
            'timestamp': time.time(),
            'level': level,
            'action': action,
            'component': self.component_name
        }

        if details:
            log_entry.update(details)

        logs.append(log_entry)

        # 保持最近100条日志
        if len(logs) > 100:
            logs = logs[-100:]

        return self.set_component_state('logs', logs)

    def get_current_time(self) -> float:
        """获取当前时间 - 使用统一状态管理"""
        return self.get_component_state('current_time', time.time())

    def update_current_time(self) -> bool:
        """更新当前时间 - 使用统一状态管理"""
        return self.set_component_state('current_time', time.time())

    def is_healthy(self) -> bool:
        """检查组件是否健康"""
        return self._get_state_manager() is not None

    def get_component_summary(self) -> dict:
        """获取组件状态摘要"""
        state_manager = self._get_state_manager()
        if not state_manager:
            return {'error': '状态管理器不可用'}

        try:
            all_keys = state_manager.get_all_keys()
            component_prefix = f"ui.{self.component_name}."
            component_keys = [key for key in all_keys if str(key).startswith(component_prefix)]

            return {
                'component_name': self.component_name,
                'total_states': len(component_keys),
                'current_time': self.get_current_time(),
                'logs_count': len(self.get_component_logs()),
                'is_healthy': self.is_healthy()
            }
        except Exception as e:
            self.logger.error(f"获取组件摘要失败: {e}")
            return {'error': str(e)}


class UIComponent(UnifiedBaseComponent, ABC):
    """
    UI组件基类 - 集成统一状态管理

    所有UI组件都应该继承此基类，自动获得统一状态管理功能
    """

    def __init__(self, component_name: str = None):
        """初始化UI组件"""
        # 初始化统一基类
        super().__init__(component_name)

        # 基本属性初始化，避免循环导入
        self.state_adapter = None
        self.component_id = component_name or self.__class__.__name__
        self.performance_monitor = None
        self.async_helper = None

        # 标记为未初始化，延迟到真正需要时再初始化这些功能
        self._advanced_features_initialized = False

    def _initialize_advanced_features(self):
        """延迟初始化高级功能，避免循环导入"""
        if self._advanced_features_initialized:
            return

        # 获取UI状态适配器
        try:
            self.state_adapter = get_ui_state_adapter()
            if self.state_adapter:
                self.state_adapter.register_component(self.component_id, self)
        except Exception:
            self.state_adapter = None

        # 初始化性能监控
        try:
            from ui.utils.performance_monitor import get_ui_performance_monitor
            self.performance_monitor = get_ui_performance_monitor()
        except ImportError:
            self.performance_monitor = None

        # 初始化异步处理器
        try:
            from ui.utils.async_processor import get_streamlit_async_helper
            self.async_helper = get_streamlit_async_helper()
        except ImportError:
            self.async_helper = None

        self._advanced_features_initialized = True

        # 初始化异步处理器
        try:
            from ui.utils.async_processor import get_streamlit_async_helper
            self.async_helper = get_streamlit_async_helper()
        except ImportError:
            self.async_helper = None

        # 组件初始化标志
        self._initialized = True

        logger.debug(f"UI组件初始化完成: {self.component_id}")

    @abstractmethod
    def render(self, st_obj, **kwargs) -> None:
        """
        渲染组件

        Args:
            st_obj: Streamlit对象
            **kwargs: 其他参数
        """
        pass

    def render_with_monitoring(self, st_obj, **kwargs) -> None:
        """
        带性能监控的渲染方法

        Args:
            st_obj: Streamlit对象
            **kwargs: 其他参数
        """
        if self.performance_monitor:
            with self.performance_monitor.monitor_operation(self.component_id, "render"):
                return self.render(st_obj, **kwargs)
        else:
            return self.render(st_obj, **kwargs)

    def process_async(self, st_obj, processor_func, data, description: str = "处理中..."):
        """
        异步处理数据

        Args:
            st_obj: Streamlit对象
            processor_func: 处理函数
            data: 要处理的数据
            description: 处理描述

        Returns:
            处理结果
        """
        if self.async_helper:
            return self.async_helper.process_data_async(st_obj, processor_func, data, description)
        else:
            # 同步处理作为备选
            return processor_func(data)

    @abstractmethod
    def get_state_keys(self) -> List[str]:
        """
        获取组件相关的状态键

        Returns:
            List[str]: 状态键列表
        """
        pass

    def get_component_id(self) -> str:
        """
        获取组件ID

        Returns:
            str: 组件ID
        """
        # 移除Component后缀，转换为小写
        class_name = self.__class__.__name__
        if class_name.endswith('Component'):
            class_name = class_name[:-9]  # 移除'Component'
        return class_name.lower()

    def get_state(self, key: str, default=None):
        """
        获取组件状态

        Args:
            key: 状态键
            default: 默认值

        Returns:
            状态值
        """
        try:
            return self.state_adapter.get_component_state(self.component_id, key, default)
        except Exception as e:
            logger.error(f"获取组件状态失败: {self.component_id}.{key}, 错误: {e}")
            return default

    def set_state(self, key: str, value):
        """
        设置组件状态

        Args:
            key: 状态键
            value: 状态值

        Returns:
            bool: 是否设置成功
        """
        try:
            return self.state_adapter.set_component_state(self.component_id, key, value)
        except Exception as e:
            logger.error(f"设置组件状态失败: {self.component_id}.{key}, 错误: {e}")
            return False

    def get_all_states(self) -> Dict[str, Any]:
        """
        获取组件的所有状态

        Returns:
            Dict[str, Any]: 组件的所有状态
        """
        try:
            return self.state_adapter.get_all_component_states(self.component_id)
        except Exception as e:
            logger.error(f"获取组件所有状态失败: {self.component_id}, 错误: {e}")
            return {}

    def cleanup(self):
        """
        组件清理

        在组件销毁时调用，清理相关状态和资源
        """
        try:
            # 清理组件状态
            self.state_adapter.cleanup_component_state(self.component_id)

            # 注销组件
            self.state_adapter.unregister_component(self.component_id)

            logger.debug(f"组件清理完成: {self.component_id}")

        except Exception as e:
            logger.error(f"组件清理失败: {self.component_id}, 错误: {e}")

    def handle_error(self, st_obj, error: Exception, context: str = "", **kwargs):
        """
        统一错误处理 - 使用标准化的UI错误处理器

        Args:
            st_obj: Streamlit对象
            error: 异常对象
            context: 错误上下文
            **kwargs: 额外参数
        """
        try:
            # 使用标准化的UI错误处理器
            from ui.utils.error_handler import get_ui_error_handler

            error_handler = get_ui_error_handler()
            result = error_handler.handle_ui_error(
                error=error,
                component_id=self.component_id,
                context=context,
                st_obj=st_obj,
                **kwargs
            )

            # 记录错误到组件状态
            if result.get('success'):
                self.set_state('last_error', result['error_info'])
                self.set_state('error_count', self.get_state('error_count', 0) + 1)

            return result

        except Exception as e:
            # 错误处理本身出错时的兜底处理
            logger.error(f"标准化错误处理失败: {self.component_id}, 原始错误: {error}, 处理错误: {e}")

            # 使用简单的兜底处理
            try:
                st_obj.error(f"组件 {self.component_id} 发生错误，请稍后重试")
                self.set_state('last_error', {
                    'error_type': type(error).__name__,
                    'error_message': str(error),
                    'context': context,
                    'error_handled': True
                })
            except Exception:
                # 最后的兜底处理
                logger.error(f"兜底错误处理也失败: {self.component_id}")

            return {
                'success': False,
                'error_recovery_used': True,
                'error_info': {'error': str(e)}
            }

    def validate_props(self, props: Dict[str, Any]) -> bool:
        """
        验证组件属性
        
        Args:
            props: 组件属性字典
            
        Returns:
            bool: 验证是否通过
        """
        return True



    def log_action(self, action: str, details: Dict[str, Any] = None) -> None:
        """
        记录组件操作 - 使用统一状态管理

        Args:
            action: 操作名称
            details: 操作详情
        """
        # 使用统一基类的日志记录方法
        self.add_component_log(action, details, "INFO")

        # 同时记录到传统日志系统
        if details:
            self.logger.info(f"组件操作: {action}, 详情: {details}")
        else:
            self.logger.info(f"组件操作: {action}")


class BaseWelcomePage(UIComponent):
    """欢迎页面基类"""

    def __init__(self):
        # 调用父类初始化，集成统一状态管理
        super().__init__()

        self.constants = None
        self.module_config = None
    
    def render(self, st_obj, **kwargs) -> None:
        """渲染欢迎页面"""
        try:
            # 显示标题和介绍
            self._render_header(st_obj)
            
            # 显示子模块选择卡片
            self._render_sub_modules(st_obj)
            
            # 处理导航事件
            self._handle_navigation(st_obj)
            
        except Exception as e:
            self.handle_error(st_obj, e, "渲染欢迎页面")
    
    def _render_header(self, st_obj) -> None:
        """渲染页面头部"""
        if self.module_config and 'title' in self.module_config:
            st_obj.title(self.module_config['title'])
        
        if self.module_config and 'description' in self.module_config:
            st_obj.markdown(self.module_config['description'])
    
    def _render_sub_modules(self, st_obj) -> None:
        """渲染子模块选择卡片"""
        if not self.module_config or 'sub_modules' not in self.module_config:
            return
        
        sub_modules = self.module_config['sub_modules']
        if not sub_modules:
            return
        
        # 创建列布局
        cols = st_obj.columns(len(sub_modules))
        
        for i, (sub_module_name, sub_module_config) in enumerate(sub_modules.items()):
            with cols[i]:
                if st_obj.button(
                    sub_module_name,
                    key=f"welcome_sub_module_{sub_module_name}",
                    use_container_width=True
                ):
                    self._handle_sub_module_click(st_obj, sub_module_name)
    
    def _handle_sub_module_click(self, st_obj, sub_module_name: str) -> None:
        """处理子模块点击事件"""
        # 记录操作
        self.log_action('sub_module_click', {'sub_module': sub_module_name})
        
        # 这里可以添加导航逻辑
        # 目前只是显示信息
        st_obj.info(f"点击了子模块: {sub_module_name}")
    
    def _handle_navigation(self, st_obj) -> None:
        """处理导航事件"""
        # 子类可以重写此方法来实现具体的导航逻辑
        pass
    
    def get_state_keys(self) -> List[str]:
        """获取状态键"""
        return [
            'component_logs',
            'current_time'
        ]


__all__ = [
    'UnifiedBaseComponent',
    'UIComponent',
    'BaseWelcomePage'
]
