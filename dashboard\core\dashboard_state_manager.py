# -*- coding: utf-8 -*-
"""
Dashboard专用状态管理器
用于统一管理dashboard.py中的所有状态
"""

import logging
import time
import os
from typing import Any, Dict, Tuple, Optional

class DashboardStateManager:
    """Dashboard专用状态管理器"""
    
    def __init__(self, unified_manager):
        self.unified_manager = unified_manager
        self.logger = logging.getLogger(__name__)
        
        if not self.unified_manager:
            self.logger.error("统一状态管理器不可用")
    
    # 页面配置状态管理
    def get_page_config_state(self) -> bool:
        """获取页面配置状态"""
        if self.unified_manager:
            return self.unified_manager.get_state('dashboard.page_config_set', False)
        return False
    
    def set_page_config_state(self, value: bool) -> bool:
        """设置页面配置状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state('dashboard.page_config_set', value)
            if success:
                self.logger.debug("页面配置状态设置成功")
            else:
                self.logger.error("页面配置状态设置失败")
            return success
        return False
    
    # CSS注入状态管理
    def get_css_injection_state(self, css_key: str) -> bool:
        """获取CSS注入状态"""
        if self.unified_manager:
            return self.unified_manager.get_state(f'dashboard.css.{css_key}', False)
        return False
    
    def set_css_injection_state(self, css_key: str, value: bool) -> bool:
        """设置CSS注入状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state(f'dashboard.css.{css_key}', value)
            if success:
                self.logger.debug(f"CSS注入状态设置成功: {css_key}")
            else:
                self.logger.error(f"CSS注入状态设置失败: {css_key}")
            return success
        return False
    
    # 管理器缓存状态
    def get_managers_cache(self) -> Optional[Tuple]:
        """获取管理器缓存"""
        if self.unified_manager:
            return self.unified_manager.get_state('dashboard.managers_cache', None)
        return None
    
    def set_managers_cache(self, managers_tuple: Tuple, health_status: bool) -> bool:
        """设置管理器缓存"""
        if self.unified_manager:
            success1 = self.unified_manager.set_state('dashboard.managers_cache', managers_tuple)
            success2 = self.unified_manager.set_state('dashboard.managers_health', health_status)
            success3 = self.unified_manager.set_state('dashboard.managers_cache_time', time.time())
            
            if success1 and success2 and success3:
                self.logger.debug("管理器缓存设置成功")
                return True
            else:
                self.logger.error("管理器缓存设置失败")
                return False
        return False
    
    def get_managers_cache_time(self) -> float:
        """获取管理器缓存时间"""
        if self.unified_manager:
            return self.unified_manager.get_state('dashboard.managers_cache_time', 0)
        return 0
    
    def get_managers_health_status(self) -> bool:
        """获取管理器健康状态"""
        if self.unified_manager:
            return self.unified_manager.get_state('dashboard.managers_health', False)
        return False
    
    # 渲染跟踪状态
    def get_render_tracking(self) -> Dict[str, Any]:
        """获取渲染跟踪状态"""
        default_tracking = {
            'count': 0,
            'last_reset': time.time(),
            'last_render': 0
        }
        if self.unified_manager:
            return self.unified_manager.get_state('dashboard.render_tracking', default_tracking)
        return default_tracking
    
    def update_render_tracking(self, tracking_data: Dict[str, Any]) -> bool:
        """更新渲染跟踪状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state('dashboard.render_tracking', tracking_data)
            if success:
                self.logger.debug("渲染跟踪状态更新成功")
            else:
                self.logger.error("渲染跟踪状态更新失败")
            return success
        return False
    
    def increment_render_count(self) -> bool:
        """增加渲染计数"""
        tracking = self.get_render_tracking()
        tracking['count'] += 1
        tracking['last_render'] = time.time()
        return self.update_render_tracking(tracking)
    
    def reset_render_tracking(self) -> bool:
        """重置渲染跟踪"""
        current_time = time.time()
        tracking = {
            'count': 0,
            'last_reset': current_time,
            'last_render': current_time
        }
        return self.update_render_tracking(tracking)
    
    # 会话唯一ID管理
    def get_unique_session_id(self) -> str:
        """获取唯一会话ID"""
        if self.unified_manager:
            existing_id = self.unified_manager.get_state('dashboard.unique_session_id', None)
            if not existing_id:
                new_id = f"{os.getpid()}_{int(time.time() * 1000)}"
                self.unified_manager.set_state('dashboard.unique_session_id', new_id)
                self.logger.debug(f"创建新的会话ID: {new_id}")
                return new_id
            return existing_id
        return f"fallback_{os.getpid()}_{int(time.time() * 1000)}"
    
    # 初始化锁管理
    def get_init_lock_state(self, lock_key: str) -> bool:
        """获取初始化锁状态"""
        if self.unified_manager:
            return self.unified_manager.get_state(f'dashboard.init_lock.{lock_key}', False)
        return False
    
    def set_init_lock_state(self, lock_key: str, value: bool) -> bool:
        """设置初始化锁状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state(f'dashboard.init_lock.{lock_key}', value)
            if success:
                self.logger.debug(f"初始化锁状态设置成功: {lock_key} = {value}")
            else:
                self.logger.error(f"初始化锁状态设置失败: {lock_key}")
            return success
        return False
    
    # 侧边栏渲染状态
    def get_sidebar_render_state(self, key: str, default: Any = None) -> Any:
        """获取侧边栏渲染状态"""
        if self.unified_manager:
            return self.unified_manager.get_state(f'dashboard.sidebar.{key}', default)
        return default
    
    def set_sidebar_render_state(self, key: str, value: Any) -> bool:
        """设置侧边栏渲染状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state(f'dashboard.sidebar.{key}', value)
            if success:
                self.logger.debug(f"侧边栏状态设置成功: {key}")
            else:
                self.logger.error(f"侧边栏状态设置失败: {key}")
            return success
        return False
    
    # 导航状态管理
    def get_navigation_state(self, key: str, default: Any = None) -> Any:
        """获取导航状态"""
        if self.unified_manager:
            return self.unified_manager.get_state(f'dashboard.navigation.{key}', default)
        return default
    
    def set_navigation_state(self, key: str, value: Any) -> bool:
        """设置导航状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state(f'dashboard.navigation.{key}', value)
            if success:
                self.logger.debug(f"导航状态设置成功: {key}")
            else:
                self.logger.error(f"导航状态设置失败: {key}")
            return success
        return False
    
    # 通用状态管理
    def get_dashboard_state(self, key: str, default: Any = None) -> Any:
        """获取dashboard通用状态"""
        if self.unified_manager:
            return self.unified_manager.get_state(f'dashboard.{key}', default)
        return default
    
    def set_dashboard_state(self, key: str, value: Any) -> bool:
        """设置dashboard通用状态"""
        if self.unified_manager:
            success = self.unified_manager.set_state(f'dashboard.{key}', value)
            if success:
                self.logger.debug(f"Dashboard状态设置成功: {key}")
            else:
                self.logger.error(f"Dashboard状态设置失败: {key}")
            return success
        return False
    
    # 健康检查
    def is_healthy(self) -> bool:
        """检查状态管理器是否健康"""
        return self.unified_manager is not None
    
    def get_state_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        if not self.unified_manager:
            return {'error': '统一状态管理器不可用'}
        
        try:
            all_keys = self.unified_manager.get_all_keys()
            dashboard_keys = [key for key in all_keys if str(key).startswith('dashboard.')]
            
            return {
                'total_dashboard_keys': len(dashboard_keys),
                'page_config_set': self.get_page_config_state(),
                'unique_session_id': self.get_unique_session_id(),
                'render_tracking': self.get_render_tracking(),
                'managers_cached': self.get_managers_cache() is not None,
                'managers_health': self.get_managers_health_status()
            }
        except Exception as e:
            self.logger.error(f"获取状态摘要失败: {e}")
            return {'error': str(e)}


# 全局实例管理
_dashboard_state_manager = None

def get_dashboard_state_manager(unified_manager=None):
    """获取Dashboard状态管理器实例"""
    global _dashboard_state_manager
    
    if _dashboard_state_manager is None and unified_manager is not None:
        _dashboard_state_manager = DashboardStateManager(unified_manager)
    
    return _dashboard_state_manager


__all__ = ['DashboardStateManager', 'get_dashboard_state_manager']
