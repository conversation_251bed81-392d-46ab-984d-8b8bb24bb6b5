#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工业宏观运行分析 - 图表生成模块
"""

import pandas as pd
import plotly.graph_objects as go
from typing import List, Optional
from .data_processor import filter_data_by_time_range


def clean_variable_name_for_legend(var: str) -> str:
    """
    Clean variable name for display in legend
    按照上游XX、中游XX、下游XX的顺序显示
    """
    display_name = var
    if var.startswith('出口依赖_'):
        display_name = var.replace('出口依赖_', '') + '行业'
    elif var.startswith('上中下游_'):
        display_name = var.replace('上中下游_', '')
    return display_name


def sort_variables_by_stream_order(variables: List[str]) -> List[str]:
    """
    按照上游XX、中游XX、下游XX的顺序对变量进行排序
    """
    def get_sort_key(var: str):
        if var.startswith('上中下游_'):
            # 提取行业类型
            industry_type = var.replace('上中下游_', '')

            # 按照上游、中游、下游的顺序排序
            if industry_type.startswith('上游'):
                return (0, industry_type)  # 上游排在最前
            elif industry_type.startswith('中游'):
                return (1, industry_type)  # 中游排在中间
            elif industry_type.startswith('下游'):
                return (2, industry_type)  # 下游排在最后
            else:
                return (3, industry_type)  # 未知类型排在最后
        else:
            # 非上中下游变量保持原顺序
            return (4, var)

    return sorted(variables, key=get_sort_key)


def create_single_axis_chart(df: pd.DataFrame, variables: List[str], title: str,
                           time_range: str = "全部", custom_start_date: Optional[str] = None,
                           custom_end_date: Optional[str] = None) -> go.Figure:
    """
    Create single Y-axis time series chart for specified variables using Plotly
    """
    if df.empty or not variables:
        return go.Figure()

    # 按照上游、中游、下游的顺序对变量进行排序
    variables = sort_variables_by_stream_order(variables)

    # Apply time range filtering
    filtered_df = filter_data_by_time_range(df, time_range, custom_start_date, custom_end_date)

    # Create figure
    fig = go.Figure()

    # Color palette
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

    # Add traces for each variable
    for i, var in enumerate(variables):
        if var in filtered_df.columns:
            # Clean the data - remove non-numeric values
            series = filtered_df[var].copy()
            series = pd.to_numeric(series, errors='coerce')
            series = series.dropna()

            if not series.empty:
                # Get color for this variable
                color = colors[i % len(colors)]

                # Create display name for legend
                display_name = clean_variable_name_for_legend(var)

                # Ensure data types are compatible with Plotly
                try:
                    # Convert index to list for Plotly compatibility
                    if isinstance(series.index, pd.DatetimeIndex):
                        x_data = series.index.tolist()
                    else:
                        # Try to convert to datetime if possible
                        try:
                            x_data = pd.to_datetime(series.index).tolist()
                        except:
                            # Fallback to original index as list
                            x_data = series.index.tolist()

                    # Ensure y values are numeric and convert to list
                    y_data = series.values.tolist()

                    # Validate data before creating trace
                    if len(x_data) == len(y_data) and len(x_data) > 0:
                        # Add trace to single y-axis
                        line = go.Scatter(
                            x=x_data,
                            y=y_data,
                            showlegend=True,
                            line=dict(color=color, width=1.5),
                            connectgaps=True,
                            mode='lines+markers',
                            marker=dict(size=3),
                            name=display_name
                        )
                        fig.add_trace(line)
                    else:
                        print(f"Warning: Data length mismatch for variable {var}: x={len(x_data)}, y={len(y_data)}")

                except Exception as e:
                    print(f"Error creating trace for variable {var}: {e}")
                    # Skip this variable if there's an error
                    continue

    # Calculate actual data range for x-axis
    all_dates = []
    for var in variables:
        if var in filtered_df.columns:
            series = pd.to_numeric(filtered_df[var], errors='coerce').dropna()
            if not series.empty:
                try:
                    # Ensure dates are properly formatted
                    if isinstance(series.index, pd.DatetimeIndex):
                        all_dates.extend(series.index.tolist())
                    else:
                        # Try to convert to datetime
                        try:
                            date_index = pd.to_datetime(series.index)
                            all_dates.extend(date_index.tolist())
                        except:
                            # Skip if conversion fails
                            continue
                except Exception as e:
                    print(f"Warning: Error processing dates for variable {var}: {e}")
                    continue

    if all_dates:
        min_date = min(all_dates)
        max_date = max(all_dates)
    else:
        min_date = max_date = None

    # Configure x-axis with 3-month intervals
    xaxis_config = dict(
        title="",  # No x-axis title as requested
        type="date",
        showgrid=True,
        gridwidth=1,
        gridcolor='lightgray',
        dtick="M3"  # 3-month intervals
    )

    # Set the range to actual data if available
    if min_date and max_date:
        xaxis_config['range'] = [min_date, max_date]

    # Update layout with single y-axis configuration
    layout_config = dict(
        title="",  # No chart title as requested
        xaxis=xaxis_config,
        yaxis=dict(
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            title=""  # No y-axis title
        ),
        legend=dict(
            orientation="h",
            yanchor="top",
            y=-0.1,
            xanchor="center",
            x=0.5
        ),
        margin=dict(l=50, r=50, t=30, b=80),
        plot_bgcolor='white',
        paper_bgcolor='white'
    )

    fig.update_layout(layout_config)
    return fig



