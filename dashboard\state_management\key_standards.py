# -*- coding: utf-8 -*-
"""
状态键标准化规范
定义统一的状态键命名规范、模块前缀和层次结构
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Set
from enum import Enum


class ModulePrefix(Enum):
    """模块前缀枚举"""
    DASHBOARD = "dashboard"
    PREVIEW = "preview"
    DFM = "dfm"
    TOOLS = "tools"
    UI = "ui"
    NAVIGATION = "navigation"
    ANALYSIS = "analysis"
    MONITORING = "monitoring"


class StateKeyStandards:
    """状态键标准化管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 模块前缀定义
        self.module_prefixes = {
            ModulePrefix.DASHBOARD.value: "dashboard.",
            ModulePrefix.PREVIEW.value: "preview.",
            ModulePrefix.DFM.value: "dfm.",
            ModulePrefix.TOOLS.value: "tools.",
            ModulePrefix.UI.value: "ui.",
            ModulePrefix.NAVIGATION.value: "navigation.",
            ModulePrefix.ANALYSIS.value: "analysis.",
            ModulePrefix.MONITORING.value: "monitoring."
        }
        
        # 层次结构定义
        self.hierarchy_patterns = {
            # dashboard模块层次结构
            "dashboard": [
                "dashboard.{component}",           # dashboard.sidebar, dashboard.main
                "dashboard.{component}.{state}",   # dashboard.sidebar.rendered
                "dashboard.{component}.{state}.{detail}"  # dashboard.sidebar.button.clicked
            ],
            
            # preview模块层次结构
            "preview": [
                "preview.{data_type}",             # preview.daily, preview.monthly
                "preview.{data_type}.{state}",     # preview.daily.df, preview.daily.industries
                "preview.{data_type}.{state}.{detail}"  # preview.daily.cache.summary
            ],
            
            # dfm模块层次结构
            "dfm": [
                "dfm.{submodule}",                 # dfm.data_prep, dfm.train_model
                "dfm.{submodule}.{component}",     # dfm.data_prep.file_upload
                "dfm.{submodule}.{component}.{state}"  # dfm.data_prep.file_upload.status
            ],
            
            # tools模块层次结构
            "tools": [
                "tools.{tool_type}",               # tools.time_series_compute
                "tools.{tool_type}.{component}",   # tools.time_series_compute.processor
                "tools.{tool_type}.{component}.{state}"  # tools.time_series_compute.processor.status
            ],
            
            # ui模块层次结构
            "ui": [
                "ui.{component_type}",             # ui.sidebar, ui.content
                "ui.{component_type}.{component}", # ui.sidebar.navigation
                "ui.{component_type}.{component}.{state}"  # ui.sidebar.navigation.selected
            ],
            
            # navigation模块层次结构
            "navigation": [
                "navigation.{category}",           # navigation.main_module, navigation.sub_module
                "navigation.{category}.{state}",   # navigation.main_module.current
                "navigation.{category}.{state}.{detail}"  # navigation.main_module.history.last
            ]
        }
        
        # 预定义的标准键映射
        self.standard_key_mappings = {
            # Dashboard相关键映射
            "sidebar_rendered": "dashboard.sidebar.rendered",
            "sidebar_key_counter": "dashboard.sidebar.key_counter",
            "main_content_rendered": "dashboard.main_content.rendered",
            "unique_session_id": "dashboard.session.unique_id",
            "current_render_id": "dashboard.render.current_id",
            "page_config_set": "dashboard.page.config_set",
            
            # Navigation相关键映射
            "selected_main_module": "navigation.main_module.selected",
            "selected_sub_module": "navigation.sub_module.selected",
            "temp_selected_main_module": "navigation.main_module.temp_selected",
            "temp_selected_sub_module": "navigation.sub_module.temp_selected",
            "last_clicked_sub_module": "navigation.sub_module.last_clicked",
            "navigate_to_sub_module": "navigation.sub_module.navigate_to",
            
            # Preview相关键映射
            "daily_df": "preview.daily.df",
            "monthly_df": "preview.monthly.df",
            "weekly_df": "preview.weekly.df",
            "industrial_df": "preview.industrial.df",
            "daily_industries": "preview.daily.industries",
            "monthly_industries": "preview.monthly.industries",
            "weekly_industries": "preview.weekly.industries",
            "industrial_industries": "preview.industrial.industries",
            
            # DFM相关键映射
            "dfm_prepared_data_df": "dfm.data_prep.prepared_data.df",
            "dfm_model_config": "dfm.train_model.config",
            "dfm_training_status": "dfm.train_model.status",
            
            # Tools相关键映射
            "time_series_compute_status": "tools.time_series_compute.status",
            "data_exploration_state": "tools.data_exploration.state",
            
            # UI相关键映射
            "ui_component_state": "ui.component.state",
            "widget_state": "ui.widget.state"
        }
        
        # 反向映射（从标准键到原始键）
        self.reverse_key_mappings = {v: k for k, v in self.standard_key_mappings.items()}
        
        # 键验证规则
        self.validation_rules = {
            "max_length": 100,
            "allowed_chars": re.compile(r'^[a-zA-Z0-9._-]+$'),
            "reserved_words": {"state", "session", "temp", "cache", "lock"},
            "min_parts": 2,  # 至少要有模块前缀和一个组件
            "max_parts": 5   # 最多5个层次
        }
    
    def normalize_key(self, key: str) -> str:
        """
        标准化状态键
        
        Args:
            key: 原始键名
            
        Returns:
            标准化后的键名
        """
        # 如果已经是标准格式，直接返回
        if self.is_standard_key(key):
            return key
        
        # 检查预定义映射
        if key in self.standard_key_mappings:
            return self.standard_key_mappings[key]
        
        # 尝试自动推断模块前缀
        inferred_key = self._infer_module_prefix(key)
        if inferred_key != key:
            return inferred_key
        
        # 如果无法推断，返回原键（可能需要手动处理）
        self.logger.warning(f"Unable to normalize key: {key}")
        return key
    
    def is_standard_key(self, key: str) -> bool:
        """
        检查键是否符合标准格式
        
        Args:
            key: 键名
            
        Returns:
            是否符合标准格式
        """
        # 检查是否有有效的模块前缀
        for prefix in self.module_prefixes.values():
            if key.startswith(prefix):
                return True
        return False
    
    def validate_key(self, key: str) -> Tuple[bool, List[str]]:
        """
        验证键名是否符合规范
        
        Args:
            key: 键名
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查长度
        if len(key) > self.validation_rules["max_length"]:
            errors.append(f"Key too long: {len(key)} > {self.validation_rules['max_length']}")
        
        # 检查字符
        if not self.validation_rules["allowed_chars"].match(key):
            errors.append("Key contains invalid characters")
        
        # 检查层次结构
        parts = key.split('.')
        if len(parts) < self.validation_rules["min_parts"]:
            errors.append(f"Key has too few parts: {len(parts)} < {self.validation_rules['min_parts']}")
        
        if len(parts) > self.validation_rules["max_parts"]:
            errors.append(f"Key has too many parts: {len(parts)} > {self.validation_rules['max_parts']}")
        
        # 检查模块前缀
        if parts and not any(key.startswith(prefix) for prefix in self.module_prefixes.values()):
            errors.append("Key does not have a valid module prefix")
        
        # 检查保留字
        for part in parts:
            if part in self.validation_rules["reserved_words"]:
                errors.append(f"Key contains reserved word: {part}")
        
        return len(errors) == 0, errors
    
    def _infer_module_prefix(self, key: str) -> str:
        """
        推断键的模块前缀
        
        Args:
            key: 原始键名
            
        Returns:
            推断后的键名
        """
        key_lower = key.lower()
        
        # Dashboard相关关键词
        dashboard_keywords = ['dashboard', 'main', 'sidebar', 'render', 'page', 'config', 'session']
        if any(keyword in key_lower for keyword in dashboard_keywords):
            return f"dashboard.{key}"
        
        # Preview相关关键词
        preview_keywords = ['preview', 'daily', 'monthly', 'weekly', 'industrial', 'df', 'industries']
        if any(keyword in key_lower for keyword in preview_keywords):
            return f"preview.{key}"
        
        # DFM相关关键词
        dfm_keywords = ['dfm', 'model', 'factor', 'train', 'data_prep', 'prepared']
        if any(keyword in key_lower for keyword in dfm_keywords):
            return f"dfm.{key}"
        
        # Tools相关关键词
        tools_keywords = ['tools', 'compute', 'process', 'time_series', 'exploration']
        if any(keyword in key_lower for keyword in tools_keywords):
            return f"tools.{key}"
        
        # UI相关关键词
        ui_keywords = ['ui', 'component', 'widget', 'button', 'input', 'select']
        if any(keyword in key_lower for keyword in ui_keywords):
            return f"ui.{key}"
        
        # Navigation相关关键词
        nav_keywords = ['nav', 'navigation', 'route', 'selected', 'module', 'navigate']
        if any(keyword in key_lower for keyword in nav_keywords):
            return f"navigation.{key}"
        
        return key
    
    def get_module_from_key(self, key: str) -> Optional[str]:
        """
        从键名中提取模块名
        
        Args:
            key: 键名
            
        Returns:
            模块名或None
        """
        for module, prefix in self.module_prefixes.items():
            if key.startswith(prefix):
                return module
        return None
    
    def get_keys_by_module(self, keys: List[str], module: str) -> List[str]:
        """
        按模块筛选键
        
        Args:
            keys: 键列表
            module: 模块名
            
        Returns:
            属于指定模块的键列表
        """
        if module not in self.module_prefixes:
            return []
        
        prefix = self.module_prefixes[module]
        return [key for key in keys if key.startswith(prefix)]
    
    def create_key(self, module: str, *components: str) -> str:
        """
        创建标准格式的键
        
        Args:
            module: 模块名
            *components: 组件名称列表
            
        Returns:
            标准格式的键名
        """
        if module not in self.module_prefixes:
            raise ValueError(f"Invalid module: {module}")
        
        prefix = self.module_prefixes[module]
        components_str = '.'.join(components)
        key = f"{prefix}{components_str}"
        
        # 验证生成的键
        is_valid, errors = self.validate_key(key)
        if not is_valid:
            raise ValueError(f"Generated invalid key: {key}, errors: {errors}")
        
        return key
    
    def get_migration_mapping(self) -> Dict[str, str]:
        """
        获取完整的迁移映射
        
        Returns:
            从旧键到新键的映射字典
        """
        return self.standard_key_mappings.copy()
    
    def add_custom_mapping(self, old_key: str, new_key: str) -> bool:
        """
        添加自定义键映射
        
        Args:
            old_key: 旧键名
            new_key: 新键名
            
        Returns:
            是否添加成功
        """
        try:
            # 验证新键
            is_valid, errors = self.validate_key(new_key)
            if not is_valid:
                self.logger.error(f"Invalid new key: {new_key}, errors: {errors}")
                return False
            
            self.standard_key_mappings[old_key] = new_key
            self.reverse_key_mappings[new_key] = old_key
            
            self.logger.info(f"Added custom mapping: {old_key} -> {new_key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add custom mapping: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, any]:
        """获取标准化统计信息"""
        return {
            "total_mappings": len(self.standard_key_mappings),
            "modules_count": len(self.module_prefixes),
            "validation_rules": self.validation_rules,
            "hierarchy_patterns_count": sum(len(patterns) for patterns in self.hierarchy_patterns.values())
        }


# 全局实例
_key_standards = None

def get_key_standards() -> StateKeyStandards:
    """获取状态键标准化管理器实例"""
    global _key_standards
    if _key_standards is None:
        _key_standards = StateKeyStandards()
    return _key_standards


__all__ = [
    'ModulePrefix',
    'StateKeyStandards',
    'get_key_standards'
]
