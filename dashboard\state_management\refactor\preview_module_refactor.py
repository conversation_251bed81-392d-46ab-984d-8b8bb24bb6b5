# -*- coding: utf-8 -*-
"""
Preview模块重构适配器
提供Preview模块与统一状态管理器的集成接口
"""

import pandas as pd
from typing import Dict, Any, Optional, List, Union
from .module_refactor_base import ModuleRefactorBase
from ..unified_state_manager import UnifiedStateManager


class PreviewModuleRefactor(ModuleRefactorBase):
    """Preview模块重构适配器"""
    
    def __init__(self, unified_manager: UnifiedStateManager = None):
        # 先初始化Preview特定的状态键映射
        self.preview_state_mapping = {
            # 数据相关
            'daily_df': 'daily_df',
            'monthly_df': 'monthly_df',
            'weekly_df': 'weekly_df',
            'industrial_df': 'industrial_df',
            
            # 行业和映射相关
            'daily_industries': 'daily_industries',
            'monthly_industries': 'monthly_industries',
            'weekly_industries': 'weekly_industries',
            'industrial_industries': 'industrial_industries',
            'clean_industry_map': 'clean_industry_map',
            'source_map': 'source_map',
            
            # 缓存相关
            'daily_summary_cache': 'daily_summary_cache',
            'monthly_summary_cache': 'monthly_summary_cache',
            'weekly_summary_cache': 'weekly_summary_cache',
            'industrial_summary_cache': 'industrial_summary_cache',
            'full_monthly_summary': 'full_monthly_summary',
            'full_weekly_summary': 'full_weekly_summary',
            'full_industrial_summary': 'full_industrial_summary',
            
            # 状态标志
            'data_loaded': 'data_loaded',
            'preview_initialized': 'preview_initialized',
            
            # 兼容性键（带preview前缀）
            'preview_daily_df': 'daily_df',
            'preview_monthly_df': 'monthly_df',
            'preview_weekly_df': 'weekly_df',
            'preview_industrial_df': 'industrial_df',
            'preview_daily_industries': 'daily_industries',
            'preview_monthly_industries': 'monthly_industries',
            'preview_weekly_industries': 'weekly_industries',
            'preview_industrial_industries': 'industrial_industries',
            'preview_clean_industry_map': 'clean_industry_map',
            'preview_source_map': 'source_map',
            'preview_daily_summary_cache': 'daily_summary_cache',
            'preview_monthly_summary_cache': 'monthly_summary_cache',
            'preview_weekly_summary_cache': 'weekly_summary_cache',
            'preview_industrial_summary_cache': 'industrial_summary_cache'
        }
        
        # Preview子模块映射
        self.preview_module_mapping = {
            'daily': 'daily_data',
            'monthly': 'monthly_data',
            'weekly': 'weekly_data',
            'industrial': 'industrial_data',
            'diffusion': 'diffusion_analysis',
            'growth': 'growth_calculator'
        }

        # 调用父类初始化
        super().__init__("preview", unified_manager)
    
    def _initialize_module(self):
        """初始化Preview模块"""
        try:
            # 设置默认状态
            self._setup_default_states()

            # 初始化子模块
            self._initialize_submodules()

            # 尝试恢复持久化数据
            self._restore_persisted_data()

        except Exception as e:
            # 不抛出异常，允许继续运行
            pass
    
    def _setup_default_states(self):
        """设置默认状态"""
        default_states = {
            # 数据框默认为空
            'daily_df': pd.DataFrame(),
            'monthly_df': pd.DataFrame(),
            'weekly_df': pd.DataFrame(),
            'industrial_df': pd.DataFrame(),
            
            # 行业列表默认为空
            'daily_industries': [],
            'monthly_industries': [],
            'weekly_industries': [],
            'industrial_industries': [],
            
            # 映射默认为空字典
            'clean_industry_map': {},
            'source_map': {},
            
            # 缓存默认为空字典
            'daily_summary_cache': {},
            'monthly_summary_cache': {},
            'weekly_summary_cache': {},
            'industrial_summary_cache': {},
            
            # 完整摘要默认为None
            'full_monthly_summary': None,
            'full_weekly_summary': None,
            'full_industrial_summary': None,
            
            # 状态标志
            'data_loaded': False,
            'preview_initialized': True
        }
        
        for config_key, default_value in default_states.items():
            current_value = self.get_state(config_key)
            # 安全的布尔值检查，避免DataFrame的歧义性错误
            if current_value is None or (hasattr(current_value, 'empty') and current_value.empty) or (not hasattr(current_value, 'empty') and not current_value):
                self.set_state(config_key, default_value)
    
    def _initialize_submodules(self):
        """初始化子模块"""
        for module_name in self.preview_module_mapping.keys():
            # 为每个子模块设置基本状态
            self.set_preview_state(module_name, 'initialized', True)
            self.set_preview_state(module_name, 'last_update', None)

    def _restore_persisted_data(self):
        """恢复持久化数据 - 暂时禁用"""
        pass
        # try:
        #     print("[PreviewModuleRefactor] 开始恢复持久化数据...")
        #
        #     # 需要恢复的重要数据键
        #     important_keys = [
        #         'data_loaded_files', 'weekly_df', 'monthly_df', 'daily_df',
        #         'source_map', 'indicator_industry_map', 'clean_industry_map',
        #         'weekly_industries', 'monthly_industries', 'daily_industries'
        #     ]
        #
        #     restored_count = 0
        #     for key in important_keys:
        #         try:
        #             storage_key = f"preview.{key}"
        #             persisted_value = self.unified_manager.retrieve_data(storage_key, None)
        #             if persisted_value is not None:
        #                 # 恢复到 session_state
        #                 self.unified_manager.set_state(storage_key, persisted_value)
        #                 restored_count += 1
        #                 print(f"[PreviewModuleRefactor] 恢复数据: {key} = {type(persisted_value).__name__}")
        #         except Exception as e:
        #             print(f"[PreviewModuleRefactor] 恢复数据失败: {key}, 错误: {e}")
        #
        #     if restored_count > 0:
        #         print(f"[PreviewModuleRefactor] 成功恢复 {restored_count} 个数据项")
        #     else:
        #         print("[PreviewModuleRefactor] 没有找到可恢复的持久化数据")
        #
        # except Exception as e:
        #     print(f"[PreviewModuleRefactor] 数据恢复过程异常: {e}")
    
    def _get_from_unified_manager(self, key: str, default=None):
        """从统一状态管理器获取值"""
        if not self.unified_manager:
            return default

        # 只使用统一状态管理器
        try:
            # 确保key是字符串类型
            if not isinstance(key, str):
                print(f"[PreviewModuleRefactor] 警告: 键名不是字符串类型: {type(key)} = {key}")
                key = str(key)

            # 标准化键名：移除重复的preview前缀
            clean_key = key
            if key.startswith('preview_'):
                clean_key = key[8:]  # 移除 'preview_' 前缀

            # 只使用标准格式：preview.{clean_key}
            standard_key = f"preview.{clean_key}"
            value = self.unified_manager.get_state(standard_key, default)

            return value
        except Exception as e:
            print(f"[PreviewModuleRefactor] 获取数据异常: {key}, 错误: {e}")
            return default

    def _set_to_unified_manager(self, key: str, value) -> bool:
        """设置值到统一状态管理器（只使用统一管理器）"""
        if not self.unified_manager:
            return False

        try:
            # 标准化键名：移除重复的preview前缀
            clean_key = key
            if key.startswith('preview_'):
                clean_key = key[8:]  # 移除 'preview_' 前缀

            # 使用标准格式存储
            storage_key = f"preview.{clean_key}"
            success = self.unified_manager.set_state(storage_key, value)

            return success
        except Exception as e:
            print(f"[PreviewModuleRefactor] 设置异常: {key}, 错误: {e}")
            return False

    def _should_persist(self, key: str) -> bool:
        """判断是否应该持久化存储"""
        # 只持久化重要的数据，避免存储临时数据
        important_keys = {
            'data_loaded_files', 'weekly_df', 'monthly_df', 'daily_df',
            'source_map', 'indicator_industry_map', 'clean_industry_map',
            'weekly_industries', 'monthly_industries', 'daily_industries'
        }
        return key in important_keys

    def _delete_from_unified_manager(self, key: str) -> bool:
        """从统一状态管理器删除值"""
        if not self.unified_manager:
            return False

        try:
            # 标准化键名：移除重复的preview前缀
            clean_key = key
            if key.startswith('preview_'):
                clean_key = key[8:]  # 移除 'preview_' 前缀

            # 只删除标准格式的键
            standard_key = f"preview.{clean_key}"
            self.unified_manager.clear_state(standard_key)
            return True
        except Exception as e:
            print(f"[PreviewModuleRefactor] 删除异常: {key}, 错误: {e}")
            return True

    def _clear_unified_manager_state(self):
        """清空统一状态管理器中的Preview状态"""
        if not self.unified_manager:
            return

        # 只使用统一状态管理器
        try:
            # 清空所有preview相关的键（使用标准化格式）
            preview_keys = ['preview.daily_df', 'preview.monthly_df', 'preview.weekly_df',
                          'preview.industrial_df', 'preview.source_map', 'preview.clean_industry_map',
                          'preview.daily_industries', 'preview.monthly_industries', 'preview.weekly_industries',
                          'preview.industrial_industries', 'preview.daily_summary_cache',
                          'preview.monthly_summary_cache', 'preview.weekly_summary_cache',
                          'preview.industrial_summary_cache', 'preview.data_loaded_files',
                          'preview.indicator_industry_map']
            for key in preview_keys:
                self.unified_manager.clear_state(key)
                print(f"[PreviewModuleRefactor] 清空键: {key}")
        except Exception as e:
            print(f"[PreviewModuleRefactor] 清空状态异常: {e}")
            pass
    
    def get_preview_state(self, key_or_module, key=None, default=None):
        """获取Preview状态

        支持两种调用方式：
        1. get_preview_state(key, default) - 获取全局preview状态
        2. get_preview_state(module, key, default) - 获取子模块状态
        """
        try:
            # 判断调用方式：只有当key是字符串时才认为是方式2
            if key is None or not isinstance(key, str):
                # 方式1: get_preview_state(key, default)
                actual_key = key_or_module
                # 如果key不是None但也不是字符串，那么它实际上是default值
                actual_default = key if key is not None else default

                # 只使用统一状态管理器
                return self._get_from_unified_manager(actual_key, actual_default)
            else:
                # 方式2: get_preview_state(module, key, default)
                module = key_or_module
                actual_key = key
                actual_default = default

                # 构造完整的键名
                full_key = f"{module}.{actual_key}"

                # 只使用统一状态管理器
                return self._get_from_unified_manager(full_key, actual_default)

        except Exception as e:
            print(f"[PreviewModuleRefactor] 获取状态失败: {e}")
            return default

    def set_preview_state(self, key_or_module, key_or_value, value=None) -> bool:
        """设置Preview状态

        支持两种调用方式：
        1. set_preview_state(key, value) - 设置全局preview状态
        2. set_preview_state(module, key, value) - 设置子模块状态
        """
        try:
            # 判断调用方式
            if value is None:
                # 方式1: set_preview_state(key, value)
                actual_key = key_or_module
                actual_value = key_or_value

                # 只使用统一状态管理器
                return self._set_to_unified_manager(actual_key, actual_value)
            else:
                # 方式2: set_preview_state(module, key, value)
                module = key_or_module
                actual_key = key_or_value
                actual_value = value

                # 构造完整的键名
                full_key = f"{module}.{actual_key}"

                # 只使用统一状态管理器
                return self._set_to_unified_manager(full_key, actual_value)

        except Exception as e:
            print(f"[PreviewModuleRefactor] 设置状态失败: {e}")
            return False



    def get_data(self, data_type: str) -> pd.DataFrame:
        """获取特定类型的数据"""
        data_key = f"{data_type}"
        if data_key in self.preview_state_mapping:
            mapped_key = self.preview_state_mapping[data_key]
            data = self.get_state(mapped_key, pd.DataFrame())
            return data if isinstance(data, pd.DataFrame) else pd.DataFrame()
        return pd.DataFrame()
    
    def set_data(self, data_type: str, data: pd.DataFrame) -> bool:
        """设置特定类型的数据"""
        data_key = f"{data_type}"
        if data_key in self.preview_state_mapping:
            mapped_key = self.preview_state_mapping[data_key]
            return self.set_state(mapped_key, data)
        return False
    
    def get_industries(self, data_type: str) -> List[str]:
        """获取特定数据类型的行业列表"""
        industries_key = f"{data_type}_industries"
        if industries_key in self.preview_state_mapping:
            mapped_key = self.preview_state_mapping[industries_key]
            industries = self.get_state(mapped_key, [])
            return industries if isinstance(industries, list) else []
        return []
    
    def set_industries(self, data_type: str, industries: List[str]) -> bool:
        """设置特定数据类型的行业列表"""
        industries_key = f"{data_type}_industries"
        if industries_key in self.preview_state_mapping:
            mapped_key = self.preview_state_mapping[industries_key]
            return self.set_state(mapped_key, industries)
        return False
    
    def get_summary_cache(self, data_type: str) -> Dict[str, Any]:
        """获取特定数据类型的摘要缓存"""
        cache_key = f"{data_type}_summary_cache"
        if cache_key in self.preview_state_mapping:
            mapped_key = self.preview_state_mapping[cache_key]
            cache = self.get_state(mapped_key, {})
            return cache if isinstance(cache, dict) else {}
        return {}
    
    def set_summary_cache(self, data_type: str, cache: Dict[str, Any]) -> bool:
        """设置特定数据类型的摘要缓存"""
        cache_key = f"{data_type}_summary_cache"
        if cache_key in self.preview_state_mapping:
            mapped_key = self.preview_state_mapping[cache_key]
            return self.set_state(mapped_key, cache)
        return False
    
    # 缓存相关方法已移除，现在直接使用统一状态管理器
    
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            'module_name': self.module_name,
            'data_types': ['daily', 'monthly', 'weekly', 'industrial'],
            'submodules': list(self.preview_module_mapping.keys()),
            'state_keys': list(self.preview_state_mapping.keys()),
            'data_loaded': self.get_state('data_loaded', False),
            'preview_initialized': self.get_state('preview_initialized', False)
        }
    
    def validate_data_integrity(self) -> Dict[str, bool]:
        """验证数据完整性"""
        validation_results = {}
        
        for data_type in ['daily', 'monthly', 'weekly', 'industrial']:
            data = self.get_data(f"{data_type}_df")
            industries = self.get_industries(data_type)
            
            validation_results[f"{data_type}_data_valid"] = not data.empty
            validation_results[f"{data_type}_industries_valid"] = len(industries) > 0
            validation_results[f"{data_type}_consistent"] = (
                not data.empty and len(industries) > 0
            )
        
        return validation_results
    
    def on_module_load(self):
        """模块加载时的回调"""
        super().on_module_load()
        
        # 验证数据完整性
        validation_results = self.validate_data_integrity()
        if self.logger:
            self.logger.info(f"Preview模块数据验证结果: {validation_results}")
    
    def on_module_unload(self):
        """模块卸载时的回调"""
        super().on_module_unload()
        
        # 可以在这里添加清理逻辑
        if self.logger:
            self.logger.info("Preview模块已卸载")
        else:
            print("Preview模块已卸载")
