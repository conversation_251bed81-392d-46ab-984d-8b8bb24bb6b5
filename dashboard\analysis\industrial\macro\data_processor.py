#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工业宏观运行分析 - 数据处理后端模块
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
import os


def get_weight_for_year(df_weights_row: pd.Series, year: int) -> float:
    """
    根据年份选择合适的权重

    Args:
        df_weights_row: 权重数据行
        year: 年份

    Returns:
        对应年份的权重值
    """
    if year >= 2020 and '权重_2020' in df_weights_row.index and pd.notna(df_weights_row['权重_2020']):
        return df_weights_row['权重_2020']
    elif year >= 2018 and '权重_2018' in df_weights_row.index and pd.notna(df_weights_row['权重_2018']):
        return df_weights_row['权重_2018']
    elif year >= 2012 and '权重_2012' in df_weights_row.index and pd.notna(df_weights_row['权重_2012']):
        return df_weights_row['权重_2012']
    else:
        # 2012年之前不计算加权增速，返回0
        return 0.0


def filter_data_from_2012(df: pd.DataFrame) -> pd.DataFrame:
    """
    过滤数据，只保留2012年及以后的数据

    Args:
        df: 输入数据框

    Returns:
        过滤后的数据框
    """
    if df.empty:
        return df

    # 找到日期类型的索引
    date_indices = []
    for idx in df.index:
        if hasattr(idx, 'year') and idx.year >= 2012:
            date_indices.append(idx)

    if date_indices:
        return df.loc[date_indices]
    else:
        return pd.DataFrame()


def load_weights_data(file_path: str) -> pd.DataFrame:
    """
    Load weights data from Excel file
    """
    try:
        if os.path.exists(file_path):
            df_weights = pd.read_excel(file_path, sheet_name='工业增加值分行业指标权重')
            return df_weights
        else:
            return pd.DataFrame()
    except Exception as e:
        print(f"Error loading weights data: {e}")
        return pd.DataFrame()


def load_macro_data(file_path: str) -> pd.DataFrame:
    """
    Load macro operations data from Excel file
    """
    try:
        if os.path.exists(file_path):
            # 使用header=1来正确读取列名，index_col=0设置第一列为索引
            df_macro = pd.read_excel(file_path, sheet_name='分行业工业增加值同比增速', header=1, index_col=0)

            # 过滤掉非日期索引行（如果有的话）
            if not df_macro.empty:
                # 找到日期类型的索引
                date_indices = []
                for idx in df_macro.index:
                    if hasattr(idx, 'year'):
                        date_indices.append(idx)

                if date_indices:
                    df_macro = df_macro.loc[date_indices]

            return df_macro
        else:
            return pd.DataFrame()
    except Exception as e:
        print(f"Error loading macro data: {e}")
        return pd.DataFrame()


def create_grouping_mappings(df_weights: pd.DataFrame) -> Tuple[Dict[str, List[str]], Dict[str, List[str]]]:
    """
    Create grouping mappings from weights data (不再预先绑定权重)
    """
    export_groups = {}
    stream_groups = {}

    if not df_weights.empty and '出口依赖' in df_weights.columns and '上中下游' in df_weights.columns:
        # Group by export dependency
        for export_type in df_weights['出口依赖'].unique():
            if pd.notna(export_type):
                group_data = df_weights[df_weights['出口依赖'] == export_type]
                indicators = []
                for _, row in group_data.iterrows():
                    if pd.notna(row['指标名称']):
                        indicators.append(row['指标名称'])
                if indicators:
                    export_groups[export_type] = indicators

        # Group by upstream/downstream
        for stream_type in df_weights['上中下游'].unique():
            if pd.notna(stream_type):
                group_data = df_weights[df_weights['上中下游'] == stream_type]
                indicators = []
                for _, row in group_data.iterrows():
                    if pd.notna(row['指标名称']):
                        indicators.append(row['指标名称'])
                if indicators:
                    stream_groups[stream_type] = indicators

    return export_groups, stream_groups


def calculate_weighted_groups(df_macro: pd.DataFrame, df_weights: pd.DataFrame, target_columns: List[str]) -> pd.DataFrame:
    """
    Calculate weighted groups based on weights data
    支持按年份动态选择权重：2012年使用2012年权重，2018年开始使用2018年权重，2020年开始使用2020年权重
    丢弃2012年以前的数据
    """
    if df_macro.empty or df_weights.empty:
        return pd.DataFrame()

    # 过滤数据，只保留2012年及以后的数据
    df_macro_filtered = filter_data_from_2012(df_macro)
    if df_macro_filtered.empty:
        return pd.DataFrame()

    # Create result dataframe with the same index as filtered macro data
    result_df = pd.DataFrame(index=df_macro_filtered.index)

    # Create grouping mappings
    export_groups, stream_groups = create_grouping_mappings(df_weights)

    # Create mapping from indicator name to weights row
    weights_mapping = {}
    for _, row in df_weights.iterrows():
        indicator_name = row['指标名称']
        if pd.notna(indicator_name):
            weights_mapping[indicator_name] = row

    # Calculate weighted sums for export dependency groups
    for group_name, indicators in export_groups.items():
        if indicators:  # Only if group has indicators
            # Initialize with NaN to preserve missing data - 不进行任何填补处理
            weighted_series = pd.Series(index=df_macro_filtered.index, dtype=float)

            for indicator in indicators:
                if indicator in df_macro_filtered.columns and indicator in weights_mapping:
                    # Convert to numeric, keep NaN values (no filling)
                    series = pd.to_numeric(df_macro_filtered[indicator], errors='coerce')
                    weights_row = weights_mapping[indicator]

                    # 对每个时间点使用对应年份的权重
                    for idx in df_macro_filtered.index:
                        if hasattr(idx, 'year'):  # 确保是日期索引
                            year = idx.year
                            weight = get_weight_for_year(weights_row, year)
                            if not pd.isna(series.loc[idx]) and weight > 0:
                                # 如果weighted_series在该位置是NaN，初始化为0
                                if pd.isna(weighted_series.loc[idx]):
                                    weighted_series.loc[idx] = 0.0
                                weighted_series.loc[idx] += series.loc[idx] * weight

            result_df[f'出口依赖_{group_name}'] = weighted_series

    # Calculate weighted sums for upstream/downstream groups
    for group_name, indicators in stream_groups.items():
        if indicators:  # Only if group has indicators
            # Initialize with NaN to preserve missing data - 不进行任何填补处理
            weighted_series = pd.Series(index=df_macro_filtered.index, dtype=float)

            for indicator in indicators:
                if indicator in df_macro_filtered.columns and indicator in weights_mapping:
                    # Convert to numeric, keep NaN values (no filling)
                    series = pd.to_numeric(df_macro_filtered[indicator], errors='coerce')
                    weights_row = weights_mapping[indicator]

                    # 对每个时间点使用对应年份的权重
                    for idx in df_macro_filtered.index:
                        if hasattr(idx, 'year'):  # 确保是日期索引
                            year = idx.year
                            weight = get_weight_for_year(weights_row, year)
                            if not pd.isna(series.loc[idx]) and weight > 0:
                                # 如果weighted_series在该位置是NaN，初始化为0
                                if pd.isna(weighted_series.loc[idx]):
                                    weighted_series.loc[idx] = 0.0
                                weighted_series.loc[idx] += series.loc[idx] * weight

            result_df[f'上中下游_{group_name}'] = weighted_series

    return result_df


def filter_data_by_time_range(df: pd.DataFrame, time_range: str, custom_start_date: Optional[str] = None, custom_end_date: Optional[str] = None) -> pd.DataFrame:
    """
    Filter dataframe by time range
    """
    if df.empty or time_range == "全部":
        return df.copy()
    
    filtered_df = df.copy()
    
    # Get the latest date in the dataset
    latest_date = df.index.max()
    
    if time_range == "自定义" and custom_start_date and custom_end_date:
        # Handle custom date range
        try:
            start_date = pd.to_datetime(custom_start_date + "-01")
            end_date = pd.to_datetime(custom_end_date + "-01") + pd.offsets.MonthEnd(0)
        except:
            start_date = df.index.min()
            end_date = df.index.max()
    else:
        # Calculate start date based on time range selection - from latest date backwards
        if time_range == "1年":
            start_date = latest_date - pd.DateOffset(years=1)
        elif time_range == "3年":
            start_date = latest_date - pd.DateOffset(years=3)
        elif time_range == "5年":
            start_date = latest_date - pd.DateOffset(years=5)
        elif time_range == "自定义":
            # Fallback for custom range when dates are not provided - use full range
            start_date = df.index.min()
            end_date = df.index.max()
        else:
            # Default fallback
            start_date = df.index.min()
            end_date = df.index.max()
        
        # Set end_date for non-custom ranges
        if time_range != "自定义":
            end_date = latest_date

    # Filter data using date range
    try:
        # Ensure the index is datetime
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)

        # Filter using boolean indexing - include data within date range
        if time_range == "自定义":
            mask = (df.index >= start_date) & (df.index <= end_date)
        else:
            mask = df.index >= start_date
        filtered_df = df.loc[mask]

    except Exception as e:
        # Fallback: if date filtering fails, use tail method
        if time_range == "1年":
            filtered_df = df.tail(12)
        elif time_range == "3年":
            filtered_df = df.tail(36)
        elif time_range == "5年":
            filtered_df = df.tail(60)
        else:
            filtered_df = df.copy()

    return filtered_df


def get_group_details(df_weights: pd.DataFrame, group_type: str) -> pd.DataFrame:
    """
    Get detailed information about industry groups for display
    
    Args:
        df_weights: Weights dataframe
        group_type: Either '出口依赖' or '上中下游'
    
    Returns:
        DataFrame with group details for display
    """
    if df_weights.empty or group_type not in df_weights.columns:
        return pd.DataFrame()
    
    # Create display dataframe
    display_data = []
    
    for group_name in df_weights[group_type].unique():
        if pd.notna(group_name):
            group_data = df_weights[df_weights[group_type] == group_name]
            
            for _, row in group_data.iterrows():
                if pd.notna(row['指标名称']) and pd.notna(row['权重']):
                    display_data.append({
                        '分组': group_name,
                        '行业名称': row['指标名称'],
                        '权重': f"{row['权重']:.4f}"
                    })
    
    return pd.DataFrame(display_data)
