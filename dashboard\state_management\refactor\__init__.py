# -*- coding: utf-8 -*-
"""
模块重构包
提供模块重构的基类和具体实现
"""

from .module_refactor_base import ModuleRefactorBase
from .dfm_module_refactor import DFMModuleRefactor
from .tools_module_refactor import ToolsModuleRefactor
from .preview_module_refactor import PreviewModuleRefactor
from .navigation_module_refactor import NavigationModuleRefactor

# 导入全局重构器管理器
from .global_refactor_manager import (
    get_global_dfm_refactor,
    get_global_tools_refactor,
    get_global_preview_refactor,
    get_global_navigation_refactor,
    reset_global_refactors
)

__all__ = [
    'ModuleRefactorBase',
    'DFMModuleRefactor',
    'ToolsModuleRefactor',
    'PreviewModuleRefactor',
    'NavigationModuleRefactor',
    # 全局重构器函数
    'get_global_dfm_refactor',
    'get_global_tools_refactor',
    'get_global_preview_refactor',
    'get_global_navigation_refactor',
    'reset_global_refactors'
]
