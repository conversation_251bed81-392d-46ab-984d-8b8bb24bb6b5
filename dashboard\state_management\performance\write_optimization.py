# -*- coding: utf-8 -*-
"""
状态写入性能优化
提供高性能的状态写入优化策略，包括批量写入、异步写入、写入缓冲等
"""

import time
import threading
import logging
import queue
from typing import Any, Dict, List, Set, Optional, Tuple, Union, Callable
from collections import defaultdict, deque
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, Future
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class WriteOperation:
    """写入操作"""
    key: str
    value: Any
    timestamp: float = field(default_factory=time.time)
    priority: int = 0  # 优先级，数字越大优先级越高
    callback: Optional[Callable[[bool], None]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WriteStats:
    """写入统计"""
    total_writes: int = 0
    batch_writes: int = 0
    async_writes: int = 0
    failed_writes: int = 0
    avg_write_time: float = 0.0
    avg_batch_size: float = 0.0
    buffer_flushes: int = 0


class WriteBuffer:
    """写入缓冲区"""
    
    def __init__(self, max_size: int = 100, flush_interval: float = 1.0):
        """
        初始化写入缓冲区
        
        Args:
            max_size: 最大缓冲区大小
            flush_interval: 刷新间隔（秒）
        """
        self.max_size = max_size
        self.flush_interval = flush_interval
        
        self._buffer: Dict[str, WriteOperation] = {}
        self._lock = threading.RLock()
        self._last_flush = time.time()
        
        # 自动刷新线程
        self._flush_timer = None
        self._stop_flush = threading.Event()
        self._start_flush_timer()
    
    def add(self, operation: WriteOperation) -> bool:
        """添加写入操作到缓冲区"""
        with self._lock:
            # 如果键已存在，更新为最新操作
            self._buffer[operation.key] = operation
            
            # 检查是否需要立即刷新
            if len(self._buffer) >= self.max_size:
                return True  # 需要刷新
            
            return False
    
    def get_pending_operations(self) -> List[WriteOperation]:
        """获取待处理的操作"""
        with self._lock:
            operations = list(self._buffer.values())
            self._buffer.clear()
            self._last_flush = time.time()
            return operations
    
    def should_flush(self) -> bool:
        """检查是否应该刷新"""
        with self._lock:
            if not self._buffer:
                return False
            
            # 检查时间间隔
            if time.time() - self._last_flush >= self.flush_interval:
                return True
            
            # 检查缓冲区大小
            if len(self._buffer) >= self.max_size:
                return True
            
            return False
    
    def size(self) -> int:
        """获取缓冲区大小"""
        with self._lock:
            return len(self._buffer)
    
    def _start_flush_timer(self):
        """启动刷新定时器"""
        def flush_worker():
            while not self._stop_flush.wait(self.flush_interval / 2):
                if self.should_flush():
                    # 通知需要刷新（由外部处理）
                    pass
        
        self._flush_thread = threading.Thread(target=flush_worker, daemon=True)
        self._flush_thread.start()
    
    def shutdown(self):
        """关闭缓冲区"""
        self._stop_flush.set()
        if hasattr(self, '_flush_thread'):
            self._flush_thread.join(timeout=2.0)


class WriteOptimizer:
    """状态写入优化器"""
    
    def __init__(self, state_manager, 
                 enable_batching: bool = True,
                 enable_async: bool = True,
                 enable_buffering: bool = True,
                 batch_size: int = 50,
                 buffer_size: int = 100,
                 flush_interval: float = 1.0):
        """
        初始化写入优化器
        
        Args:
            state_manager: 状态管理器实例
            enable_batching: 是否启用批量写入
            enable_async: 是否启用异步写入
            enable_buffering: 是否启用写入缓冲
            batch_size: 批量写入大小
            buffer_size: 缓冲区大小
            flush_interval: 刷新间隔
        """
        self.state_manager = state_manager
        self.enable_batching = enable_batching
        self.enable_async = enable_async
        self.enable_buffering = enable_buffering
        self.batch_size = batch_size
        
        # 写入缓冲区
        self.write_buffer = WriteBuffer(buffer_size, flush_interval) if enable_buffering else None
        
        # 异步写入队列
        self.async_queue: queue.Queue = queue.Queue() if enable_async else None
        
        # 批量写入队列
        self.batch_queue: deque = deque()
        self.batch_lock = threading.Lock()
        
        # 性能统计
        self.stats = WriteStats()
        self.stats_lock = threading.Lock()
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="WriteOptimizer")
        
        # 异步写入工作线程
        if enable_async:
            self._async_worker_thread = threading.Thread(target=self._async_write_worker, daemon=True)
            self._stop_async_worker = threading.Event()
            self._async_worker_thread.start()
        
        # 缓冲区刷新工作线程
        if enable_buffering:
            self._buffer_worker_thread = threading.Thread(target=self._buffer_flush_worker, daemon=True)
            self._stop_buffer_worker = threading.Event()
            self._buffer_worker_thread.start()
        
        logger.info(f"状态写入优化器初始化完成 - 批量: {enable_batching}, 异步: {enable_async}, 缓冲: {enable_buffering}")
    
    def optimized_set_state(self, key: str, value: Any, 
                           async_write: bool = None,
                           priority: int = 0,
                           callback: Optional[Callable[[bool], None]] = None) -> Union[bool, Future]:
        """优化的状态设置"""
        start_time = time.time()
        
        try:
            # 创建写入操作
            operation = WriteOperation(
                key=key,
                value=value,
                priority=priority,
                callback=callback
            )
            
            # 决定写入策略
            use_async = async_write if async_write is not None else self.enable_async
            
            if use_async and self.async_queue is not None:
                # 异步写入
                return self._async_write(operation)
            elif self.enable_buffering and self.write_buffer is not None:
                # 缓冲写入
                return self._buffered_write(operation)
            else:
                # 直接写入
                return self._direct_write(operation)
                
        finally:
            # 更新统计
            write_time = time.time() - start_time
            with self.stats_lock:
                self.stats.total_writes += 1
                self.stats.avg_write_time = (
                    (self.stats.avg_write_time * (self.stats.total_writes - 1) + write_time) /
                    self.stats.total_writes
                )
    
    def batch_set_states(self, state_dict: Dict[str, Any], 
                        async_write: bool = None) -> Union[bool, Future]:
        """批量设置状态"""
        if not self.enable_batching or len(state_dict) == 1:
            # 如果未启用批量写入或只有一个键，逐个写入
            results = []
            for key, value in state_dict.items():
                result = self.optimized_set_state(key, value, async_write)
                results.append(result)
            return all(r if isinstance(r, bool) else r.result() for r in results)
        
        start_time = time.time()
        
        # 创建批量写入操作
        operations = [
            WriteOperation(key=key, value=value)
            for key, value in state_dict.items()
        ]
        
        # 决定写入策略
        use_async = async_write if async_write is not None else self.enable_async
        
        if use_async and self.async_queue is not None:
            # 异步批量写入
            future = self.executor.submit(self._execute_batch_write, operations)
            with self.stats_lock:
                self.stats.async_writes += 1
            return future
        else:
            # 同步批量写入
            result = self._execute_batch_write(operations)
            
            # 更新统计
            write_time = time.time() - start_time
            with self.stats_lock:
                self.stats.batch_writes += 1
                self.stats.avg_batch_size = (
                    (self.stats.avg_batch_size * (self.stats.batch_writes - 1) + len(operations)) /
                    self.stats.batch_writes
                )
            
            return result
    
    def flush_buffer(self) -> bool:
        """手动刷新缓冲区"""
        if not self.enable_buffering or self.write_buffer is None:
            return True
        
        operations = self.write_buffer.get_pending_operations()
        if not operations:
            return True
        
        result = self._execute_batch_write(operations)
        
        with self.stats_lock:
            self.stats.buffer_flushes += 1
        
        logger.debug(f"手动刷新缓冲区: {len(operations)}个操作")
        return result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.stats_lock:
            stats = self.stats.__dict__.copy()
        
        # 添加额外统计信息
        stats.update({
            'buffer_size': self.write_buffer.size() if self.write_buffer else 0,
            'async_queue_size': self.async_queue.qsize() if self.async_queue else 0,
            'batch_queue_size': len(self.batch_queue),
            'success_rate': (
                (stats['total_writes'] - stats['failed_writes']) / stats['total_writes']
                if stats['total_writes'] > 0 else 1.0
            )
        })
        
        return stats
    
    def _async_write(self, operation: WriteOperation) -> Future:
        """异步写入"""
        future = self.executor.submit(self._execute_single_write, operation)
        
        with self.stats_lock:
            self.stats.async_writes += 1
        
        return future
    
    def _buffered_write(self, operation: WriteOperation) -> bool:
        """缓冲写入"""
        need_flush = self.write_buffer.add(operation)
        
        if need_flush:
            # 立即刷新
            return self.flush_buffer()
        
        return True  # 已添加到缓冲区
    
    def _direct_write(self, operation: WriteOperation) -> bool:
        """直接写入"""
        return self._execute_single_write(operation)
    
    def _execute_single_write(self, operation: WriteOperation) -> bool:
        """执行单个写入操作"""
        try:
            result = self.state_manager._original_set_state(operation.key, operation.value)
            
            # 调用回调
            if operation.callback:
                operation.callback(result)
            
            return result
            
        except Exception as e:
            logger.error(f"写入状态失败: {operation.key} - {e}")
            
            with self.stats_lock:
                self.stats.failed_writes += 1
            
            # 调用回调
            if operation.callback:
                operation.callback(False)
            
            return False
    
    def _execute_batch_write(self, operations: List[WriteOperation]) -> bool:
        """执行批量写入操作"""
        if not operations:
            return True
        
        try:
            # 如果状态管理器支持批量写入，使用批量接口
            if hasattr(self.state_manager, 'batch_set_state'):
                state_dict = {op.key: op.value for op in operations}
                result = self.state_manager.batch_set_state(state_dict)
                
                # 调用所有回调
                for operation in operations:
                    if operation.callback:
                        operation.callback(result)
                
                return result
            else:
                # 并行写入
                with ThreadPoolExecutor(max_workers=min(len(operations), 8)) as executor:
                    futures = [
                        executor.submit(self._execute_single_write, operation)
                        for operation in operations
                    ]
                    
                    results = [future.result() for future in futures]
                    return all(results)
                    
        except Exception as e:
            logger.error(f"批量写入失败: {e}")
            
            with self.stats_lock:
                self.stats.failed_writes += len(operations)
            
            # 调用所有回调
            for operation in operations:
                if operation.callback:
                    operation.callback(False)
            
            return False
    
    def _async_write_worker(self):
        """异步写入工作线程"""
        while not self._stop_async_worker.is_set():
            try:
                # 批量处理异步写入
                operations = []
                
                # 收集一批操作
                try:
                    # 等待第一个操作
                    operation = self.async_queue.get(timeout=1.0)
                    operations.append(operation)
                    
                    # 收集更多操作（非阻塞）
                    while len(operations) < self.batch_size:
                        try:
                            operation = self.async_queue.get_nowait()
                            operations.append(operation)
                        except queue.Empty:
                            break
                            
                except queue.Empty:
                    continue
                
                # 执行批量写入
                if operations:
                    self._execute_batch_write(operations)
                    
                    # 标记任务完成
                    for _ in operations:
                        self.async_queue.task_done()
                        
            except Exception as e:
                logger.error(f"异步写入工作线程错误: {e}")
    
    def _buffer_flush_worker(self):
        """缓冲区刷新工作线程"""
        while not self._stop_buffer_worker.is_set():
            try:
                time.sleep(0.1)  # 100ms检查间隔
                
                if self.write_buffer and self.write_buffer.should_flush():
                    self.flush_buffer()
                    
            except Exception as e:
                logger.error(f"缓冲区刷新工作线程错误: {e}")
    
    def shutdown(self):
        """关闭优化器"""
        # 停止工作线程
        if hasattr(self, '_stop_async_worker'):
            self._stop_async_worker.set()
        if hasattr(self, '_stop_buffer_worker'):
            self._stop_buffer_worker.set()
        
        # 刷新所有待处理的写入
        if self.write_buffer:
            self.flush_buffer()
            self.write_buffer.shutdown()
        
        # 等待异步队列完成
        if self.async_queue:
            self.async_queue.join()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 等待工作线程结束
        if hasattr(self, '_async_worker_thread'):
            self._async_worker_thread.join(timeout=5.0)
        if hasattr(self, '_buffer_worker_thread'):
            self._buffer_worker_thread.join(timeout=5.0)
        
        logger.info("状态写入优化器已关闭")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except Exception:
            pass


def integrate_write_optimization(state_manager) -> WriteOptimizer:
    """将写入优化集成到状态管理器中"""
    optimizer = WriteOptimizer(state_manager)
    
    # 保存原始方法
    if not hasattr(state_manager, '_original_set_state'):
        state_manager._original_set_state = state_manager.set_state
    
    # 替换为优化版本
    state_manager.set_state = optimizer.optimized_set_state
    state_manager.batch_set_states = optimizer.batch_set_states
    state_manager._write_optimizer = optimizer
    
    logger.info("状态写入优化已集成到状态管理器")
    return optimizer


# 导出的公共接口
__all__ = [
    'WriteOptimizer',
    'WriteBuffer',
    'WriteOperation',
    'WriteStats',
    'integrate_write_optimization'
]
