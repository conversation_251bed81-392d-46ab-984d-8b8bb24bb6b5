# -*- coding: utf-8 -*-
"""
Tools模块适配器
提供工具模块的统一状态管理接口
"""

import logging
import threading
import time
import json
import pickle
import hashlib
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
from pathlib import Path
import pandas as pd
import numpy as np
from collections import defaultdict, deque

# 导入核心基础设施
from ..core.module_manager import ModuleManager
from ..core.data_flow_controller import DataFlowController
from ..core.state_synchronizer import StateSynchronizer
from ..core.performance_monitor import PerformanceMonitor
from ..core.error_handler import <PERSON>rror<PERSON><PERSON><PERSON>
from ..core.config_manager import ConfigManager
from ..core.logging_monitor import LoggingMonitor, MetricType


class ProcessingStage(Enum):
    """数据处理阶段枚举"""
    INITIALIZED = "initialized"
    LOADING = "loading"
    PREPROCESSING = "preprocessing"
    ANALYZING = "analyzing"
    TRANSFORMING = "transforming"
    VALIDATING = "validating"
    COMPLETED = "completed"
    ERROR = "error"


class CacheStrategy(Enum):
    """缓存策略枚举"""
    NONE = "none"
    MEMORY = "memory"
    DISK = "disk"
    HYBRID = "hybrid"
    SMART = "smart"


class ProcessingPriority(Enum):
    """处理优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class ToolType(Enum):
    """工具类型枚举"""
    TIME_SERIES_PRETREAT = "time_series_pretreat"
    TIME_SERIES_PROPERTY = "time_series_property"
    DATA_ANALYSIS = "data_analysis"
    DATA_TRANSFORMATION = "data_transformation"
    STATISTICAL_ANALYSIS = "statistical_analysis"
    VISUALIZATION = "visualization"


@dataclass
class ProcessingStep:
    """处理步骤"""
    step_id: str
    step_name: str
    tool_type: ToolType
    stage: ProcessingStage = ProcessingStage.INITIALIZED
    input_data: Optional[Any] = None
    output_data: Optional[Any] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_completed(self) -> bool:
        """检查步骤是否完成"""
        return self.stage == ProcessingStage.COMPLETED
    
    @property
    def is_error(self) -> bool:
        """检查步骤是否出错"""
        return self.stage == ProcessingStage.ERROR
    
    @property
    def is_running(self) -> bool:
        """检查步骤是否正在运行"""
        return self.stage in [
            ProcessingStage.LOADING,
            ProcessingStage.PREPROCESSING,
            ProcessingStage.ANALYZING,
            ProcessingStage.TRANSFORMING,
            ProcessingStage.VALIDATING
        ]


@dataclass
class ProcessingPipeline:
    """数据处理流水线"""
    pipeline_id: str
    pipeline_name: str
    steps: List[ProcessingStep] = field(default_factory=list)
    priority: ProcessingPriority = ProcessingPriority.NORMAL
    cache_strategy: CacheStrategy = CacheStrategy.SMART
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def total_steps(self) -> int:
        """总步骤数"""
        return len(self.steps)
    
    @property
    def completed_steps(self) -> int:
        """已完成步骤数"""
        return sum(1 for step in self.steps if step.is_completed)
    
    @property
    def progress(self) -> float:
        """进度百分比"""
        if self.total_steps == 0:
            return 0.0
        return self.completed_steps / self.total_steps
    
    @property
    def current_step(self) -> Optional[ProcessingStep]:
        """当前正在执行的步骤"""
        for step in self.steps:
            if step.is_running:
                return step
        return None
    
    @property
    def is_completed(self) -> bool:
        """检查流水线是否完成"""
        return all(step.is_completed for step in self.steps)
    
    @property
    def has_error(self) -> bool:
        """检查流水线是否有错误"""
        return any(step.is_error for step in self.steps)


@dataclass
class CacheItem:
    """缓存项"""
    cache_key: str
    data: Any
    created_at: datetime = field(default_factory=datetime.now)
    accessed_at: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    size: int = 0
    ttl: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.accessed_at = datetime.now()
        self.access_count += 1


@dataclass
class ToolsStats:
    """Tools模块统计信息"""
    total_pipelines: int = 0
    active_pipelines: int = 0
    completed_pipelines: int = 0
    failed_pipelines: int = 0
    total_steps: int = 0
    completed_steps: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    cache_size: int = 0
    total_processing_time: float = 0.0
    average_step_time: float = 0.0
    last_activity: Optional[datetime] = None

    # 添加缺少的统计属性
    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0


class ToolsAdapter:
    """Tools模块适配器 - 统一管理工具模块的状态和数据处理流水线"""
    
    def __init__(
        self,
        state_manager=None,  # 添加state_manager参数以兼容调用
        module_manager: ModuleManager = None,
        data_flow_controller: DataFlowController = None,
        state_synchronizer: StateSynchronizer = None,
        performance_monitor: PerformanceMonitor = None,
        error_handler: ErrorHandler = None,
        config_manager: ConfigManager = None,
        logging_monitor: LoggingMonitor = None
    ):
        """
        初始化Tools适配器

        Args:
            state_manager: 统一状态管理器（可选）
            module_manager: 模块管理器
            data_flow_controller: 数据流控制器
            state_synchronizer: 状态同步器
            performance_monitor: 性能监控器
            error_handler: 错误处理器
            config_manager: 配置管理器
            logging_monitor: 日志监控器
        """
        self.state_manager = state_manager  # 存储state_manager引用
        self.module_manager = module_manager
        self.data_flow_controller = data_flow_controller
        self.state_synchronizer = state_synchronizer
        self.performance_monitor = performance_monitor
        self.error_handler = error_handler
        self.config_manager = config_manager
        self.logging_monitor = logging_monitor

        # 添加缺少的属性
        self.core_manager = None  # 将在初始化后设置
        self.logger = logging.getLogger(__name__)

        # 初始化统计信息
        self.stats = ToolsStats()

        # 状态键映射 - 修复键冲突，实施新的命名约定
        self.state_key_mapping = {
            # 时间序列预处理工具 - 通用预处理功能
            'time_series_pretreat': {
                'uploaded_file': 'tools.pretreat.general.uploaded_file',
                'processed_data': 'tools.pretreat.general.processed_data',
                'processing_steps': 'tools.pretreat.general.processing_steps',
                'current_step': 'tools.pretreat.general.current_step',
                'progress': 'tools.pretreat.general.progress',
                'results': 'tools.pretreat.general.results',
                'parameters': 'tools.pretreat.general.parameters',

                # 时间序列清洗相关键 - 保持向后兼容
                'ts_tool_uploaded_file_name': 'tools.pretreat.clean.uploaded_file_name',
                'ts_tool_uploaded_file_content': 'tools.pretreat.clean.uploaded_file_content',
                'ts_tool_data_raw': 'tools.pretreat.clean.data_raw',
                'ts_tool_rows_to_skip_str': 'tools.pretreat.clean.rows_to_skip_str',
                'ts_tool_header_row_str': 'tools.pretreat.clean.header_row_str',
                'ts_tool_data_processed': 'tools.pretreat.clean.data_processed',
                'ts_tool_processing_applied': 'tools.pretreat.clean.processing_applied',
                'ts_tool_preview_rows': 'tools.pretreat.clean.preview_rows',
                'ts_tool_cols_to_keep': 'tools.pretreat.clean.cols_to_keep',
                'ts_tool_data_final': 'tools.pretreat.clean.data_final',
                'ts_tool_error_msg': 'tools.pretreat.clean.error_msg',
                'ts_tool_manual_time_col': 'tools.pretreat.clean.manual_time_col',
                'processed_header_duplicates': 'tools.pretreat.clean.header_duplicates',
                'ts_tool_auto_remove_duplicates': 'tools.pretreat.clean.auto_remove_duplicates',
                'ts_tool_manual_frequency': 'tools.pretreat.clean.manual_frequency',
                'ts_tool_rename_rules': 'tools.pretreat.clean.rename_rules',
                'ts_tool_filter_start_date': 'tools.pretreat.clean.filter_start_date',
                'ts_tool_filter_end_date': 'tools.pretreat.clean.filter_end_date',
                'ts_tool_merge_file_name': 'tools.pretreat.clean.merge_file_name',
                'ts_tool_merge_file_content': 'tools.pretreat.clean.merge_file_content',
                'ts_tool_merged_data': 'tools.pretreat.clean.merged_data',
                'ts_tool_merge_error': 'tools.pretreat.clean.merge_error',
                'ts_tool_time_col_info': 'tools.pretreat.clean.time_col_info',
                'ts_tool_horizontally_merged_data': 'tools.pretreat.clean.horizontally_merged_data',
                'ts_tool_horizontal_merge_error': 'tools.pretreat.clean.horizontal_merge_error',
                'ts_tool_last_operation_type': 'tools.pretreat.clean.last_operation_type',
                'ts_tool_complete_time_index': 'tools.pretreat.clean.complete_time_index',
                'ts_tool_completion_message': 'tools.pretreat.clean.completion_message',
                'ts_tool_frequency_completion_applied_flag': 'tools.pretreat.clean.frequency_completion_applied_flag',
                'ts_tool_sheet_names': 'tools.pretreat.clean.sheet_names',
                'ts_tool_selected_sheet_name': 'tools.pretreat.clean.selected_sheet_name',
                'staged_data': 'tools.pretreat.clean.staged_data',
                'ts_tool_alignment_report_items': 'tools.pretreat.clean.alignment_report_items',

                # 追加合并UI相关键
                'pending_upload_files': 'tools.pretreat.clean.pending_upload_files',
                'append_merge_uploader_key_suffix': 'tools.pretreat.clean.append_merge_uploader_key_suffix'
            },
            # 时间序列清洗工具 - 现在有独立的键空间
            'time_series_clean': {
                'uploaded_file': 'tools.clean.general.uploaded_file',
                'processed_data': 'tools.clean.general.processed_data',
                'processing_steps': 'tools.clean.general.processing_steps',
                'current_step': 'tools.clean.general.current_step',
                'progress': 'tools.clean.general.progress',
                'results': 'tools.clean.general.results',
                'parameters': 'tools.clean.general.parameters',

                # 时间序列清洗相关键 - 独立键空间
                'ts_tool_uploaded_file_name': 'tools.clean.ui.uploaded_file_name',
                'ts_tool_uploaded_file_content': 'tools.clean.ui.uploaded_file_content',
                'ts_tool_data_raw': 'tools.clean.ui.data_raw',
                'ts_tool_rows_to_skip_str': 'tools.clean.ui.rows_to_skip_str',
                'ts_tool_header_row_str': 'tools.clean.ui.header_row_str',
                'ts_tool_data_processed': 'tools.clean.ui.data_processed',
                'ts_tool_processing_applied': 'tools.clean.ui.processing_applied',
                'ts_tool_preview_rows': 'tools.clean.ui.preview_rows',
                'ts_tool_cols_to_keep': 'tools.clean.ui.cols_to_keep',
                'ts_tool_data_final': 'tools.clean.ui.data_final',
                'ts_tool_error_msg': 'tools.clean.ui.error_msg',
                'ts_tool_manual_time_col': 'tools.clean.ui.manual_time_col',
                'processed_header_duplicates': 'tools.clean.ui.header_duplicates',
                'ts_tool_auto_remove_duplicates': 'tools.clean.ui.auto_remove_duplicates',
                'ts_tool_manual_frequency': 'tools.clean.ui.manual_frequency',
                'ts_tool_rename_rules': 'tools.clean.ui.rename_rules',
                'ts_tool_filter_start_date': 'tools.clean.ui.filter_start_date',
                'ts_tool_filter_end_date': 'tools.clean.ui.filter_end_date',
                'ts_tool_merge_file_name': 'tools.clean.ui.merge_file_name',
                'ts_tool_merge_file_content': 'tools.clean.ui.merge_file_content',
                'ts_tool_merged_data': 'tools.clean.ui.merged_data',
                'ts_tool_merge_error': 'tools.clean.ui.merge_error',
                'ts_tool_time_col_info': 'tools.clean.ui.time_col_info',
                'ts_tool_horizontally_merged_data': 'tools.clean.ui.horizontally_merged_data',
                'ts_tool_horizontal_merge_error': 'tools.clean.ui.horizontal_merge_error',
                'ts_tool_last_operation_type': 'tools.clean.ui.last_operation_type',
                'ts_tool_complete_time_index': 'tools.clean.ui.complete_time_index',
                'ts_tool_completion_message': 'tools.clean.ui.completion_message',
                'ts_tool_frequency_completion_applied_flag': 'tools.clean.ui.frequency_completion_applied_flag',
                'ts_tool_sheet_names': 'tools.clean.ui.sheet_names',
                'ts_tool_selected_sheet_name': 'tools.clean.ui.selected_sheet_name',
                'staged_data': 'tools.clean.ui.staged_data',
                'ts_tool_alignment_report_items': 'tools.clean.ui.alignment_report_items',

                # 追加合并UI相关键
                'pending_upload_files': 'tools.clean.ui.pending_upload_files',
                'append_merge_uploader_key_suffix': 'tools.clean.ui.append_merge_uploader_key_suffix'
            },
            # 时间序列计算工具 - 新增独立键空间
            'time_series_compute': {
                'ts_compute_data': 'tools.compute.data.current_data',
                'ts_compute_file_name': 'tools.compute.data.file_name',
                'ts_compute_original_data': 'tools.compute.data.original_data',
                'ts_compute_selected_staged_key': 'tools.compute.data.selected_staged_key',
                'ts_compute_pivot_table': 'tools.compute.analysis.pivot_table',
                'ts_compute_calculations': 'tools.compute.analysis.calculations',
                'ts_compute_visualizations': 'tools.compute.analysis.visualizations',
                'ts_compute_parameters': 'tools.compute.config.parameters',
                'ts_compute_results': 'tools.compute.results.current_results'
            },
            # 时间序列属性分析工具 - 更新命名约定
            'time_series_property': {
                'input_data': 'tools.property.general.input_data',
                'analysis_results': 'tools.property.general.analysis_results',
                'selected_columns': 'tools.property.general.selected_columns',
                'analysis_type': 'tools.property.general.analysis_type',
                'parameters': 'tools.property.general.parameters',
                'charts': 'tools.property.general.charts',
                'statistics': 'tools.property.general.statistics'
            },
            # 属性分析工具 - 新的简化命名
            'property': {
                'input_data': 'tools.property.general.input_data',
                'analysis_results': 'tools.property.general.analysis_results',
                'selected_columns': 'tools.property.general.selected_columns',
                'analysis_type': 'tools.property.general.analysis_type',
                'parameters': 'tools.property.general.parameters',
                'charts': 'tools.property.general.charts',
                'statistics': 'tools.property.general.statistics'
            },
            # 数据比较工具 - 更新命名约定
            'data_comparison': {
                'dc_m1_uploader_key_suffix': 'tools.comparison.upload.m1_uploader_key_suffix',
                'dc_m1_pending_uploads': 'tools.comparison.upload.m1_pending_uploads',
                'dc_m2_selected_dataset_key': 'tools.comparison.analysis.m2_selected_dataset_key',
                'dc_m2_selected_variables': 'tools.comparison.analysis.m2_selected_variables',
                'dc_m2_comparison_results': 'tools.comparison.results.m2_comparison_results',
                'dc_m3_selected_datasets': 'tools.comparison.analysis.m3_selected_datasets',
                'dc_m3_comparison_results': 'tools.comparison.results.m3_comparison_results',
                'dc_m3_update_execution_report': 'tools.comparison.results.m3_update_execution_report'
            },
            # 通用工具状态 - 更新命名约定
            'common': {
                'active_tool': 'tools.common.general.active_tool',
                'processing_queue': 'tools.common.general.processing_queue',
                'cache_status': 'tools.common.general.cache_status',
                'performance_metrics': 'tools.common.general.performance_metrics'
            }
        }

        # 初始化其他必要属性
        self.cache = {}
        self.pipelines = {}

    def set_core_manager(self, core_manager):
        """设置核心状态管理器"""
        try:
            self.core_manager = core_manager

            # 验证核心管理器是否可用
            if core_manager is None:
                self.logger.warning("设置的核心管理器为None")
            elif not hasattr(core_manager, 'get_state'):
                self.logger.warning(f"核心管理器缺少get_state方法，类型: {type(core_manager)}")
            elif not hasattr(core_manager, 'set_state'):
                self.logger.warning(f"核心管理器缺少set_state方法，类型: {type(core_manager)}")
            else:
                self.logger.info(f"核心管理器设置成功，类型: {type(core_manager)}，具备基本功能")
                # 测试核心管理器的基本功能
                try:
                    test_key = "tools.test.core_manager_test"
                    test_value = "test_value"
                    set_success = core_manager.set_state(test_key, test_value)
                    get_value = core_manager.get_state(test_key)
                    if set_success and get_value == test_value:
                        self.logger.info("核心管理器功能测试通过")
                        # 清理测试数据
                        if hasattr(core_manager, 'delete_state'):
                            core_manager.delete_state(test_key)
                    else:
                        self.logger.warning(f"核心管理器功能测试失败: set_success={set_success}, get_value={get_value}")
                except Exception as test_e:
                    self.logger.warning(f"核心管理器功能测试异常: {test_e}")

            # 数据处理流水线管理
            self.pipelines: Dict[str, ProcessingPipeline] = {}
            self.active_pipelines: Dict[str, ProcessingPipeline] = {}

            # 智能缓存系统（优化配置以减少内存使用）
            self.cache: Dict[str, CacheItem] = {}
            self.cache_config = {
                'max_size': 100,  # 从1000减少到100
                'max_memory': 50 * 1024 * 1024,  # 从500MB减少到50MB
                'default_ttl': 1800,  # 从1小时减少到30分钟
                'cleanup_interval': 300  # 5分钟
            }

            # 处理步骤状态跟踪
            self.step_history: deque = deque(maxlen=1000)
            self.step_callbacks: Dict[str, List[Callable]] = defaultdict(list)

            # 统计信息
            self.stats = ToolsStats()

            # 移除本地状态存储，直接使用核心统一管理器

            # 线程锁
            self._lock = threading.RLock()

            # 注册到模块管理器
            if self.module_manager:
                self.module_manager.register_module(name="tools_adapter")

            # 记录初始化日志
            if self.logging_monitor:
                logger = self.logging_monitor.get_logger("tools_adapter")
                logger.info("ToolsAdapter initialized successfully")

        except Exception as e:
            self.logger.error(f"设置核心管理器失败: {e}")
            # 确保即使设置失败，适配器也能继续工作
            if not hasattr(self, 'core_manager'):
                self.core_manager = None
    
    def get_state(self, tool_type: str, key: str, default: Any = None) -> Any:
        """获取工具状态"""
        try:
            # 尝试使用映射键
            unified_key = None
            if tool_type in self.state_key_mapping:
                mapping = self.state_key_mapping[tool_type]
                if key in mapping:
                    unified_key = mapping[key]

            # 如果没有映射，使用默认键结构
            if unified_key is None:
                unified_key = f"tools.{tool_type}.{key}"

            # 从核心统一管理器获取状态
            if self.core_manager and hasattr(self.core_manager, 'get_state'):
                value = self.core_manager.get_state(unified_key, default)
            elif self.state_manager and hasattr(self.state_manager, 'get_state'):
                # 回退到统一状态管理器
                value = self.state_manager.get_state(unified_key, default)
            else:
                # 最后的回退机制：使用简单的内存存储
                if not hasattr(self, '_emergency_storage'):
                    self._emergency_storage = {}
                value = self._emergency_storage.get(unified_key, default)

            # 记录状态访问日志
            self._log_state_access('GET', unified_key, value)

            return value

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "get_state",
                "tool_type": tool_type,
                "key": key
            })
            return default
    
    def set_state(self, tool_type: str, key: str, value: Any) -> bool:
        """设置工具状态"""
        try:
            # 尝试使用映射键
            unified_key = None
            if tool_type in self.state_key_mapping:
                mapping = self.state_key_mapping[tool_type]
                if key in mapping:
                    unified_key = mapping[key]

            # 如果没有映射，使用默认键结构
            if unified_key is None:
                unified_key = f"tools.{tool_type}.{key}"

            # 验证键命名约定
            if not self._validate_key_naming(unified_key):
                self.logger.warning(f"键命名约定违规: {unified_key}")

            # 设置到核心统一管理器
            if self.core_manager and hasattr(self.core_manager, 'set_state'):
                success = self.core_manager.set_state(unified_key, value)
                if not success:
                    self.logger.error(f"核心管理器设置状态失败: {unified_key}")
                    return False
            elif self.state_manager and hasattr(self.state_manager, 'set_state'):
                # 回退到统一状态管理器
                success = self.state_manager.set_state(unified_key, value)
                if not success:
                    self.logger.warning(f"统一状态管理器设置状态失败: {unified_key}")
            else:
                # 最后的回退机制：使用简单的内存存储
                if not hasattr(self, '_emergency_storage'):
                    self._emergency_storage = {}
                self._emergency_storage[unified_key] = value
                success = True

            # 触发状态同步
            if self.state_synchronizer:
                self.state_synchronizer.trigger_sync(unified_key)

            # 记录性能指标
            if self.performance_monitor:
                self.performance_monitor.record_metric(
                    "tools_state_update",
                    1,
                    {"tool_type": tool_type, "key": key}
                )

            # 记录状态访问日志
            self._log_state_access('SET', unified_key, value)

            return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "set_state",
                "tool_type": tool_type,
                "key": key
            })
            return False

    def create_pipeline(
        self,
        pipeline_name: str,
        steps: List[Dict[str, Any]],
        priority: ProcessingPriority = ProcessingPriority.NORMAL,
        cache_strategy: CacheStrategy = CacheStrategy.SMART
    ) -> str:
        """创建数据处理流水线"""
        try:
            with self._lock:
                pipeline_id = f"pipeline_{int(time.time())}_{hash(pipeline_name) % 10000}"

                # 创建处理步骤
                processing_steps = []
                for i, step_config in enumerate(steps):
                    step = ProcessingStep(
                        step_id=f"{pipeline_id}_step_{i}",
                        step_name=step_config.get('name', f'Step {i+1}'),
                        tool_type=ToolType(step_config.get('tool_type', 'data_analysis')),
                        parameters=step_config.get('parameters', {})
                    )
                    processing_steps.append(step)

                # 创建流水线
                pipeline = ProcessingPipeline(
                    pipeline_id=pipeline_id,
                    pipeline_name=pipeline_name,
                    steps=processing_steps,
                    priority=priority,
                    cache_strategy=cache_strategy
                )

                self.pipelines[pipeline_id] = pipeline
                self.stats.total_pipelines += 1

                # 记录指标
                if self.logging_monitor:
                    self.logging_monitor.record_metric(
                        "tools_pipeline_created",
                        1,
                        MetricType.COUNTER,
                        tags={"priority": priority.value}
                    )

                return pipeline_id

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "create_pipeline",
                "pipeline_name": pipeline_name
            })
            return ""

    def start_pipeline(self, pipeline_id: str) -> bool:
        """启动数据处理流水线"""
        try:
            with self._lock:
                if pipeline_id not in self.pipelines:
                    return False

                pipeline = self.pipelines[pipeline_id]
                self.active_pipelines[pipeline_id] = pipeline
                self.stats.active_pipelines += 1

                # 开始执行第一个步骤
                if pipeline.steps:
                    first_step = pipeline.steps[0]
                    first_step.stage = ProcessingStage.LOADING
                    first_step.start_time = datetime.now()

                    # 添加到步骤历史
                    self.step_history.append({
                        'pipeline_id': pipeline_id,
                        'step_id': first_step.step_id,
                        'action': 'started',
                        'timestamp': datetime.now()
                    })

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "start_pipeline",
                "pipeline_id": pipeline_id
            })
            return False

    def update_step_status(
        self,
        pipeline_id: str,
        step_id: str,
        stage: ProcessingStage,
        output_data: Any = None,
        error_message: str = None
    ) -> bool:
        """更新处理步骤状态"""
        try:
            with self._lock:
                if pipeline_id not in self.pipelines:
                    return False

                pipeline = self.pipelines[pipeline_id]
                step = None

                # 找到对应的步骤
                for s in pipeline.steps:
                    if s.step_id == step_id:
                        step = s
                        break

                if not step:
                    return False

                # 更新步骤状态
                old_stage = step.stage
                step.stage = stage

                if output_data is not None:
                    step.output_data = output_data

                if error_message:
                    step.error_message = error_message

                # 如果步骤完成或出错，记录结束时间
                if stage in [ProcessingStage.COMPLETED, ProcessingStage.ERROR]:
                    step.end_time = datetime.now()
                    if step.start_time:
                        step.duration = (step.end_time - step.start_time).total_seconds()
                        self.stats.total_processing_time += step.duration

                # 更新统计信息
                if stage == ProcessingStage.COMPLETED:
                    self.stats.completed_steps += 1

                # 添加到步骤历史
                self.step_history.append({
                    'pipeline_id': pipeline_id,
                    'step_id': step_id,
                    'action': f'status_changed',
                    'old_stage': old_stage.value,
                    'new_stage': stage.value,
                    'timestamp': datetime.now()
                })

                # 触发回调
                self._trigger_step_callbacks(pipeline_id, step_id, stage)

                # 如果当前步骤完成，启动下一个步骤
                if stage == ProcessingStage.COMPLETED:
                    self._start_next_step(pipeline_id, step_id)

                # 如果流水线完成，更新状态
                if pipeline.is_completed:
                    self._complete_pipeline(pipeline_id)

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "update_step_status",
                "pipeline_id": pipeline_id,
                "step_id": step_id
            })
            return False

    def get_pipeline_status(self, pipeline_id: str) -> Optional[Dict[str, Any]]:
        """获取流水线状态"""
        try:
            if pipeline_id not in self.pipelines:
                return None

            pipeline = self.pipelines[pipeline_id]

            return {
                'pipeline_id': pipeline_id,
                'pipeline_name': pipeline.pipeline_name,
                'total_steps': pipeline.total_steps,
                'completed_steps': pipeline.completed_steps,
                'progress': pipeline.progress,
                'current_step': pipeline.current_step.step_name if pipeline.current_step else None,
                'is_completed': pipeline.is_completed,
                'has_error': pipeline.has_error,
                'created_at': pipeline.created_at,
                'updated_at': pipeline.updated_at
            }

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "get_pipeline_status",
                "pipeline_id": pipeline_id
            })
            return None

    def get_all_pipelines(self) -> Dict[str, Dict[str, Any]]:
        """获取所有流水线状态"""
        try:
            result = {}
            for pipeline_id in self.pipelines:
                status = self.get_pipeline_status(pipeline_id)
                if status:
                    result[pipeline_id] = status
            return result

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "get_all_pipelines"
            })
            return {}

    def add_step_callback(self, pipeline_id: str, step_id: str, callback: Callable):
        """添加步骤状态变化回调"""
        callback_key = f"{pipeline_id}:{step_id}"
        self.step_callbacks[callback_key].append(callback)

    def remove_step_callback(self, pipeline_id: str, step_id: str, callback: Callable):
        """移除步骤状态变化回调"""
        callback_key = f"{pipeline_id}:{step_id}"
        if callback_key in self.step_callbacks:
            try:
                self.step_callbacks[callback_key].remove(callback)
            except ValueError:
                pass

    def cache_data(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """缓存数据"""
        try:
            with self._lock:
                # 检查缓存大小限制
                if len(self.cache) >= self.cache_config['max_size']:
                    self._evict_cache_items()

                # 计算数据大小
                try:
                    size = len(pickle.dumps(data))
                except:
                    size = len(str(data))

                # 检查内存限制
                if size > self.cache_config['max_memory']:
                    return False

                # 创建缓存项
                cache_item = CacheItem(
                    cache_key=key,
                    data=data,
                    size=size,
                    ttl=ttl or self.cache_config['default_ttl']
                )

                self.cache[key] = cache_item
                self.stats.cache_size += size

                return True

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "cache_data",
                "key": key
            })
            return False

    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            with self._lock:
                if key not in self.cache:
                    self.stats.cache_misses += 1
                    return None

                cache_item = self.cache[key]

                # 检查是否过期
                if cache_item.is_expired():
                    del self.cache[key]
                    self.stats.cache_size -= cache_item.size
                    self.stats.cache_misses += 1
                    return None

                # 更新访问信息
                cache_item.touch()
                self.stats.cache_hits += 1

                return cache_item.data

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "get_cached_data",
                "key": key
            })
            self.stats.cache_misses += 1
            return None

    def clear_cache(self, pattern: str = None):
        """清空缓存"""
        try:
            with self._lock:
                if pattern is None:
                    # 清空所有缓存
                    self.cache.clear()
                    self.stats.cache_size = 0
                else:
                    # 清空匹配模式的缓存
                    keys_to_remove = [k for k in self.cache.keys() if pattern in k]
                    for key in keys_to_remove:
                        cache_item = self.cache[key]
                        self.stats.cache_size -= cache_item.size
                        del self.cache[key]

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "clear_cache",
                "pattern": pattern
            })

    def get_step_history(self, pipeline_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取步骤历史"""
        try:
            history = list(self.step_history)

            # 过滤特定流水线
            if pipeline_id:
                history = [h for h in history if h.get('pipeline_id') == pipeline_id]

            # 限制数量
            return history[-limit:]

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "get_step_history",
                "pipeline_id": pipeline_id
            })
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            # 更新平均步骤时间
            if self.stats.completed_steps > 0:
                self.stats.average_step_time = self.stats.total_processing_time / self.stats.completed_steps

            # 更新最后活动时间
            if self.step_history:
                self.stats.last_activity = self.step_history[-1]['timestamp']

            return {
                'total_pipelines': self.stats.total_pipelines,
                'active_pipelines': self.stats.active_pipelines,
                'completed_pipelines': self.stats.completed_pipelines,
                'failed_pipelines': self.stats.failed_pipelines,
                'total_steps': self.stats.total_steps,
                'completed_steps': self.stats.completed_steps,
                'cache_hits': self.stats.cache_hits,
                'cache_misses': self.stats.cache_misses,
                'cache_size': self.stats.cache_size,
                'cache_hit_rate': (
                    self.stats.cache_hits / (self.stats.cache_hits + self.stats.cache_misses)
                    if (self.stats.cache_hits + self.stats.cache_misses) > 0 else 0.0
                ),
                'total_processing_time': self.stats.total_processing_time,
                'average_step_time': self.stats.average_step_time,
                'last_activity': self.stats.last_activity,
                'active_pipeline_count': len(self.active_pipelines),
                'cached_items_count': len(self.cache)
            }

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "get_statistics"
            })
            return {}

    def _start_next_step(self, pipeline_id: str, completed_step_id: str):
        """启动下一个处理步骤"""
        try:
            pipeline = self.pipelines[pipeline_id]

            # 找到已完成步骤的索引
            completed_index = -1
            for i, step in enumerate(pipeline.steps):
                if step.step_id == completed_step_id:
                    completed_index = i
                    break

            # 启动下一个步骤
            if completed_index >= 0 and completed_index + 1 < len(pipeline.steps):
                next_step = pipeline.steps[completed_index + 1]
                next_step.stage = ProcessingStage.LOADING
                next_step.start_time = datetime.now()

                # 将前一步的输出作为下一步的输入
                if completed_index >= 0:
                    prev_step = pipeline.steps[completed_index]
                    next_step.input_data = prev_step.output_data

                # 添加到步骤历史
                self.step_history.append({
                    'pipeline_id': pipeline_id,
                    'step_id': next_step.step_id,
                    'action': 'auto_started',
                    'timestamp': datetime.now()
                })

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "_start_next_step",
                "pipeline_id": pipeline_id,
                "completed_step_id": completed_step_id
            })

    def _complete_pipeline(self, pipeline_id: str):
        """完成流水线处理"""
        try:
            with self._lock:
                if pipeline_id in self.active_pipelines:
                    del self.active_pipelines[pipeline_id]
                    self.stats.active_pipelines -= 1
                    self.stats.completed_pipelines += 1

                # 记录指标
                if self.logging_monitor:
                    self.logging_monitor.record_metric(
                        "tools_pipeline_completed",
                        1,
                        MetricType.COUNTER
                    )

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "_complete_pipeline",
                "pipeline_id": pipeline_id
            })

    def _trigger_step_callbacks(self, pipeline_id: str, step_id: str, stage: ProcessingStage):
        """触发步骤回调"""
        try:
            callback_key = f"{pipeline_id}:{step_id}"
            if callback_key in self.step_callbacks:
                for callback in self.step_callbacks[callback_key]:
                    try:
                        callback(pipeline_id, step_id, stage)
                    except Exception as e:
                        self.error_handler.handle_error(e, {
                            "module": "tools_adapter",
                            "function": "_trigger_step_callbacks",
                            "callback_error": True
                        })

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "_trigger_step_callbacks",
                "pipeline_id": pipeline_id,
                "step_id": step_id
            })

    def _evict_cache_items(self):
        """驱逐缓存项"""
        try:
            with self._lock:
                # 按访问时间排序，移除最久未访问的项
                sorted_items = sorted(
                    self.cache.items(),
                    key=lambda x: x[1].accessed_at
                )

                # 移除最老的25%项目
                items_to_remove = len(sorted_items) // 4
                for i in range(items_to_remove):
                    key, cache_item = sorted_items[i]
                    self.stats.cache_size -= cache_item.size
                    del self.cache[key]

        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "tools_adapter",
                "function": "_evict_cache_items"
            })

    def get_all_keys(self) -> List[str]:
        """获取所有工具相关的状态键"""
        try:
            if self.core_manager and hasattr(self.core_manager, 'get_all_keys'):
                all_keys = self.core_manager.get_all_keys()
                return [key for key in all_keys if key.startswith('tools.')]
            elif self.state_manager and hasattr(self.state_manager, 'get_all_keys'):
                # 回退到统一状态管理器
                all_keys = self.state_manager.get_all_keys()
                return [key for key in all_keys if key.startswith('tools.')]
            else:
                # 如果都不可用，返回空列表
                self.logger.debug("核心管理器和统一状态管理器都不可用，返回空的工具状态键列表")
                return []
        except Exception as e:
            if self.error_handler:
                self.error_handler.handle_error(e, {
                    "module": "tools_adapter",
                    "function": "get_all_keys"
                })
            # 返回空列表而不是抛出错误，确保系统继续运行
            self.logger.debug(f"获取工具状态键失败: {str(e)}，返回空列表")
            return []

    def _validate_key_naming(self, key: str) -> bool:
        """验证键命名约定"""
        try:
            # 检查是否符合 tools.{module}.{submodule}.{key} 格式
            parts = key.split('.')
            if len(parts) < 4:
                return False

            if parts[0] != 'tools':
                return False

            # 检查模块名是否在允许的列表中
            allowed_modules = ['pretreat', 'clean', 'compute', 'property', 'comparison', 'common', 'analysis']
            if parts[1] not in allowed_modules:
                self.logger.warning(f"未知模块: {parts[1]}")

            return True

        except Exception as e:
            self.logger.error(f"键验证失败: {e}")
            return False

    def _log_state_access(self, operation: str, key: str, value: Any) -> None:
        """记录状态访问日志"""
        try:
            # 只在调试模式下记录详细日志
            if self.logger.isEnabledFor(logging.DEBUG):
                value_type = type(value).__name__
                value_size = len(str(value)) if value is not None else 0

                self.logger.debug(f"状态访问 - {operation}: {key}, 类型: {value_type}, 大小: {value_size}")

                # 记录到性能监控
                if self.performance_monitor:
                    self.performance_monitor.record_metric(
                        f"tools_state_{operation.lower()}",
                        1,
                        {"key": key, "value_type": value_type}
                    )
        except Exception as e:
            # 日志记录失败不应该影响主要功能
            pass

    def validate_state_consistency(self) -> Dict[str, Any]:
        """验证状态一致性"""
        try:
            all_keys = self.get_all_keys()

            validation_report = {
                'timestamp': datetime.now().isoformat(),
                'total_keys': len(all_keys),
                'issues': [],
                'modules': {},
                'naming_compliance': 0
            }

            # 按模块分组键
            for key in all_keys:
                parts = key.split('.')
                if len(parts) >= 2:
                    module = parts[1]
                    if module not in validation_report['modules']:
                        validation_report['modules'][module] = []
                    validation_report['modules'][module].append(key)
                else:
                    validation_report['issues'].append(f"键格式不正确: {key}")

            # 检查命名约定
            compliant_keys = 0
            for key in all_keys:
                if self._validate_key_naming(key):
                    compliant_keys += 1
                else:
                    validation_report['issues'].append(f"键命名约定违规: {key}")

            validation_report['naming_compliance'] = compliant_keys / len(all_keys) if all_keys else 1.0

            return validation_report

        except Exception as e:
            self.logger.error(f"状态一致性验证失败: {e}")
            return {'error': str(e)}

    def get_health_status(self) -> Dict[str, Any]:
        """获取适配器健康状态"""
        try:
            health_info = {
                'timestamp': datetime.now().isoformat(),
                'adapter_available': True,
                'core_manager_available': self.core_manager is not None and hasattr(self.core_manager, 'get_state'),
                'cache_size': len(getattr(self, 'cache', {})),
                'pipeline_count': len(getattr(self, 'pipelines', {})),
                'stats': {
                    'total_operations': getattr(self.stats, 'total_operations', 0),
                    'successful_operations': getattr(self.stats, 'successful_operations', 0),
                    'failed_operations': getattr(self.stats, 'failed_operations', 0),
                    'cache_hits': getattr(self.stats, 'cache_hits', 0),
                    'cache_misses': getattr(self.stats, 'cache_misses', 0)
                }
            }

            # 测试基本操作（通过适配器自身的方法）
            test_key = 'health_test'
            test_value = 'health_check'

            # 测试设置和获取
            set_success = self.set_state('common', test_key, test_value)
            get_value = self.get_state('common', test_key)
            delete_success = self.set_state('common', test_key, None)

            health_info.update({
                'set_operation': set_success,
                'get_operation': get_value == test_value,
                'delete_operation': delete_success,
                'overall_health': set_success and (get_value == test_value) and delete_success
            })

            return health_info

        except Exception as e:
            self.logger.error(f"获取健康状态失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'overall_health': False
            }


# 助手函数
def create_processing_step(
    step_name: str,
    tool_type: ToolType,
    parameters: Dict[str, Any] = None
) -> Dict[str, Any]:
    """创建处理步骤配置的助手函数"""
    return {
        'name': step_name,
        'tool_type': tool_type.value,
        'parameters': parameters or {}
    }


def create_time_series_pretreat_pipeline(
    pipeline_name: str,
    preprocessing_steps: List[str],
    parameters: Dict[str, Any] = None
) -> List[Dict[str, Any]]:
    """创建时间序列预处理流水线的助手函数"""
    steps = []

    # 数据加载步骤
    steps.append(create_processing_step(
        "数据加载",
        ToolType.TIME_SERIES_PRETREAT,
        {"action": "load_data", **(parameters or {})}
    ))

    # 预处理步骤
    for step_name in preprocessing_steps:
        steps.append(create_processing_step(
            step_name,
            ToolType.TIME_SERIES_PRETREAT,
            {"action": step_name.lower().replace(" ", "_"), **(parameters or {})}
        ))

    # 数据验证步骤
    steps.append(create_processing_step(
        "数据验证",
        ToolType.TIME_SERIES_PRETREAT,
        {"action": "validate_data", **(parameters or {})}
    ))

    return steps


def create_time_series_analysis_pipeline(
    pipeline_name: str,
    analysis_types: List[str],
    parameters: Dict[str, Any] = None
) -> List[Dict[str, Any]]:
    """创建时间序列分析流水线的助手函数"""
    steps = []

    # 数据准备步骤
    steps.append(create_processing_step(
        "数据准备",
        ToolType.TIME_SERIES_PROPERTY,
        {"action": "prepare_data", **(parameters or {})}
    ))

    # 分析步骤
    for analysis_type in analysis_types:
        steps.append(create_processing_step(
            f"{analysis_type}分析",
            ToolType.TIME_SERIES_PROPERTY,
            {"action": analysis_type.lower().replace(" ", "_"), **(parameters or {})}
        ))

    # 结果汇总步骤
    steps.append(create_processing_step(
        "结果汇总",
        ToolType.TIME_SERIES_PROPERTY,
        {"action": "summarize_results", **(parameters or {})}
    ))

    return steps


def create_batch_processing_pipeline(
    pipeline_name: str,
    data_sources: List[str],
    processing_actions: List[str],
    parameters: Dict[str, Any] = None
) -> List[Dict[str, Any]]:
    """创建批量处理流水线的助手函数"""
    steps = []

    # 批量数据加载
    for i, source in enumerate(data_sources):
        steps.append(create_processing_step(
            f"加载数据源 {i+1}",
            ToolType.DATA_ANALYSIS,
            {"action": "load_batch_data", "source": source, **(parameters or {})}
        ))

    # 批量处理
    for action in processing_actions:
        steps.append(create_processing_step(
            f"批量{action}",
            ToolType.DATA_TRANSFORMATION,
            {"action": f"batch_{action.lower().replace(' ', '_')}", **(parameters or {})}
        ))

    # 结果合并
    steps.append(create_processing_step(
        "结果合并",
        ToolType.DATA_ANALYSIS,
        {"action": "merge_results", **(parameters or {})}
    ))

    return steps
