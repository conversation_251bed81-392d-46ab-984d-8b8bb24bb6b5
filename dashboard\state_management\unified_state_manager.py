# -*- coding: utf-8 -*-
"""
统一状态管理器
整合所有适配器，提供统一的状态管理接口
"""

import threading
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime

# 导入核心基础设施
from .core.module_manager import ModuleManager
from .core.data_flow_controller import DataFlowController
from .core.state_synchronizer import StateSynchronizer
from .core.performance_monitor import PerformanceMonitor
from .core.error_handler import <PERSON>rror<PERSON>and<PERSON>
from .core.config_manager import ConfigManager
from .core.logging_monitor import LoggingMonitor

# 导入所有适配器（延迟导入以避免循环导入）
# from .adapters.preview_adapter import PreviewAdapter
# from .adapters.dfm_adapter import DFMAdapter
# from .adapters.tools_adapter import ToolsAdapter
# from .adapters.navigation_adapter import NavigationAdapter


class UnifiedStateManager:
    """统一状态管理器 - 整合所有适配器，提供统一的状态管理接口"""

    _instance: Optional['UnifiedStateManager'] = None
    _lock = threading.RLock()

    def __new__(cls, *args, **kwargs) -> 'UnifiedStateManager':
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(
        self,
        config_path: str = None,
        log_dir: str = "logs",
        enable_console_logging: bool = True,
        enable_system_monitoring: bool = True
    ):
        """
        初始化统一状态管理器

        Args:
            config_path: 配置文件路径
            log_dir: 日志目录
            enable_console_logging: 是否启用控制台日志
            enable_system_monitoring: 是否启用系统监控
        """
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return

        # 初始化核心基础设施
        self.module_manager = ModuleManager()
        self.data_flow_controller = DataFlowController(self.module_manager)
        self.state_synchronizer = StateSynchronizer()
        self.performance_monitor = PerformanceMonitor()
        self.error_handler = ErrorHandler()
        self.config_manager = ConfigManager(
            config_dirs=[config_path] if config_path else None,
            auto_reload=True
        )
        self.logging_monitor = LoggingMonitor(
            log_dir=log_dir,
            enable_console=enable_console_logging,
            enable_system_monitoring=enable_system_monitoring
        )
        
        # 设置 logger（在_initialize_adapters之前）
        self.logger = self.logging_monitor.get_logger("unified_state_manager")
        
        # 初始化所有适配器
        self.adapters = {}
        self._initialize_adapters()
        
        # 线程锁
        self._lock = threading.RLock()

        # 记录初始化日志
        self.logger.info("UnifiedStateManager initialized successfully")

        # 标记为已初始化
        self._initialized = True

        # 初始化性能优化组件
        self._init_performance_optimization()

    def isInitialized(self) -> bool:
        """检查是否已初始化"""
        return hasattr(self, '_initialized') and self._initialized

    def _init_performance_optimization(self):
        """初始化性能优化组件"""
        # 缓存系统
        self._cache_enabled = False
        self._state_cache = {}
        self._cache_stats = {'hits': 0, 'misses': 0, 'size': 0}

        # 性能监控
        self._performance_metrics = {
            'operation_count': 0,
            'total_response_time': 0.0,
            'average_response_time': 0.0,
            'memory_usage': 0.0
        }

        # 批量操作缓冲区
        self._batch_buffer = {}
        self._batch_size_limit = 1000

        # 并发安全管理
        self._init_concurrent_safety()

    def _init_concurrent_safety(self):
        """初始化并发安全组件"""
        try:
            from .core.concurrent_safety import ConcurrentSafetyManager
            from .core.thread_pool_manager import ThreadPoolManager

            # 初始化并发安全管理器
            self.concurrent_safety = ConcurrentSafetyManager(self)

            # 初始化线程池管理器
            self.thread_pool = ThreadPoolManager(
                max_workers=min(32, 8),  # 限制线程数
                thread_name_prefix="StateManager"
            )

            self.logger.info("Concurrent safety components initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize concurrent safety: {e}")
            # 创建空的占位符以避免属性错误
            self.concurrent_safety = None
            self.thread_pool = None

    def validate_state_consistency(self, key: str) -> bool:
        """验证状态一致性"""
        try:
            with self._lock:
                # 获取状态值
                value = self.get_state(key)

                # 如果状态不存在，返回False
                if value is None:
                    # 检查是否真的不存在还是值为None
                    core_manager = self.get_adapter('core')
                    if core_manager and hasattr(core_manager, 'has_state'):
                        if not core_manager.has_state(key):
                            return False
                    return True  # None值被认为是一致的

                # 检查数据类型一致性
                if isinstance(value, dict):
                    # 字典类型的一致性检查
                    return self._validate_dict_consistency(value)
                elif isinstance(value, list):
                    # 列表类型的一致性检查
                    return self._validate_list_consistency(value)
                else:
                    # 其他类型默认一致
                    return True

        except Exception as e:
            self.logger.error(f"State consistency validation failed for {key}: {e}")
            return False

    def _validate_dict_consistency(self, data: dict) -> bool:
        """验证字典数据一致性"""
        try:
            # 检查字典是否可以序列化（基本一致性检查）
            import json
            json.dumps(data, default=str)
            return True
        except (TypeError, ValueError):
            return False

    def _validate_list_consistency(self, data: list) -> bool:
        """验证列表数据一致性"""
        try:
            # 检查列表是否可以序列化（基本一致性检查）
            import json
            json.dumps(data, default=str)
            return True
        except (TypeError, ValueError):
            return False

    def _initialize_adapters(self):
        """初始化所有适配器"""
        try:
            # 创建公共参数字典
            common_params = {
                'state_manager': self,  # 添加state_manager参数
                'module_manager': self.module_manager,
                'data_flow_controller': self.data_flow_controller,
                'state_synchronizer': self.state_synchronizer,
                'performance_monitor': self.performance_monitor,
                'error_handler': self.error_handler,
                'config_manager': self.config_manager,
                'logging_monitor': self.logging_monitor
            }

            # 注册核心统一状态管理器（避免循环导入）
            try:
                from .core.unified_manager import UnifiedStateManager as CoreUnifiedManager
                self.adapters['core'] = CoreUnifiedManager()
                self.logger.info("Core unified manager initialized successfully")
            except ImportError as e:
                self.logger.warning(f"Failed to import CoreUnifiedManager: {e}")
                # 不使用fallback，继续初始化其他适配器

            # 初始化所有适配器（使用延迟导入）
            try:
                from .adapters.preview_adapter import PreviewAdapter
                self.adapters['preview'] = PreviewAdapter(**common_params)
            except ImportError as e:
                self.logger.warning(f"Failed to import PreviewAdapter: {e}")

            try:
                from .adapters.dfm_adapter import DFMAdapter
                self.adapters['dfm'] = DFMAdapter(**common_params)
            except ImportError as e:
                self.logger.warning(f"Failed to import DFMAdapter: {e}")

            try:
                from .adapters.tools_adapter import ToolsAdapter
                self.adapters['tools'] = ToolsAdapter(**common_params)
            except ImportError as e:
                self.logger.warning(f"Failed to import ToolsAdapter: {e}")

            try:
                from .adapters.navigation_adapter import NavigationAdapter
                self.adapters['navigation'] = NavigationAdapter(**common_params)
            except ImportError as e:
                self.logger.warning(f"Failed to import NavigationAdapter: {e}")

            # 记录成功日志
            self.logger.info(f"Successfully initialized {len(self.adapters)} adapters")

            # 设置核心管理器到需要的适配器
            self._setup_core_managers()

        except Exception as e:
            self.logger.error(f"Failed to initialize adapters: {e}")
            self.error_handler.handle_error(e, {
                "module": "unified_state_manager",
                "function": "_initialize_adapters"
            })

    def _setup_core_managers(self):
        """设置核心管理器到需要的适配器"""
        try:
            # 获取核心管理器
            core_manager = self.get_adapter('core')

            if core_manager is None:
                self.logger.warning("核心管理器不可用，跳过核心管理器设置")
                return

            # 设置核心管理器到tools_adapter
            tools_adapter = self.get_adapter('tools')
            if tools_adapter and hasattr(tools_adapter, 'set_core_manager'):
                try:
                    tools_adapter.set_core_manager(core_manager)
                    self.logger.info("成功设置核心管理器到tools_adapter")
                except Exception as e:
                    self.logger.error(f"设置核心管理器到tools_adapter失败: {e}")
            else:
                self.logger.warning("tools_adapter不可用或缺少set_core_manager方法")

            # 可以在这里添加其他需要核心管理器的适配器
            # 例如：dfm_adapter, preview_adapter等

        except Exception as e:
            self.logger.error(f"设置核心管理器失败: {e}")


    def get_adapter(self, adapter_name: str):
        """获取指定的适配器"""
        return self.adapters.get(adapter_name)
    
    def get_all_adapters(self) -> Dict[str, Any]:
        """获取所有适配器"""
        return self.adapters.copy()
    
    # DFM模块状态管理
    def get_dfm_state(self, key: str, default: Any = None) -> Any:
        """获取DFM模块状态"""
        dfm_adapter = self.get_adapter('dfm')
        if dfm_adapter:
            return dfm_adapter.get_state(key, default)
        return default
    
    def set_dfm_state(self, key: str, value: Any) -> bool:
        """设置DFM模块状态"""
        dfm_adapter = self.get_adapter('dfm')
        if dfm_adapter:
            return dfm_adapter.set_state(key, value)
        return False
    
    def get_dfm_model_config(self, model_id: str):
        """获取DFM模型配置"""
        dfm_adapter = self.get_adapter('dfm')
        if dfm_adapter:
            return dfm_adapter.get_model_config(model_id)
        return None
    
    def create_dfm_model(self, model_config: Dict[str, Any]) -> str:
        """创建DFM模型"""
        dfm_adapter = self.get_adapter('dfm')
        if dfm_adapter:
            return dfm_adapter.create_model(model_config)
        return ""
    
    # Tools模块状态管理
    def get_tools_state(self, tool_type: str, key: str, default: Any = None) -> Any:
        """获取Tools模块状态"""
        tools_adapter = self.get_adapter('tools')
        if tools_adapter:
            return tools_adapter.get_state(tool_type, key, default)
        return default
    
    def set_tools_state(self, tool_type: str, key: str, value: Any) -> bool:
        """设置Tools模块状态"""
        tools_adapter = self.get_adapter('tools')
        if tools_adapter:
            return tools_adapter.set_state(tool_type, key, value)
        return False
    
    def create_processing_pipeline(
        self,
        pipeline_name: str,
        steps: List[Dict[str, Any]],
        priority: str = "normal",
        cache_strategy: str = "smart"
    ) -> str:
        """创建数据处理流水线"""
        tools_adapter = self.get_adapter('tools')
        if tools_adapter:
            from .adapters.tools_adapter import ProcessingPriority, CacheStrategy
            return tools_adapter.create_pipeline(
                pipeline_name,
                steps,
                ProcessingPriority(priority),
                CacheStrategy(cache_strategy)
            )
        return ""
    
    def start_processing_pipeline(self, pipeline_id: str) -> bool:
        """启动数据处理流水线"""
        tools_adapter = self.get_adapter('tools')
        if tools_adapter:
            return tools_adapter.start_pipeline(pipeline_id)
        return False
    
    # Navigation模块状态管理
    def get_navigation_state(self, category: str, key: str, default: Any = None) -> Any:
        """获取Navigation模块状态"""
        nav_adapter = self.get_adapter('navigation')
        if nav_adapter:
            return nav_adapter.get_state(category, key, default)
        return default
    
    def set_navigation_state(self, category: str, key: str, value: Any) -> bool:
        """设置Navigation模块状态"""
        nav_adapter = self.get_adapter('navigation')
        if nav_adapter:
            return nav_adapter.set_state(category, key, value)
        return False
    
    def navigate_to(self, path: str, session_id: str = "default") -> bool:
        """导航到指定路径"""
        nav_adapter = self.get_adapter('navigation')
        if nav_adapter:
            return nav_adapter.navigate_to(path, session_id)
        return False
    
    def add_navigation_item(
        self,
        item_id: str,
        label: str,
        path: str,
        icon: str = None,
        parent_id: str = None,
        order: int = 0
    ) -> bool:
        """添加导航项"""
        nav_adapter = self.get_adapter('navigation')
        if nav_adapter:
            return nav_adapter.add_navigation_item(
                item_id, label, path, icon, parent_id, order
            )
        return False
    
    def get_navigation_tree(self, parent_id: str = "root") -> List[Dict[str, Any]]:
        """获取导航树"""
        nav_adapter = self.get_adapter('navigation')
        if nav_adapter:
            return nav_adapter.get_navigation_tree(parent_id)
        return []
    
    def set_user_preferences(
        self,
        user_id: str,
        theme_mode: str = None,
        navigation_mode: str = None,
        sidebar_collapsed: bool = None,
        custom_settings: Dict[str, Any] = None
    ) -> bool:
        """设置用户偏好"""
        nav_adapter = self.get_adapter('navigation')
        if nav_adapter:
            from .adapters.navigation_adapter import ThemeMode, NavigationMode
            theme = ThemeMode(theme_mode) if theme_mode else None
            nav_mode = NavigationMode(navigation_mode) if navigation_mode else None
            
            return nav_adapter.set_user_preferences(
                user_id, theme, nav_mode, sidebar_collapsed, custom_settings
            )
        return False

    # --- 通用状态管理接口 ---
    def get_state(self, key: str, default: Any = None) -> Any:
        """获取通用状态（带缓存和性能监控）"""
        import time
        start_time = time.time()

        try:
            # 首先尝试从缓存获取
            cached_value = self._get_from_cache(key)
            if cached_value is not None:
                return cached_value

            # 从核心管理器获取
            core_manager = self.get_adapter('core')
            if core_manager:
                value = core_manager.get_state(key, default)
                if value is not None:
                    self._set_to_cache(key, value)
                    return value

            # 如果核心管理器不可用，尝试从各个适配器获取
            for _, adapter in self.adapters.items():
                if hasattr(adapter, 'get_state'):
                    try:
                        value = adapter.get_state(key, None)
                        if value is not None:
                            self._set_to_cache(key, value)
                            return value
                    except:
                        continue

            return default

        finally:
            # 更新性能指标
            end_time = time.time()
            response_time = end_time - start_time
            self._performance_metrics['operation_count'] += 1
            self._performance_metrics['total_response_time'] += response_time

    def set_state(self, key: str, value: Any, is_initialization: bool = False) -> bool:
        """设置通用状态（带缓存和性能监控）"""
        # time已在顶部导入
        start_time = time.time()

        try:
            core_manager = self.get_adapter('core')
            if core_manager:
                success = core_manager.set_state(key, value, is_initialization=is_initialization)
                if success:
                    # 更新缓存
                    self._set_to_cache(key, value)
                return success

            # 如果核心管理器不可用，记录警告并返回False
            if self.logger:
                self.logger.warning(f"Core state manager not available for key: {key}")
            return False

        finally:
            # 更新性能指标
            end_time = time.time()
            response_time = end_time - start_time
            self._performance_metrics['operation_count'] += 1
            self._performance_metrics['total_response_time'] += response_time

    def silent_initialize_state(self, key: str, value: Any) -> bool:
        """静默初始化状态（不触发变化检测和重新运行）"""
        core_manager = self.get_adapter('core')
        if core_manager:
            return core_manager.silent_initialize_state(key, value)

        # 如果核心管理器不可用，记录警告并返回False
        if self.logger:
            self.logger.warning(f"Core state manager not available for silent initialization: {key}")
        return False

    def clear_state(self, key: str = None) -> bool:
        """清空状态"""
        core_manager = self.get_adapter('core')
        if core_manager:
            if key:
                return core_manager.delete_state(key)
            else:
                return core_manager.clear_all_states()
        
        return False

    def get_all_keys(self) -> List[str]:
        """获取所有状态键"""
        all_keys = []
        
        # 从核心管理器获取键
        core_manager = self.get_adapter('core')
        if core_manager and hasattr(core_manager, 'get_all_keys'):
            core_keys = core_manager.get_all_keys()
            if isinstance(core_keys, list):
                all_keys.extend(core_keys)
        
        # 从各个适配器获取键
        for adapter_name, adapter in self.adapters.items():
            if hasattr(adapter, 'get_all_keys'):
                try:
                    adapter_keys = adapter.get_all_keys()
                    if isinstance(adapter_keys, list):
                        # 添加适配器前缀
                        prefixed_keys = [f"{adapter_name}.{key}" for key in adapter_keys]
                        all_keys.extend(prefixed_keys)
                except:
                    continue
            elif hasattr(adapter, 'state_storage'):
                # 如果适配器有state_storage属性，直接获取键
                try:
                    storage_keys = list(adapter.state_storage.keys())
                    prefixed_keys = [f"{adapter_name}.{key}" for key in storage_keys]
                    all_keys.extend(prefixed_keys)
                except:
                    continue
        
        return list(set(all_keys))  # 去重并返回

    def remove_state(self, key: str) -> bool:
        """删除状态（兼容性方法）"""
        return self.clear_state(key)

    # Preview模块管理
    def get_preview_state(self, key: str, default: Any = None) -> Any:
        """获取Preview模块状态"""
        preview_adapter = self.get_adapter('preview')
        if preview_adapter:
            return preview_adapter.get_data(key, default)

        # 如果适配器不可用，记录警告并返回默认值
        if self.logger:
            self.logger.warning(f"Preview adapter not available for key: {key}")
        return default

    def set_preview_state(self, key: str, value: Any) -> bool:
        """设置Preview模块状态"""
        preview_adapter = self.get_adapter('preview')
        if preview_adapter:
            return preview_adapter.set_data(key, value)

        # 如果适配器不可用，记录警告并返回False
        if self.logger:
            self.logger.warning(f"Preview adapter not available for key: {key}")
        return False

    def clear_preview_state(self) -> bool:
        """清空Preview模块状态"""
        preview_adapter = self.get_adapter('preview')
        if preview_adapter:
            return preview_adapter.clear_data()

        # 如果适配器不可用，记录警告并返回False
        if self.logger:
            self.logger.warning("Preview adapter not available for clearing states")
        return False

    # 数据源管理
    def add_data_source(self, config: Dict[str, Any]) -> str:
        """添加数据源"""
        ds_adapter = self.get_adapter('data_source')
        if ds_adapter:
            return ds_adapter.add_data_source(config)
        return ""
    
    def query_data_source(self, source_id: str, query: Dict[str, Any]) -> Any:
        """查询数据源"""
        ds_adapter = self.get_adapter('data_source')
        if ds_adapter:
            return ds_adapter.query_data_source(source_id, query)
        return None
    

    
    # 存储管理
    def store_data(self, key: str, data: Any, storage_type: str = "file") -> bool:
        """存储数据到持久化存储（使用简单文件存储）"""
        try:
            import os
            import pickle

            # 禁用存储目录创建 - 使用内存存储
            # storage_dir = "storage/preview_data"
            # os.makedirs(storage_dir, exist_ok=True)

            # 禁用文件存储 - 直接使用内存存储
            # safe_key = key.replace(".", "_").replace("/", "_").replace("\\", "_")
            # file_path = os.path.join(storage_dir, f"{safe_key}.pkl")
            #
            # # 保存数据到文件
            # with open(file_path, 'wb') as f:
            #     pickle.dump(data, f)
            #
            # self.logger.info(f"Data stored successfully: {key} -> {file_path}")

            # 直接使用统一状态管理器存储
            return self.set_state(key, data)

        except Exception as e:
            self.logger.error(f"Failed to store data {key}: {e}")
            # 回退到统一状态管理器存储
            return self.set_state(key, data)
    
    def retrieve_data(self, key: str, default: Any = None, storage_type: str = "file") -> Any:
        """从持久化存储检索数据（使用简单文件存储）"""
        try:
            # os已在顶部导入
            import pickle

            # 使用键名作为文件名（替换特殊字符）
            safe_key = key.replace(".", "_").replace("/", "_").replace("\\", "_")
            storage_dir = "storage/preview_data"
            file_path = os.path.join(storage_dir, f"{safe_key}.pkl")

            # 检查文件是否存在
            if os.path.exists(file_path):
                # 从文件加载数据
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)

                self.logger.info(f"Data retrieved successfully: {key} <- {file_path}")
                return data
            else:
                self.logger.debug(f"No stored data found for key: {key}")
                return default

        except Exception as e:
            self.logger.error(f"Failed to retrieve data {key}: {e}")
            # 回退到统一状态管理器获取
            return self.get_state(key, default)
    

    
    # 统计信息和监控
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            'timestamp': datetime.now(),
            'adapters': {}
        }
        
        for adapter_name, adapter in self.adapters.items():
            try:
                if hasattr(adapter, 'get_statistics'):
                    stats['adapters'][adapter_name] = adapter.get_statistics()
            except Exception as e:
                self.error_handler.handle_error(e, {
                    "module": "unified_state_manager",
                    "function": "get_system_statistics",
                    "adapter_name": adapter_name
                })
        
        # 添加核心组件统计
        if hasattr(self.performance_monitor, 'get_stats'):
            stats['performance'] = self.performance_monitor.get_stats()
        
        if hasattr(self.data_flow_controller, 'get_stats'):
            stats['data_flow'] = self.data_flow_controller.get_stats()
        
        return stats
    
    def shutdown(self):
        """关闭统一状态管理器"""
        try:
            # 关闭所有适配器
            for _, adapter in self.adapters.items():
                if hasattr(adapter, 'shutdown'):
                    adapter.shutdown()
            
            # 关闭核心组件
            if hasattr(self.logging_monitor, 'shutdown'):
                self.logging_monitor.shutdown()
            
            logger = self.logging_monitor.get_logger("unified_state_manager")
            logger.info("UnifiedStateManager shutdown completed")
            
        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "unified_state_manager",
                "function": "shutdown"
            })

    # --- 性能优化方法 ---
    def enable_caching(self, enabled: bool = True):
        """启用或禁用缓存"""
        self._cache_enabled = enabled
        if not enabled:
            self.clear_cache()

    def clear_cache(self):
        """清理缓存"""
        with self._lock:
            self._state_cache.clear()
            self._cache_stats = {'hits': 0, 'misses': 0, 'size': 0}

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return self._cache_stats.copy()

    def _get_from_cache(self, key: str):
        """从缓存获取状态"""
        if not self._cache_enabled:
            return None

        if key in self._state_cache:
            self._cache_stats['hits'] += 1
            return self._state_cache[key]
        else:
            self._cache_stats['misses'] += 1
            return None

    def _set_to_cache(self, key: str, value):
        """设置状态到缓存"""
        if not self._cache_enabled:
            return

        self._state_cache[key] = value
        self._cache_stats['size'] = len(self._state_cache)

    def batch_set_states(self, state_dict: dict) -> bool:
        """批量设置状态"""
        try:
            with self._lock:
                success_count = 0
                for key, value in state_dict.items():
                    if self.set_state(key, value):
                        success_count += 1

                # 更新性能指标
                self._performance_metrics['operation_count'] += len(state_dict)

                return success_count == len(state_dict)
        except Exception as e:
            self.logger.error(f"Batch set states failed: {e}")
            return False

    def batch_get_states(self, keys: list) -> dict:
        """批量获取状态"""
        try:
            with self._lock:
                results = {}
                for key in keys:
                    results[key] = self.get_state(key)

                # 更新性能指标
                self._performance_metrics['operation_count'] += len(keys)

                return results
        except Exception as e:
            self.logger.error(f"Batch get states failed: {e}")
            return {}

    def get_performance_metrics(self) -> dict:
        """获取性能指标"""
        import psuti
        # os已在顶部导入

        try:
            # 更新内存使用情况
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            self._performance_metrics['memory_usage'] = memory_mb

            # 计算平均响应时间
            if self._performance_metrics['operation_count'] > 0:
                self._performance_metrics['average_response_time'] = (
                    self._performance_metrics['total_response_time'] /
                    self._performance_metrics['operation_count']
                )

            return self._performance_metrics.copy()
        except Exception as e:
            self.logger.error(f"Failed to get performance metrics: {e}")
            return self._performance_metrics.copy()

    def delete_state(self, key: str) -> bool:
        """删除状态"""
        try:
            with self._lock:
                # 从缓存中删除
                if key in self._state_cache:
                    del self._state_cache[key]
                    self._cache_stats['size'] = len(self._state_cache)

                # 从实际存储中删除
                core_manager = self.get_adapter('core')
                if core_manager and hasattr(core_manager, 'delete_state'):
                    return core_manager.delete_state(key)

                # 如果没有核心管理器，尝试直接删除
                if hasattr(self, 'session_state') and key in self.session_state:
                    del self.session_state[key]
                    return True

                return False
        except Exception as e:
            self.logger.error(f"Failed to delete state {key}: {e}")
            return False

    # --- 适配器管理方法 ---
    def register_adapter(self, name: str, adapter) -> bool:
        """
        注册适配器

        Args:
            name: 适配器名称
            adapter: 适配器实例

        Returns:
            是否注册成功
        """
        try:
            with self._lock:
                if name in self.adapters:
                    self.logger.warning(f"Adapter {name} already registered, replacing...")

                self.adapters[name] = adapter
                self.logger.info(f"Adapter {name} registered successfully")
                return True
        except Exception as e:
            self.logger.error(f"Failed to register adapter {name}: {e}")
            return False

    def get_adapter(self, name: str):
        """
        获取适配器

        Args:
            name: 适配器名称

        Returns:
            适配器实例或None
        """
        return self.adapters.get(name)

    def list_adapters(self) -> list:
        """
        列出所有已注册的适配器

        Returns:
            适配器名称列表
        """
        return list(self.adapters.keys())
