# -*- coding: utf-8 -*-
"""
主内容路由组件
提供主内容区域的路由和渲染功能
"""

import streamlit as st
import logging
from typing import Dict, Any, Optional
from contextlib import contextmanager
# from ..utils.debug_helpers import debug_navigation  # 移除调试导入
from ..utils.tab_detector import TabStateDetector

logger = logging.getLogger(__name__)


def force_navigation_state_sync(state_manager, main_module: str, sub_module: str = None):
    """
    强制同步导航状态，清除所有相关缓存

    Args:
        state_manager: 统一状态管理器
        main_module: 主模块名称
        sub_module: 子模块名称
    """
    if not state_manager:
        return

    try:
        # 设置导航状态
        state_manager.set_state('navigation.main_module', main_module)
        if sub_module:
            state_manager.set_state('navigation.sub_module', sub_module)

        # 清除所有相关缓存
        # 1. 清除按钮状态缓存
        state_manager.clear_state('ui.button_state_cache')
        state_manager.clear_state('ui.button_state_time')

        # 2. 清除工具模块缓存
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor
            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 清除按钮状态缓存
                cache_key = f'button_states_{main_module}'
                tools_refactor.clear_tools_state('ui_cache', f'button_states.{cache_key}')
                tools_refactor.clear_tools_state('ui_cache', 'button_states.last_update')
        except:
            pass

        # 3. 清除统一状态管理器中的缓存
        cache_keys_to_clear = [
            'ui.button_state_cache',
            'ui.button_state_time',
            'ui.navigation_cache',
            'ui.module_selector_cache'
        ]

        # 获取现有键，只清除存在的键
        existing_keys = set(state_manager.get_all_keys())
        for key in cache_keys_to_clear:
            if key in existing_keys:
                state_manager.clear_state(key)

        # 4. 强制更新按钮状态
        try:
            from dashboard.ui.utils.button_state_manager import clear_button_state_cache, update_button_state_cache
            clear_button_state_cache()

            # 获取模块选项并强制更新
            main_module_options = ['数据预览', '应用工具', '模型分析', '策略回测', '实时监控']
            update_button_state_cache(main_module_options, main_module)
        except:
            pass

        # print(f"[DEBUG] 强制同步导航状态: {main_module} -> {sub_module}")  # 移除调试输出

    except Exception as e:
        print(f"[ERROR] 强制同步导航状态失败: {e}")


def render_main_content(nav_manager: Any) -> Dict[str, Any]:
    """
    渲染主内容区域

    Args:
        nav_manager: 导航管理器

    Returns:
        Dict[str, Any]: 渲染结果
    """
    # 获取当前导航状态
    current_main_module = nav_manager.get_current_main_module() if nav_manager else '数据预览'
    current_sub_module = nav_manager.get_current_sub_module() if nav_manager else None

    # 清理之前模块的状态残留
    _clear_previous_module_state(current_main_module)
    
    # debug_navigation(
    #     "主内容路由",
    #     f"渲染主内容 - 主模块: {current_main_module}, 子模块: {current_sub_module}"
    # )  # 移除调试输出
    
    # 获取内容配置
    content_config = get_content_config(current_main_module, current_sub_module)
    
    if not validate_content_config(content_config):
        st.error("内容配置无效")
        return {
            'main_module': current_main_module,
            'sub_module': current_sub_module,
            'content_type': 'error',
            'status': 'error'
        }
    
    # 创建内容容器
    with create_content_container():
        # 移除内容头部渲染，直接路由到具体内容
        # render_content_header(content_config)  # 已移除

        # 路由到具体内容
        content_result = route_to_content(content_config, nav_manager)
    
    return {
        'main_module': current_main_module,
        'sub_module': current_sub_module,
        'content_type': content_config['content_type'],
        'status': 'success',
        'content_result': content_result
    }


def get_content_config(main_module: str, sub_module: Optional[str] = None) -> Dict[str, Any]:
    """
    获取内容配置
    
    Args:
        main_module: 主模块名称
        sub_module: 子模块名称
        
    Returns:
        Dict[str, Any]: 内容配置
    """
    # 基础配置
    config = {
        'title': main_module,
        'icon': get_module_icon(main_module),
        'description': get_module_description(main_module, sub_module),
        'main_module': main_module,
        'sub_module': sub_module
    }
    
    # 根据主模块设置内容类型
    if main_module == '数据预览':
        config['content_type'] = 'data_preview'
    elif main_module == '监测分析':
        config['content_type'] = 'monitoring_analysis'
    elif main_module == '模型分析':
        config['content_type'] = 'model_analysis'
    elif main_module == '应用工具':
        config['content_type'] = 'application_tools'
    else:
        config['content_type'] = 'unknown'
    
    return config


def render_content_header(config: Dict[str, Any]) -> None:
    """
    渲染内容头部
    
    Args:
        config: 内容配置
    """
    # 渲染标题
    st.markdown(f"{config['icon']}")
    st.title(config['title'])
    
    # 渲染描述
    if config.get('description'):
        st.markdown(config['description'])


def route_to_content(config: Dict[str, Any], nav_manager: Any) -> Dict[str, Any]:
    """
    路由到具体内容

    Args:
        config: 内容配置
        nav_manager: 导航管理器

    Returns:
        Dict[str, Any]: 内容渲染结果
    """
    content_type = config['content_type']
    main_module = config.get('main_module')
    sub_module = config.get('sub_module')

    # 检测导航层次
    navigation_level = detect_navigation_level(main_module, sub_module, nav_manager)

    try:
        # 根据导航层次决定渲染内容
        if navigation_level == 'MAIN_MODULE_ONLY':
            # 第一层：只选择了主模块，显示子模块选择界面
            return render_module_selection_guide(main_module, 'sub_module')
        elif navigation_level == 'SUB_MODULE_ONLY':
            # 第二层：选择了子模块，但没有活跃的第三层tab
            # 对于应用工具模块，直接显示tab界面而不是功能选择指导
            if main_module == '应用工具' and sub_module in ['数据探索', '数据预处理']:
                return render_application_tools_content(sub_module, nav_manager)
            # 对于模型分析模块，直接显示tab界面而不是功能选择指导
            elif main_module == '模型分析' and sub_module:
                return render_model_analysis_content(sub_module, nav_manager)
            else:
                # 其他模块显示功能选择界面
                return render_module_selection_guide(main_module, 'function', sub_module)
        elif navigation_level == 'FUNCTION_ACTIVE':
            # 第三层：有活跃的功能tab，渲染具体内容
            if content_type == 'data_preview':
                return render_data_preview_content(sub_module, nav_manager)
            elif content_type == 'monitoring_analysis':
                return render_monitoring_analysis_content(sub_module, nav_manager)
            elif content_type == 'model_analysis':
                return render_model_analysis_content(sub_module, nav_manager)
            elif content_type == 'application_tools':
                return render_application_tools_content(sub_module, nav_manager)
            else:
                st.warning(f"未知的内容类型: {content_type}")
                return {'status': 'warning', 'message': f'未知的内容类型: {content_type}'}
        else:
            # 默认情况，显示欢迎页面
            return render_welcome_page(main_module, sub_module)

    except Exception as e:
        st.error(f"内容渲染失败: {e}")
        # debug_navigation("内容路由错误", f"渲染{content_type}失败: {e}")  # 移除调试输出
        return {'status': 'error', 'message': str(e)}


def render_data_preview_content(sub_module: Optional[str], nav_manager: Any) -> Dict[str, Any]:
    """
    渲染数据预览内容

    Args:
        sub_module: 子模块名称
        nav_manager: 导航管理器

    Returns:
        Dict[str, Any]: 渲染结果
    """
    if sub_module == '工业':
        # 渲染工业数据预览内容
        try:
            from dashboard.preview.industrial_data_tab import display_industrial_tabs
            from dashboard.preview.data_loader import extract_industry_name

            # 调用工业数据预览的主要功能
            display_industrial_tabs(extract_industry_name)

            return {'status': 'success', 'content_type': 'data_preview', 'sub_module': sub_module}

        except ImportError as e:
            st.error(f"工业数据预览模块导入失败: {e}")
            st.info("请从左侧侧边栏上传工业数据文件以开始数据预览")
            return {'status': 'error', 'content_type': 'data_preview', 'sub_module': sub_module, 'error': str(e)}
        except Exception as e:
            st.error(f"工业数据预览模块渲染失败: {e}")
            st.info("请尝试刷新页面或联系技术支持")
            return {'status': 'error', 'content_type': 'data_preview', 'sub_module': sub_module, 'error': str(e)}

    elif sub_module == '消费':
        # 消费数据预览功能 - 改进实现，避免循环渲染
        try:
            # 使用稳定的容器避免重复渲染
            with st.container():
                st.info("消费数据预览功能正在开发中")
                st.info("请从左侧侧边栏上传消费数据文件以开始数据预览")

                # 添加占位内容，避免空内容导致的渲染问题
                st.markdown("---")
                st.markdown("**功能特性预览：**")
                st.markdown("- 消费数据文件上传")
                st.markdown("- 消费趋势分析")
                st.markdown("- 消费结构分析")
                st.markdown("- 消费预测模型")

        except Exception as e:
            st.error(f"消费模块渲染错误: {e}")
            return {'status': 'error', 'content_type': 'data_preview', 'sub_module': sub_module, 'error': str(e)}

    elif sub_module:
        st.info(f"请从左侧侧边栏上传{sub_module}数据文件以开始数据预览")
    else:
        st.info("请选择一个子模块以开始数据预览")

    return {'status': 'success', 'content_type': 'data_preview', 'sub_module': sub_module}


def render_monitoring_analysis_content(sub_module: Optional[str], nav_manager: Any) -> Dict[str, Any]:
    """
    渲染监测分析内容

    Args:
        sub_module: 子模块名称
        nav_manager: 导航管理器

    Returns:
        Dict[str, Any]: 渲染结果
    """
    if sub_module == '工业':
        # 调用实际的工业分析模块
        try:
            from dashboard.analysis.industrial import render_industrial_analysis
            render_industrial_analysis(st)
        except ImportError as e:
            # 如果导入失败，显示错误信息和备用内容
            st.error(f"工业分析模块导入失败: {e}")
            st.markdown("### 📁 工业监测分析数据上传")
            st.markdown("上传一个Excel文件，同时支持宏观运行和企业经营分析")

            # 显示功能介绍
            st.markdown("欢迎使用工业监测分析模块！此模块提供：")
            st.markdown("**📈 工业增加值分析**")
            st.markdown("- PMI-工业增加值分析")
            st.markdown("- 出口依赖度分组分析")
            st.markdown("- 上中下游行业分析")

            st.markdown("**🏢 工业企业利润拆解分析**")
            st.markdown("- 盈利能力评估")
            st.markdown("- 财务指标监测")
            st.markdown("- 行业对比分析")

            st.markdown("**开始使用：** 请在左侧侧边栏上传包含工业数据的Excel模板文件。")

            # 数据格式要求
            with st.expander("📋 数据格式要求"):
                st.markdown("请确保上传的Excel文件包含以下工作表：")
                st.markdown("- 工业增加值数据")
                st.markdown("- PMI指数数据")
                st.markdown("- 企业利润数据")

    elif sub_module == '消费':
        st.info("消费监测分析功能正在开发中")
    else:
        st.info("请选择一个子模块以开始监测分析")

    return {'status': 'success', 'content_type': 'monitoring_analysis', 'sub_module': sub_module}


def render_model_analysis_content(sub_module: Optional[str], nav_manager: Any) -> Dict[str, Any]:
    """
    渲染模型分析内容

    Args:
        sub_module: 子模块名称
        nav_manager: 导航管理器

    Returns:
        Dict[str, Any]: 渲染结果
    """
    try:
        # 如果选择了DFM模型，显示DFM功能的tab界面
        if sub_module == "DFM 模型":
            # 状态同步已在dashboard.py主流程中完成，这里不再重复设置

            # 创建DFM功能标签页（移除Tab内容中的状态设置）
            tab1, tab2, tab3, tab4 = st.tabs(["📊 数据准备", "🏋️ 模型训练", "📈 模型分析", "📰 新闻分析"])

            with tab1:
                try:
                    from ui.pages.dfm import render_dfm_data_prep_tab
                    render_dfm_data_prep_tab(st)
                except ImportError as e:
                    st.error(f"数据准备组件加载失败: {e}")
                    st.write("文件上传组件加载失败")

            with tab2:
                try:
                    from ui.pages.dfm import render_dfm_train_model_tab
                    render_dfm_train_model_tab(st)
                except ImportError as e:
                    st.error(f"模型训练组件加载失败: {e}")
                    st.write("模型训练组件加载失败")

            with tab3:
                try:
                    from ui.pages.dfm import render_dfm_analysis_tab
                    render_dfm_analysis_tab(st)
                except ImportError as e:
                    st.error(f"模型分析组件加载失败: {e}")
                    st.write("模型分析组件加载失败")

            with tab4:
                try:
                    from ui.pages.dfm import render_dfm_news_analysis_tab
                    render_dfm_news_analysis_tab(st)
                except ImportError as e:
                    st.error(f"新闻分析组件加载失败: {e}")
                    st.write("新闻分析组件加载失败")
        else:
            st.info("请选择一个模型分析子模块以开始分析")

        return {'status': 'success', 'content_type': 'model_analysis', 'sub_module': sub_module}
    except Exception as e:
        st.error(f"加载DFM模块时出错: {str(e)}")
        return {'status': 'error', 'content_type': 'model_analysis', 'sub_module': sub_module, 'error': str(e)}


def render_application_tools_content(sub_module: Optional[str], nav_manager: Any) -> Dict[str, Any]:
    """
    渲染应用工具内容
    
    Args:
        sub_module: 子模块名称
        nav_manager: 导航管理器
        
    Returns:
        Dict[str, Any]: 渲染结果
    """
    if sub_module == '数据探索':
        # 渲染完整的数据探索界面
        try:
            # 导入数据探索组件
            from ..analysis.timeseries import (
                StationarityAnalysisComponent,
                UnifiedCorrelationAnalysisComponent
            )
            from ..sidebar import DataExplorationSidebar

            # 状态同步已在dashboard.py主流程中完成，这里不再重复设置

            # 渲染侧边栏数据上传
            sidebar = DataExplorationSidebar()
            uploaded_data = sidebar.render(st)

            # 创建分析标签页（移除Tab内容中的状态设置）
            tab1, tab2 = st.tabs(["📉 平稳性分析", "🔗 相关分析"])

            with tab1:
                stationarity_component = StationarityAnalysisComponent()
                stationarity_component.render(st, tab_index=0)

            with tab2:
                try:
                    unified_correlation_component = UnifiedCorrelationAnalysisComponent()
                    unified_correlation_component.render(st, tab_index=1)
                except Exception as e:
                    st.error(f"统一相关分析组件加载失败: {e}")
                    import traceback
                    st.code(traceback.format_exc())

            return {'status': 'success', 'content_type': 'data_exploration', 'sub_module': sub_module}
        except ImportError as e:
            st.error(f"数据探索模块导入失败: {e}")
            return {'status': 'error', 'message': f'数据探索模块导入失败: {e}'}
    
    elif sub_module == '数据预处理':
        # 直接创建数据预处理标签页，不显示标题和描述
        try:
            # 状态同步已在dashboard.py主流程中完成，这里不再重复设置

            # 创建数据预处理标签页（移除Tab内容中的状态设置）
            tab1, tab2, tab3, tab4 = st.tabs(["✨ 数据清洗", "🧮 变量计算", "🔄 数据追加与合并", "⚖️ 数据比较"])

            with tab1:
                render_data_cleaning_interface(st)

            with tab2:
                render_variable_calculation_interface(st)

            with tab3:
                render_data_merge_interface(st)

            with tab4:
                render_data_comparison_interface(st)

            return {'status': 'success', 'content_type': 'data_preprocessing', 'sub_module': sub_module}
        except ImportError as e:
            st.error(f"数据预处理模块导入失败: {e}")
            return {'status': 'error', 'message': f'数据预处理模块导入失败: {e}'}
    
    elif sub_module is None:
        st.info("请选择一个工具以开始使用")
        return {'status': 'success', 'content_type': 'tool_selection', 'sub_module': None}
    
    else:
        st.error(f"未知的应用工具子模块: {sub_module}")
        return {'status': 'error', 'message': f'未知的应用工具子模块: {sub_module}'}


@contextmanager
def create_content_container():
    """
    创建内容容器的上下文管理器
    
    Yields:
        内容容器上下文
    """
    with st.container():
        yield st.container()


def get_module_icon(main_module: str) -> str:
    """
    获取模块图标
    
    Args:
        main_module: 主模块名称
        
    Returns:
        str: 模块图标
    """
    icons = {
        '数据预览': '📊',
        '监测分析': '📈',
        '模型分析': '🤖',
        '应用工具': '🛠️'
    }
    return icons.get(main_module, '📋')


def get_module_description(main_module: str, sub_module: Optional[str] = None) -> str:
    """
    获取模块描述
    
    Args:
        main_module: 主模块名称
        sub_module: 子模块名称
        
    Returns:
        str: 模块描述
    """
    descriptions = {
        '数据预览': '查看和预览各类经济数据，支持工业和消费领域的数据展示',
        '监测分析': '对经济运行数据进行深度监测和分析，提供专业的分析报告',
        '模型分析': '使用先进的数学模型对经济数据进行建模和预测分析',
        '应用工具': '提供数据处理和分析的实用工具，支持数据清洗、探索和预处理'
    }
    
    base_desc = descriptions.get(main_module, '经济数据分析功能')
    
    if sub_module:
        return f"{base_desc} - {sub_module}"
    else:
        return base_desc


def validate_content_config(config: Optional[Dict[str, Any]]) -> bool:
    """
    验证内容配置的有效性
    
    Args:
        config: 内容配置
        
    Returns:
        bool: 配置是否有效
    """
    if not config or not isinstance(config, dict):
        return False
    
    required_fields = ['title', 'icon', 'description', 'content_type']
    for field in required_fields:
        if field not in config:
            return False
    
    return True


def _clear_previous_module_state(current_main_module: str) -> None:
    """
    清理之前模块的状态残留

    Args:
        current_main_module: 当前主模块名称
    """
    try:
        # 使用统一状态管理器进行清理
        from dashboard.state_management import get_unified_manager

        state_manager = get_unified_manager()
        if state_manager:
            # 定义需要清理的状态键模式
            state_patterns_to_clear = [
                'temp_selected_',
                'navigate_to_',
                '_preview_data',
                '_processed_data',
                '_analysis_result'
            ]

            # 模块特定的状态清理
            module_specific_states = {
                '数据预览': ['monitoring_', 'model_', 'tools_'],
                '监测分析': ['preview_', 'model_', 'tools_'],
                '模型分析': ['preview_', 'monitoring_', 'tools_'],
                '应用工具': ['preview_', 'monitoring_', 'model_']
            }

            # 获取需要清理的模块前缀
            prefixes_to_clear = module_specific_states.get(current_main_module, [])

            # 获取所有状态键
            all_keys = state_manager.get_all_keys()

            # 清理状态
            keys_to_remove = []
            for key in all_keys:
                key_str = str(key)

                # 清理通用状态模式
                for pattern in state_patterns_to_clear:
                    if pattern in key_str:
                        keys_to_remove.append(key)
                        break

                # 清理模块特定状态
                for prefix in prefixes_to_clear:
                    if key_str.startswith(prefix):
                        keys_to_remove.append(key)
                        break

            # 执行清理
            for key in keys_to_remove:
                state_manager.clear_state(key)

            # 静默处理状态键清理，避免重复日志
            pass
        else:
            logger.warning("统一状态管理器不可用，跳过状态清理")

    except Exception as e:
        # 静默处理状态清理失败，避免重复错误日志
        pass


def render_data_cleaning_interface(st_obj) -> Dict[str, Any]:
    """渲染数据清洗界面"""
    try:
        from ..preprocessing import DataCleanComponent

        # 创建数据清洗组件，不显示标题和描述
        data_clean_component = DataCleanComponent()

        # 渲染组件
        processed_data = data_clean_component.render(st_obj)

        return {'status': 'success', 'content_type': 'data_cleaning', 'processed_data': processed_data}
    except Exception as e:
        st_obj.error(f"数据清洗界面加载失败: {e}")
        return {'status': 'error', 'message': f'数据清洗界面加载失败: {e}'}


def render_variable_calculation_interface(st_obj) -> Dict[str, Any]:
    """渲染变量计算界面"""
    try:
        from dashboard.ui.components.preprocessing import DataComputeComponent

        # 创建变量计算组件，不显示标题和描述
        data_compute_component = DataComputeComponent()

        # 渲染组件
        processed_data = data_compute_component.render(st_obj)

        return {'status': 'success', 'content_type': 'variable_calculation', 'processed_data': processed_data}
    except Exception as e:
        st_obj.error(f"变量计算界面加载失败: {e}")
        return {'status': 'error', 'message': f'变量计算界面加载失败: {e}'}


def render_data_merge_interface(st_obj) -> Dict[str, Any]:
    """渲染数据合并界面"""
    try:
        # 简化的数据合并界面，不显示标题和描述
        st_obj.info("数据追加与合并功能正在开发中")
        st_obj.markdown("**功能特性预览：**")
        st_obj.markdown("- 多文件数据合并")
        st_obj.markdown("- 时间序列数据追加")
        st_obj.markdown("- 数据源对齐和匹配")

        return {'status': 'success', 'content_type': 'data_merge'}
    except Exception as e:
        st_obj.error(f"数据合并界面加载失败: {e}")
        return {'status': 'error', 'message': f'数据合并界面加载失败: {e}'}


def render_data_comparison_interface(st_obj) -> Dict[str, Any]:
    """渲染数据比较界面"""
    try:
        # 简化的数据比较界面，不显示标题和描述
        st_obj.info("数据比较功能正在开发中")
        st_obj.markdown("**功能特性预览：**")
        st_obj.markdown("- 数据集差异分析")
        st_obj.markdown("- 变化趋势对比")
        st_obj.markdown("- 统计指标比较")

        return {'status': 'success', 'content_type': 'data_comparison'}
    except Exception as e:
        st_obj.error(f"数据比较界面加载失败: {e}")
        return {'status': 'error', 'message': f'数据比较界面加载失败: {e}'}


def detect_navigation_level(main_module: str, sub_module: Optional[str], nav_manager: Any) -> str:
    """
    检测当前导航层次

    Args:
        main_module: 主模块名称
        sub_module: 子模块名称
        nav_manager: 导航管理器

    Returns:
        str: 导航层次 ('MAIN_MODULE_ONLY', 'SUB_MODULE_ONLY', 'FUNCTION_ACTIVE')
    """
    try:
        # 如果没有子模块，说明只选择了主模块
        if not sub_module:
            return 'MAIN_MODULE_ONLY'

        # 对于数据预览模块，如果已选择子模块，直接进入功能层
        if main_module == '数据预览' and sub_module in ['工业', '消费']:
            # debug_navigation("导航层次检测", f"数据预览模块 {sub_module} 直接进入功能层")  # 移除调试输出
            return 'FUNCTION_ACTIVE'

        # 对于监测分析模块，如果已选择子模块，直接进入功能层
        if main_module == '监测分析' and sub_module in ['工业', '消费']:
            # debug_navigation("导航层次检测", f"监测分析模块 {sub_module} 直接进入功能层")  # 移除调试输出
            return 'FUNCTION_ACTIVE'

        # 检查是否有活跃的第三层tab（主要用于应用工具模块）
        tab_detector = TabStateDetector()
        has_active_tab = tab_detector.has_active_tab(sub_module)

        if has_active_tab:
            return 'FUNCTION_ACTIVE'
        else:
            return 'SUB_MODULE_ONLY'

    except Exception as e:
        # debug_navigation("导航层次检测错误", f"检测失败: {e}")  # 移除调试输出
        return 'SUB_MODULE_ONLY'  # 默认返回子模块层次


def render_module_selection_guide(main_module: str, guide_type: str, sub_module: Optional[str] = None) -> Dict[str, Any]:
    """
    渲染模块选择指导界面

    Args:
        main_module: 主模块名称
        guide_type: 指导类型 ('sub_module' 或 'function')
        sub_module: 子模块名称（当guide_type为'function'时需要）

    Returns:
        Dict[str, Any]: 渲染结果
    """
    if guide_type == 'sub_module':
        st.markdown("## 📂 请选择功能模块")
        st.info(f"您已选择主模块：**{main_module}**")
        st.markdown("请从左侧边栏选择具体的功能模块以继续。")

        # 显示可用的子模块提示
        if main_module == '应用工具':
            st.markdown("### 可用功能模块：")
            st.markdown("- 📊 **数据探索**：进行数据的平稳性分析、相关性分析等")
            st.markdown("- 🔧 **数据预处理**：数据清洗、变量计算、数据合并等")
        elif main_module == '监测分析':
            st.markdown("### 可用功能模块：")
            st.markdown("- 🏭 **工业**：工业数据监测分析")
            st.markdown("- 🛒 **消费**：消费数据监测分析")
        elif main_module == '模型分析':
            st.markdown("### 可用功能模块：")
            st.markdown("- 📈 **DFM相关模块**：动态因子模型分析")

    elif guide_type == 'function':
        st.markdown("## 🎯 请选择具体功能")
        st.info(f"您已选择：**{main_module}** > **{sub_module}**")
        st.markdown("请点击下方的功能标签页以开始使用。")

        # 显示可用功能的提示
        if sub_module == '数据探索':
            st.markdown("### 可用功能：")
            st.markdown("- 📊 **平稳性分析**：检验时间序列数据的平稳性")
            st.markdown("- 🔗 **相关性分析**：分析变量间的相关关系")
            st.markdown("- ⏱️ **领先滞后分析**：分析变量间的时间关系")
        elif sub_module == '数据预处理':
            st.markdown("### 可用功能：")
            st.markdown("- 🧹 **数据清洗**：处理缺失值、异常值等")
            st.markdown("- 🧮 **变量计算**：创建新变量、数据转换")
            st.markdown("- 🔗 **数据追加与合并**：合并多个数据源")
            st.markdown("- 📊 **数据比较**：比较不同数据集")

    return {
        'status': 'success',
        'content_type': 'selection_guide',
        'guide_type': guide_type,
        'main_module': main_module,
        'sub_module': sub_module
    }


def render_welcome_page(main_module: str, sub_module: Optional[str] = None) -> Dict[str, Any]:
    """
    渲染欢迎页面

    Args:
        main_module: 主模块名称
        sub_module: 子模块名称

    Returns:
        Dict[str, Any]: 渲染结果
    """
    st.markdown("# 🏠 欢迎使用HFTA系统")
    st.markdown("---")

    st.markdown("## 📋 系统功能概览")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 🔍 数据预览")
        st.markdown("- 工业数据预览")
        st.markdown("- 消费数据预览")

        st.markdown("### 🛠️ 应用工具")
        st.markdown("- 数据探索分析")
        st.markdown("- 数据预处理")

    with col2:
        st.markdown("### 📊 监测分析")
        st.markdown("- 工业数据监测")
        st.markdown("- 消费数据监测")

        st.markdown("### 🧠 模型分析")
        st.markdown("- DFM模型分析")
        st.markdown("- 预测模型")

    st.markdown("---")
    st.info("💡 请从左侧边栏选择主模块开始使用系统功能。")

    return {
        'status': 'success',
        'content_type': 'welcome',
        'main_module': main_module,
        'sub_module': sub_module
    }


__all__ = [
    'render_main_content', 'get_content_config', 'render_content_header',
    'route_to_content', 'render_data_preview_content', 'render_monitoring_analysis_content',
    'render_model_analysis_content', 'render_application_tools_content',
    'create_content_container', 'get_module_icon', 'get_module_description',
    'validate_content_config', 'render_data_cleaning_interface',
    'render_variable_calculation_interface', 'render_data_merge_interface',
    'render_data_comparison_interface', 'detect_navigation_level',
    'render_module_selection_guide', 'render_welcome_page'
]
