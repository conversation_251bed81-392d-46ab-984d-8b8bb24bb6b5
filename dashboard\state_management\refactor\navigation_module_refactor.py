# -*- coding: utf-8 -*-
"""
Navigation模块重构适配器
提供Navigation模块与统一状态管理器的集成接口
"""

from typing import Dict, Any, Optional, List, Union
from .module_refactor_base import ModuleRefactorBase
from ..unified_state_manager import UnifiedStateManager


class NavigationModuleRefactor(ModuleRefactorBase):
    """Navigation模块重构适配器"""
    
    def __init__(self, unified_manager: UnifiedStateManager = None):
        # 先初始化Navigation特定的状态键映射
        self.navigation_state_mapping = {
            # 主要导航状态
            'nav_main_module': 'main_module',
            'nav_sub_module': 'sub_module',
            'nav_previous_main': 'previous_main',
            'nav_previous_sub': 'previous_sub',
            'nav_transitioning': 'transitioning',
            
            # 传统状态键兼容
            'selected_main_module': 'main_module',
            'selected_sub_module': 'sub_module',
            'previous_main_module': 'previous_main',
            'previous_sub_module': 'previous_sub',
            
            # 导航配置
            'navigation_config': 'config',
            'navigation_mode': 'mode',
            'navigation_theme': 'theme',
            'navigation_layout': 'layout',
            
            # 用户偏好
            'user_preferences': 'user_preferences',
            'navigation_history': 'history',
            'navigation_bookmarks': 'bookmarks',
            
            # 状态标志
            'navigation_initialized': 'initialized',
            'navigation_ready': 'ready',
            'navigation_error': 'error'
        }
        
        # Navigation子模块映射
        self.navigation_module_mapping = {
            'main': 'main_navigation',
            'sub': 'sub_navigation',
            'breadcrumb': 'breadcrumb_navigation',
            'sidebar': 'sidebar_navigation',
            'tabs': 'tabs_navigation',
            'menu': 'menu_navigation'
        }

        # 调用父类初始化
        super().__init__("navigation", unified_manager)
    
    def _initialize_module(self):
        """初始化Navigation模块"""
        try:
            # 设置默认状态
            self._setup_default_states()
            
            # 初始化子模块
            self._initialize_submodules()
            
            if self.logger:
                self.logger.info("Navigation模块初始化完成")
            else:
                print("Navigation模块初始化完成")

        except Exception as e:
            if self.logger:
                self.logger.error(f"Navigation模块初始化失败: {e}")
            else:
                print(f"Navigation模块初始化失败: {e}")
            raise
    
    def _setup_default_states(self):
        """设置默认状态"""
        default_states = {
            # 主要导航状态
            'main_module': '数据预览',
            'sub_module': None,
            'previous_main': None,
            'previous_sub': None,
            'transitioning': False,
            
            # 导航配置
            'config': {
                'default_main': '数据预览',
                'available_modules': ['数据预览', '监测分析', '模型分析', '应用工具'],
                'sub_modules': {
                    '数据预览': ['工业', '消费'],
                    '监测分析': ['工业', '消费'],
                    '模型分析': ['DFM 模型'],
                    '应用工具': ['数据探索', '数据预处理']  # 调整顺序，数据探索优先
                }
            },
            'mode': 'sidebar',
            'theme': 'light',
            'layout': 'standard',
            
            # 用户偏好
            'user_preferences': {
                'preferred_main': '数据预览',
                'preferred_theme': 'light',
                'auto_save_state': True,
                'show_breadcrumb': True
            },
            'history': [],
            'bookmarks': [],
            
            # 状态标志
            'initialized': True,
            'ready': True,
            'error': None
        }
        
        for config_key, default_value in default_states.items():
            if not self.get_state(config_key):
                self.set_state(config_key, default_value)
    
    def _initialize_submodules(self):
        """初始化子模块"""
        for module_name in self.navigation_module_mapping.keys():
            # 为每个子模块设置基本状态
            self.set_navigation_state(module_name, 'initialized', True)
            self.set_navigation_state(module_name, 'last_update', None)
    
    def _get_from_unified_manager(self, key: str, default=None):
        """从统一状态管理器获取值"""
        if not self.unified_manager:
            return default

        try:
            # 尝试多个可能的键格式，使用统一状态管理器而不是直接访问session_state
            for possible_key in [f"navigation.{key}", key, f"nav_{key}"]:
                result = self.unified_manager.get_state(possible_key, None)
                if result is not None:
                    return result
            return default
        except Exception as e:
            self.log_warning(f"Failed to get state from unified manager for key {key}: {e}")
            return default
    
    def _set_to_unified_manager(self, key: str, value) -> bool:
        """设置值到统一状态管理器（只使用统一管理器）"""
        if not self.unified_manager:
            return False

        try:
            # 只使用统一状态管理器，不再双重存储
            return self.unified_manager.set_state(f"navigation.{key}", value)
        except:
            return False

    def _delete_from_unified_manager(self, key: str) -> bool:
        """从统一状态管理器删除导航状态"""
        if not self.unified_manager:
            return False

        # 尝试从Navigation命名空间删除
        if hasattr(self.unified_manager, 'delete_navigation_state'):
            return self.unified_manager.delete_navigation_state('navigation', key)

        # 回退到全局状态删除
        if hasattr(self.unified_manager, 'delete_state'):
            return self.unified_manager.delete_state(f"navigation.{key}")

        return True  # 假设删除成功

    def _clear_unified_manager_state(self):
        """清空统一状态管理器中的Navigation状态"""
        if not self.unified_manager:
            return

        # 尝试清空Navigation命名空间
        if hasattr(self.unified_manager, 'clear_navigation_states'):
            self.unified_manager.clear_navigation_states()
        elif hasattr(self.unified_manager, 'clear_module_states'):
            self.unified_manager.clear_module_states('navigation')

    def get_navigation_state(self, module: str, key: str, default=None):
        """获取Navigation子模块状态"""
        if not self.unified_manager:
            return default
        
        # 构造完整的键名
        full_key = f"{module}_{key}"
        
        # 尝试从navigation命名空间获取
        value = self.unified_manager.get_navigation_state('navigation', full_key, None)
        if value is not None:
            return value
        
        # 尝试从模块特定的命名空间获取
        return self.unified_manager.get_state(f"navigation.{module}.{key}", default)
    
    def set_navigation_state(self, module: str, key: str, value) -> bool:
        """设置Navigation子模块状态"""
        if not self.unified_manager:
            return False
        
        # 构造完整的键名
        full_key = f"{module}_{key}"
        
        # 同时设置到多个位置（兼容性）
        success1 = self.unified_manager.set_navigation_state('navigation', full_key, value)
        success2 = self.unified_manager.set_state(f"navigation.{module}.{key}", value)
        
        return success1 or success2
    
    def get_current_main_module(self) -> str:
        """获取当前主模块"""
        return self.get_state('main_module', '数据预览')
    
    def set_current_main_module(self, module_name: str) -> bool:
        """设置当前主模块"""
        current = self.get_current_main_module()
        if current != module_name:
            # 保存之前的状态
            self.set_state('previous_main', current)

            # 如果从应用工具切换到其他模块，清除数据探索状态
            if current == "应用工具" and module_name != "应用工具":
                self._clear_data_exploration_states()

            # 设置新的状态
            success = self.set_state('main_module', module_name)

            # 重置子模块为None，实现三层导航体验
            # 用户需要手动选择子模块，而不是自动设置
            self.set_state('sub_module', None)

            return success
        return True
    
    def get_current_sub_module(self) -> Optional[str]:
        """获取当前子模块"""
        return self.get_state('sub_module')
    
    def set_current_sub_module(self, sub_module_name: str) -> bool:
        """设置当前子模块"""
        try:
            current = self.get_current_sub_module()
            if current != sub_module_name:
                # 如果从数据探索切换到其他子模块，或者设置为None，清除数据探索状态
                current_main = self.get_current_main_module()
                if current_main == "应用工具":
                    if current == "数据探索" and sub_module_name != "数据探索":
                        self._clear_data_exploration_states()
                    elif sub_module_name is None:
                        # 清除子模块时也清除数据探索状态
                        self._clear_data_exploration_states()

                # 保存之前的状态
                self.set_state('previous_sub', current)
                # 设置新的状态
                result = self.set_state('sub_module', sub_module_name)
                return result
            return True
        except Exception as e:
            raise
    
    def get_available_main_modules(self) -> List[str]:
        """获取可用的主模块列表"""
        config = self.get_state('config', {})
        return config.get('available_modules', [])
    
    def get_available_sub_modules(self, main_module: str = None) -> List[str]:
        """获取可用的子模块列表"""
        if main_module is None:
            main_module = self.get_current_main_module()
        
        config = self.get_state('config', {})
        sub_modules = config.get('sub_modules', {})
        return sub_modules.get(main_module, [])
    
    def navigate_to_main(self, module_name: str) -> bool:
        """导航到主模块"""
        if module_name in self.get_available_main_modules():
            return self.set_current_main_module(module_name)
        return False
    
    def navigate_to_sub(self, sub_module_name: str) -> bool:
        """导航到子模块"""
        current_main = self.get_current_main_module()
        available_subs = self.get_available_sub_modules(current_main)

        if sub_module_name in available_subs:
            return self.set_current_sub_module(sub_module_name)
        return False

    def _clear_data_exploration_states(self):
        """清除数据探索相关状态"""
        try:

            # 获取数据探索适配器并清除状态
            from dashboard.state_management.adapters.data_exploration_adapter import get_data_exploration_adapter
            data_exploration_adapter = get_data_exploration_adapter()

            if data_exploration_adapter:
                success = data_exploration_adapter.clear_all_states()
                if success:
                    pass  # 清除成功
                else:
                    pass  # 清除失败，但不影响主流程
            else:
                pass  # 没有适配器

        except Exception as e:
            # 不抛出异常，避免影响主导航流程
            pass
    
    def add_to_history(self, main_module: str, sub_module: str = None):
        """添加到导航历史"""
        history = self.get_state('history', [])
        
        # 创建历史记录项
        history_item = {
            'main_module': main_module,
            'sub_module': sub_module,
            'timestamp': self._get_current_timestamp()
        }
        
        # 避免重复记录
        if not history or history[-1] != history_item:
            history.append(history_item)
            
            # 限制历史记录长度
            if len(history) > 100:
                history = history[-100:]
            
            self.set_state('history', history)
    
    def get_navigation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取导航历史"""
        history = self.get_state('history', [])
        return history[-limit:] if limit > 0 else history
    
    def clear_history(self):
        """清除导航历史"""
        self.set_state('history', [])
    
    def add_bookmark(self, main_module: str, sub_module: str = None, label: str = None):
        """添加书签"""
        bookmarks = self.get_state('bookmarks', [])
        
        bookmark = {
            'main_module': main_module,
            'sub_module': sub_module,
            'label': label or f"{main_module}" + (f" - {sub_module}" if sub_module else ""),
            'timestamp': self._get_current_timestamp()
        }
        
        # 避免重复书签
        existing = [b for b in bookmarks if b['main_module'] == main_module and b['sub_module'] == sub_module]
        if not existing:
            bookmarks.append(bookmark)
            self.set_state('bookmarks', bookmarks)
    
    def get_bookmarks(self) -> List[Dict[str, Any]]:
        """获取书签列表"""
        return self.get_state('bookmarks', [])
    
    def remove_bookmark(self, main_module: str, sub_module: str = None) -> bool:
        """移除书签"""
        bookmarks = self.get_state('bookmarks', [])
        original_length = len(bookmarks)
        
        bookmarks = [b for b in bookmarks if not (b['main_module'] == main_module and b['sub_module'] == sub_module)]
        
        if len(bookmarks) < original_length:
            self.set_state('bookmarks', bookmarks)
            return True
        return False
    
    def get_user_preferences(self) -> Dict[str, Any]:
        """获取用户偏好"""
        return self.get_state('user_preferences', {})
    
    def set_user_preference(self, key: str, value: Any) -> bool:
        """设置用户偏好"""
        preferences = self.get_user_preferences()
        preferences[key] = value
        return self.set_state('user_preferences', preferences)
    
    def reset_navigation(self):
        """重置导航状态"""
        config = self.get_state('config', {})
        default_main = config.get('default_main', '数据预览')
        
        self.set_state('main_module', default_main)
        self.set_state('sub_module', None)
        self.set_state('previous_main', None)
        self.set_state('previous_sub', None)
        self.set_state('transitioning', False)
    
    def get_navigation_config(self) -> Dict[str, Any]:
        """获取导航配置"""
        return self.get_state('config', {})
    
    def update_navigation_config(self, config_updates: Dict[str, Any]) -> bool:
        """更新导航配置"""
        config = self.get_navigation_config()
        config.update(config_updates)
        return self.set_state('config', config)
    
    def is_transitioning(self) -> bool:
        """检查是否正在转换"""
        return self.get_state('transitioning', False)
    
    def set_transitioning(self, transitioning: bool) -> bool:
        """设置转换状态"""
        return self.set_state('transitioning', transitioning)
    
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            'module_name': self.module_name,
            'current_main': self.get_current_main_module(),
            'current_sub': self.get_current_sub_module(),
            'available_main_modules': self.get_available_main_modules(),
            'available_sub_modules': self.get_available_sub_modules(),
            'navigation_mode': self.get_state('mode', 'sidebar'),
            'theme': self.get_state('theme', 'light'),
            'initialized': self.get_state('initialized', False),
            'ready': self.get_state('ready', False)
        }
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def on_module_load(self):
        """模块加载时的回调"""
        super().on_module_load()
        
        # 记录当前导航状态到历史
        current_main = self.get_current_main_module()
        current_sub = self.get_current_sub_module()
        self.add_to_history(current_main, current_sub)

        if self.logger:
            self.logger.info(f"Navigation模块已加载: {current_main}" + (f" - {current_sub}" if current_sub else ""))
        else:
            print(f"Navigation模块已加载: {current_main}" + (f" - {current_sub}" if current_sub else ""))
    
    def on_module_unload(self):
        """模块卸载时的回调"""
        super().on_module_unload()
        
        # 可以在这里添加清理逻辑
        if self.logger:
            self.logger.info("Navigation模块已卸载")
        else:
            print("Navigation模块已卸载")
