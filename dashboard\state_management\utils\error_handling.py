# -*- coding: utf-8 -*-
"""
状态管理标准化错误处理和日志记录模块
提供统一的异常类、错误处理装饰器和日志记录功能
"""

import logging
import functools
import time
import traceback
from typing import Any, Callable, Dict, Optional, Union
from enum import Enum


class StateManagementError(Exception):
    """状态管理基础异常"""
    
    def __init__(self, message: str, error_code: str = None, context: Dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.context = context or {}
        self.timestamp = time.time()


class StateNotFoundError(StateManagementError):
    """状态未找到异常"""
    
    def __init__(self, key: str, context: Dict = None):
        message = f"状态键未找到: {key}"
        super().__init__(message, "STATE_NOT_FOUND", context)
        self.key = key


class StateValidationError(StateManagementError):
    """状态验证失败异常"""
    
    def __init__(self, key: str, value: Any, reason: str, context: Dict = None):
        message = f"状态验证失败: {key} = {value}, 原因: {reason}"
        super().__init__(message, "STATE_VALIDATION_ERROR", context)
        self.key = key
        self.value = value
        self.reason = reason


class StateAccessError(StateManagementError):
    """状态访问权限异常"""
    
    def __init__(self, key: str, operation: str, context: Dict = None):
        message = f"状态访问被拒绝: {operation} {key}"
        super().__init__(message, "STATE_ACCESS_DENIED", context)
        self.key = key
        self.operation = operation


class StateManagerUnavailableError(StateManagementError):
    """状态管理器不可用异常"""
    
    def __init__(self, manager_type: str, context: Dict = None):
        message = f"状态管理器不可用: {manager_type}"
        super().__init__(message, "STATE_MANAGER_UNAVAILABLE", context)
        self.manager_type = manager_type


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class StateManagementLogger:
    """状态管理专用日志记录器"""
    
    def __init__(self, name: str = "state_management"):
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 创建格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(console_handler)
            self.logger.setLevel(logging.INFO)
    
    def log_state_operation(self, operation: str, key: str, success: bool, 
                           duration: float = None, context: Dict = None):
        """记录状态操作日志"""
        level = LogLevel.INFO if success else LogLevel.ERROR
        message = f"状态操作: {operation} {key} - {'成功' if success else '失败'}"
        
        if duration is not None:
            message += f" (耗时: {duration:.3f}s)"
        
        if context:
            message += f" - 上下文: {context}"
        
        self._log(level, message)
    
    def log_state_access(self, key: str, access_type: str, success: bool, 
                        value_type: str = None, context: Dict = None):
        """记录状态访问日志"""
        level = LogLevel.DEBUG if success else LogLevel.WARNING
        message = f"状态访问: {access_type} {key} - {'成功' if success else '失败'}"
        
        if value_type:
            message += f" (类型: {value_type})"
        
        if context:
            message += f" - 上下文: {context}"
        
        self._log(level, message)
    
    def log_error(self, error: Exception, context: Dict = None):
        """记录错误日志"""
        if isinstance(error, StateManagementError):
            message = f"状态管理错误 [{error.error_code}]: {error.message}"
            if error.context:
                message += f" - 错误上下文: {error.context}"
        else:
            message = f"未知错误: {str(error)}"
        
        if context:
            message += f" - 调用上下文: {context}"
        
        self._log(LogLevel.ERROR, message)
        
        # 记录详细的堆栈跟踪
        self.logger.debug(f"错误堆栈: {traceback.format_exc()}")
    
    def log_performance_warning(self, operation: str, duration: float, 
                               threshold: float = 0.1, context: Dict = None):
        """记录性能警告"""
        if duration > threshold:
            message = f"性能警告: {operation} 耗时过长 ({duration:.3f}s > {threshold:.3f}s)"
            if context:
                message += f" - 上下文: {context}"
            self._log(LogLevel.WARNING, message)
    
    def _log(self, level: LogLevel, message: str):
        """内部日志记录方法"""
        log_method = getattr(self.logger, level.value.lower())
        log_method(message)


# 全局日志记录器实例
_global_logger = StateManagementLogger()


def get_logger(name: str = None) -> StateManagementLogger:
    """获取日志记录器实例"""
    if name:
        return StateManagementLogger(name)
    return _global_logger


def handle_state_errors(fallback_value: Any = None, 
                       log_errors: bool = True,
                       raise_on_critical: bool = False):
    """
    状态管理错误处理装饰器
    
    Args:
        fallback_value: 发生错误时的回退值
        log_errors: 是否记录错误日志
        raise_on_critical: 是否在关键错误时重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger = get_logger()
            
            try:
                result = func(*args, **kwargs)
                
                # 记录成功的操作
                duration = time.time() - start_time
                if log_errors:
                    logger.log_state_operation(
                        func.__name__, 
                        kwargs.get('key', 'unknown'),
                        True,
                        duration
                    )
                
                # 性能警告
                logger.log_performance_warning(func.__name__, duration)
                
                return result
                
            except StateManagementError as e:
                duration = time.time() - start_time
                
                if log_errors:
                    logger.log_error(e, {
                        'function': func.__name__,
                        'args': str(args)[:100],  # 限制长度
                        'kwargs': str(kwargs)[:100],
                        'duration': duration
                    })
                
                # 关键错误重新抛出
                if raise_on_critical and isinstance(e, (StateManagerUnavailableError,)):
                    raise
                
                return fallback_value
                
            except Exception as e:
                duration = time.time() - start_time
                
                if log_errors:
                    logger.log_error(e, {
                        'function': func.__name__,
                        'args': str(args)[:100],
                        'kwargs': str(kwargs)[:100],
                        'duration': duration
                    })
                
                # 未知错误总是重新抛出（如果启用）
                if raise_on_critical:
                    raise
                
                return fallback_value
        
        return wrapper
    return decorator


def safe_state_operation(operation_name: str, key: str = None):
    """
    安全状态操作装饰器 - 简化版本
    
    Args:
        operation_name: 操作名称
        key: 状态键（可选）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger()
            actual_key = key or kwargs.get('key', 'unknown')
            
            try:
                result = func(*args, **kwargs)
                logger.log_state_access(actual_key, operation_name, True, 
                                      type(result).__name__ if result is not None else None)
                return result
            except Exception as e:
                logger.log_state_access(actual_key, operation_name, False)
                logger.log_error(e)
                raise
        
        return wrapper
    return decorator


def validate_state_key(key: str) -> bool:
    """
    验证状态键格式
    
    Args:
        key: 状态键
        
    Returns:
        bool: 是否有效
        
    Raises:
        StateValidationError: 键格式无效
    """
    if not key:
        raise StateValidationError(key, key, "状态键不能为空")
    
    if not isinstance(key, str):
        raise StateValidationError(key, key, "状态键必须是字符串")
    
    # 检查命名约定
    if not key.replace('.', '').replace('_', '').replace('-', '').isalnum():
        raise StateValidationError(key, key, "状态键只能包含字母、数字、点、下划线和连字符")
    
    # 检查长度
    if len(key) > 100:
        raise StateValidationError(key, key, "状态键长度不能超过100个字符")
    
    return True


def create_error_context(module: str, function: str, **kwargs) -> Dict[str, Any]:
    """
    创建错误上下文信息
    
    Args:
        module: 模块名
        function: 函数名
        **kwargs: 额外的上下文信息
        
    Returns:
        Dict[str, Any]: 上下文字典
    """
    context = {
        'module': module,
        'function': function,
        'timestamp': time.time()
    }
    context.update(kwargs)
    return context


# 便捷的错误处理函数
def handle_state_get_error(key: str, error: Exception, default: Any = None) -> Any:
    """处理状态获取错误"""
    logger = get_logger()
    
    if isinstance(error, StateNotFoundError):
        logger.log_state_access(key, "get", False, context={'reason': 'not_found'})
        return default
    elif isinstance(error, StateAccessError):
        logger.log_state_access(key, "get", False, context={'reason': 'access_denied'})
        return default
    else:
        logger.log_error(error, {'operation': 'get_state', 'key': key})
        return default


def handle_state_set_error(key: str, value: Any, error: Exception) -> bool:
    """处理状态设置错误"""
    logger = get_logger()
    
    if isinstance(error, StateValidationError):
        logger.log_state_access(key, "set", False, context={'reason': 'validation_failed'})
        return False
    elif isinstance(error, StateAccessError):
        logger.log_state_access(key, "set", False, context={'reason': 'access_denied'})
        return False
    else:
        logger.log_error(error, {'operation': 'set_state', 'key': key, 'value_type': type(value).__name__})
        return False


# 导出的公共接口
__all__ = [
    # 异常类
    'StateManagementError',
    'StateNotFoundError', 
    'StateValidationError',
    'StateAccessError',
    'StateManagerUnavailableError',
    
    # 日志记录
    'StateManagementLogger',
    'LogLevel',
    'get_logger',
    
    # 装饰器
    'handle_state_errors',
    'safe_state_operation',
    
    # 工具函数
    'validate_state_key',
    'create_error_context',
    'handle_state_get_error',
    'handle_state_set_error'
]
