"""
独立的映射加载包装器
解决相对导入问题，为UI提供稳定的load_mappings接口
"""

import pandas as pd
import unicodedata
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

def normalize_text(text):
    """标准化文本，移除特殊字符和空格"""
    if pd.isna(text) or text == '':
        return ''
    
    # 转换为字符串并标准化Unicode
    text = str(text)
    text = unicodedata.normalize('NFKC', text)
    
    # 移除前后空格
    text = text.strip()
    
    return text

def load_mappings(
    excel_path: str,
    sheet_name: str,
    indicator_col: str = '高频指标',
    type_col: str = '类型',
    industry_col: Optional[str] = '行业'
) -> Tuple[Dict[str, str], Dict[str, str]]:
    """
    从Excel文件加载变量映射信息
    
    Args:
        excel_path: Excel文件路径
        sheet_name: 工作表名称
        indicator_col: 指标列名
        type_col: 类型列名
        industry_col: 行业列名（可选）
    
    Returns:
        Tuple[Dict[str, str], Dict[str, str]]: (变量类型映射, 变量行业映射)
    """
    try:
        logger.info(f"加载映射文件: {excel_path}, 工作表: {sheet_name}")
        
        # 读取Excel文件
        df = pd.read_excel(excel_path, sheet_name=sheet_name)
        logger.info(f"成功读取映射表，形状: {df.shape}")
        
        # 检查必需的列是否存在
        if indicator_col not in df.columns:
            logger.error(f"未找到指标列: {indicator_col}")
            return {}, {}
        
        # 标准化指标名称
        df[indicator_col] = df[indicator_col].apply(normalize_text)
        
        # 创建变量类型映射
        var_type_map = {}
        if type_col in df.columns:
            df[type_col] = df[type_col].apply(normalize_text)
            var_type_map = dict(zip(df[indicator_col], df[type_col]))
            logger.info(f"创建类型映射，包含 {len(var_type_map)} 个条目")
        else:
            logger.warning(f"未找到类型列: {type_col}")
        
        # 创建变量行业映射
        var_industry_map = {}
        if industry_col and industry_col in df.columns:
            df[industry_col] = df[industry_col].apply(normalize_text)
            var_industry_map = dict(zip(df[indicator_col], df[industry_col]))
            logger.info(f"创建行业映射，包含 {len(var_industry_map)} 个条目")
        else:
            logger.warning(f"未找到行业列: {industry_col}")
        
        # 过滤掉空的指标名称
        var_type_map = {k: v for k, v in var_type_map.items() if k and k.strip()}
        var_industry_map = {k: v for k, v in var_industry_map.items() if k and k.strip()}
        
        logger.info(f"映射加载完成 - 类型映射: {len(var_type_map)} 条目, 行业映射: {len(var_industry_map)} 条目")
        
        return var_type_map, var_industry_map
        
    except Exception as e:
        logger.error(f"加载映射时发生错误: {str(e)}")
        return {}, {}

def get_pmi_industry_variables(var_industry_map: Dict[str, str]) -> Dict[str, str]:
    """
    从行业映射中提取PMI相关的行业变量
    
    Args:
        var_industry_map: 变量行业映射字典
    
    Returns:
        Dict[str, str]: PMI行业变量映射
    """
    pmi_variables = {}
    
    # 搜索包含PMI关键词的变量
    pmi_keywords = ['PMI', 'pmi', '采购经理指数', '制造业PMI', '服务业PMI']
    
    for var_name, industry in var_industry_map.items():
        # 检查变量名或行业名是否包含PMI关键词
        var_lower = var_name.lower()
        industry_lower = industry.lower() if industry else ''
        
        for keyword in pmi_keywords:
            if keyword.lower() in var_lower or keyword.lower() in industry_lower:
                pmi_variables[var_name] = industry
                break
    
    logger.info(f"找到 {len(pmi_variables)} 个PMI相关变量")
    return pmi_variables

# 为了向后兼容，提供一个简化的接口
def load_mappings_simple(excel_path: str, sheet_name: str = '指标体系') -> Tuple[Dict[str, str], Dict[str, str]]:
    """简化的映射加载接口"""
    return load_mappings(excel_path, sheet_name)

if __name__ == "__main__":
    # 测试代码
    print("映射包装器模块加载成功")
