# -*- coding: utf-8 -*-
"""
数据流控制器
管理跨模块数据流、转换和验证
"""

import logging
from typing import Dict, List, Optional, Any, Callable, Set, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import threading
import copy
from .state_metadata import StateScope, DataType


class DataFlowStatus(Enum):
    """数据流状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DataFlowPriority(Enum):
    """数据流优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class DataTransformation:
    """数据转换定义"""
    name: str
    transform_func: Callable[[Any], Any]
    validation_func: Optional[Callable[[Any], bool]] = None
    description: str = ""
    is_async: bool = False


@dataclass
class DataFlowRule:
    """数据流规则"""
    source_module: str
    target_module: str
    source_key: str
    target_key: str
    transformations: List[DataTransformation] = field(default_factory=list)
    conditions: List[Callable[[], bool]] = field(default_factory=list)
    priority: DataFlowPriority = DataFlowPriority.NORMAL
    auto_trigger: bool = True
    description: str = ""


@dataclass
class DataFlowExecution:
    """数据流执行记录"""
    rule_id: str
    source_module: str
    target_module: str
    source_key: str
    target_key: str
    status: DataFlowStatus = DataFlowStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    data_size: int = 0
    execution_time_ms: float = 0.0


class DataFlowController:
    """数据流控制器 - 管理跨模块数据流"""
    
    def __init__(self, unified_manager=None, module_manager=None):
        """
        初始化数据流控制器
        
        Args:
            unified_manager: 统一状态管理器实例
            module_manager: 模块管理器实例
        """
        self.unified_manager = unified_manager
        self.module_manager = module_manager
        self.logger = logging.getLogger(__name__)
        
        # 数据流规则
        self._flow_rules: Dict[str, DataFlowRule] = {}
        
        # 执行历史
        self._execution_history: List[DataFlowExecution] = []
        
        # 活跃的数据流
        self._active_flows: Dict[str, DataFlowExecution] = {}
        
        # 数据血缘关系
        self._data_lineage: Dict[str, Set[str]] = {}  # key -> set of dependent keys
        self._reverse_lineage: Dict[str, Set[str]] = {}  # key -> set of source keys
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._total_executions = 0
        self._successful_executions = 0
        self._failed_executions = 0
        
        # 数据处理器注册
        self._data_processors: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("DataFlowController initialized")
    
    def register_flow_rule(self, 
                          rule_id: str,
                          source_module: str,
                          target_module: str,
                          source_key: str,
                          target_key: str,
                          transformations: List[DataTransformation] = None,
                          conditions: List[Callable[[], bool]] = None,
                          priority: DataFlowPriority = DataFlowPriority.NORMAL,
                          auto_trigger: bool = True,
                          description: str = "") -> bool:
        """
        注册数据流规则
        
        Args:
            rule_id: 规则唯一标识
            source_module: 源模块名称
            target_module: 目标模块名称
            source_key: 源数据键
            target_key: 目标数据键
            transformations: 数据转换列表
            conditions: 触发条件列表
            priority: 优先级
            auto_trigger: 是否自动触发
            description: 规则描述
            
        Returns:
            bool: 注册是否成功
        """
        with self._lock:
            try:
                # 检查规则是否已存在
                if rule_id in self._flow_rules:
                    self.logger.warning(f"Flow rule {rule_id} already exists")
                    return False
                
                # 验证模块是否已注册
                if self.module_manager:
                    if not self.module_manager.is_module_registered(source_module):
                        self.logger.error(f"Source module {source_module} not registered")
                        return False
                    
                    if not self.module_manager.is_module_registered(target_module):
                        self.logger.error(f"Target module {target_module} not registered")
                        return False
                
                # 创建数据流规则
                rule = DataFlowRule(
                    source_module=source_module,
                    target_module=target_module,
                    source_key=source_key,
                    target_key=target_key,
                    transformations=transformations or [],
                    conditions=conditions or [],
                    priority=priority,
                    auto_trigger=auto_trigger,
                    description=description
                )
                
                # 注册规则
                self._flow_rules[rule_id] = rule
                
                # 更新数据血缘关系
                self._update_lineage(source_key, target_key)
                
                self.logger.info(f"Flow rule {rule_id} registered successfully")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to register flow rule {rule_id}: {e}")
                return False
    
    def _update_lineage(self, source_key: str, target_key: str):
        """更新数据血缘关系"""
        # 正向血缘：source_key -> target_key
        if source_key not in self._data_lineage:
            self._data_lineage[source_key] = set()
        self._data_lineage[source_key].add(target_key)
        
        # 反向血缘：target_key <- source_key
        if target_key not in self._reverse_lineage:
            self._reverse_lineage[target_key] = set()
        self._reverse_lineage[target_key].add(source_key)

    def register_processor(self, 
                          processor_id: str,
                          processor_func: Callable[[Any], Any],
                          data_types: List[str] = None) -> bool:
        """
        注册数据处理器
        
        Args:
            processor_id: 处理器唯一标识
            processor_func: 处理函数
            data_types: 支持的数据类型列表
            
        Returns:
            bool: 注册是否成功
        """
        with self._lock:
            try:
                if processor_id in self._data_processors:
                    self.logger.warning(f"Data processor {processor_id} already exists")
                    return False
                
                self._data_processors[processor_id] = {
                    'func': processor_func,
                    'data_types': data_types or [],
                    'registered_at': datetime.now()
                }
                
                self.logger.info(f"Data processor {processor_id} registered successfully")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to register data processor {processor_id}: {e}")
                return False
    
    def execute_flow(self, rule_id: str, force: bool = False) -> bool:
        """
        执行数据流
        
        Args:
            rule_id: 规则ID
            force: 是否强制执行（忽略条件检查）
            
        Returns:
            bool: 执行是否成功
        """
        with self._lock:
            try:
                # 检查规则是否存在
                if rule_id not in self._flow_rules:
                    self.logger.error(f"Flow rule {rule_id} not found")
                    return False
                
                rule = self._flow_rules[rule_id]
                
                # 检查是否已有活跃的执行
                if rule_id in self._active_flows:
                    self.logger.warning(f"Flow rule {rule_id} already executing")
                    return False
                
                # 创建执行记录
                execution = DataFlowExecution(
                    rule_id=rule_id,
                    source_module=rule.source_module,
                    target_module=rule.target_module,
                    source_key=rule.source_key,
                    target_key=rule.target_key,
                    status=DataFlowStatus.PENDING
                )
                
                # 添加到活跃流
                self._active_flows[rule_id] = execution
                
                # 开始执行
                execution.status = DataFlowStatus.PROCESSING
                execution.started_at = datetime.now()
                
                # 检查触发条件
                if not force:
                    for condition in rule.conditions:
                        if not condition():
                            execution.status = DataFlowStatus.CANCELLED
                            execution.completed_at = datetime.now()
                            execution.error_message = "Condition not met"
                            self._complete_execution(execution)
                            return False
                
                # 获取源数据
                if not self.unified_manager.has_state(rule.source_key):
                    execution.status = DataFlowStatus.FAILED
                    execution.completed_at = datetime.now()
                    execution.error_message = f"Source key {rule.source_key} not found"
                    self._complete_execution(execution)
                    return False
                
                source_data = self.unified_manager.get_state(rule.source_key)
                execution.data_size = len(str(source_data))  # 简单的大小估算
                
                # 应用数据转换
                transformed_data = source_data
                for transformation in rule.transformations:
                    try:
                        # 验证输入数据
                        if transformation.validation_func:
                            if not transformation.validation_func(transformed_data):
                                raise ValueError(f"Validation failed for transformation {transformation.name}")
                        
                        # 应用转换
                        transformed_data = transformation.transform_func(transformed_data)
                        
                        self.logger.debug(f"Applied transformation {transformation.name}")
                        
                    except Exception as e:
                        execution.status = DataFlowStatus.FAILED
                        execution.completed_at = datetime.now()
                        execution.error_message = f"Transformation {transformation.name} failed: {e}"
                        self._complete_execution(execution)
                        return False
                
                # 设置目标数据
                success = self.unified_manager.set_state(
                    rule.target_key, 
                    transformed_data,
                    scope=StateScope.SHARED,
                    module_owner=rule.target_module,
                    description=f"Data flow from {rule.source_module} via rule {rule_id}"
                )
                
                if success:
                    execution.status = DataFlowStatus.COMPLETED
                    execution.completed_at = datetime.now()
                    execution.execution_time_ms = (
                        execution.completed_at - execution.started_at
                    ).total_seconds() * 1000
                    
                    self._successful_executions += 1
                    self.logger.info(f"Flow rule {rule_id} executed successfully")
                else:
                    execution.status = DataFlowStatus.FAILED
                    execution.completed_at = datetime.now()
                    execution.error_message = "Failed to set target state"
                    self._failed_executions += 1
                
                self._complete_execution(execution)
                return success
                
            except Exception as e:
                self.logger.error(f"Failed to execute flow rule {rule_id}: {e}")
                if rule_id in self._active_flows:
                    execution = self._active_flows[rule_id]
                    execution.status = DataFlowStatus.FAILED
                    execution.completed_at = datetime.now()
                    execution.error_message = str(e)
                    self._complete_execution(execution)
                return False
    
    def _complete_execution(self, execution: DataFlowExecution):
        """完成执行记录"""
        # 从活跃流中移除
        if execution.rule_id in self._active_flows:
            del self._active_flows[execution.rule_id]

        # 添加到历史记录
        self._execution_history.append(execution)
        self._total_executions += 1

        # 限制历史记录数量
        if len(self._execution_history) > 1000:
            self._execution_history = self._execution_history[-500:]

    def trigger_auto_flows(self, changed_key: str) -> List[str]:
        """
        触发与指定键相关的自动数据流

        Args:
            changed_key: 发生变化的数据键

        Returns:
            List[str]: 成功执行的规则ID列表
        """
        executed_rules = []

        with self._lock:
            try:
                # 查找以此键为源的自动触发规则
                for rule_id, rule in self._flow_rules.items():
                    if (rule.source_key == changed_key and
                        rule.auto_trigger and
                        rule_id not in self._active_flows):

                        if self.execute_flow(rule_id):
                            executed_rules.append(rule_id)

                self.logger.debug(f"Auto-triggered {len(executed_rules)} flows for key {changed_key}")

            except Exception as e:
                self.logger.error(f"Failed to trigger auto flows for key {changed_key}: {e}")

        return executed_rules

    def get_data_lineage(self, key: str, direction: str = "downstream") -> Set[str]:
        """
        获取数据血缘关系

        Args:
            key: 数据键
            direction: 方向 ("downstream" 或 "upstream")

        Returns:
            Set[str]: 相关的数据键集合
        """
        if direction == "downstream":
            return self._data_lineage.get(key, set()).copy()
        elif direction == "upstream":
            return self._reverse_lineage.get(key, set()).copy()
        else:
            raise ValueError("Direction must be 'downstream' or 'upstream'")

    def get_full_lineage_tree(self, key: str, max_depth: int = 10) -> Dict[str, Any]:
        """
        获取完整的数据血缘树

        Args:
            key: 根数据键
            max_depth: 最大深度

        Returns:
            Dict: 血缘树结构
        """
        def build_tree(current_key: str, depth: int, visited: Set[str]) -> Dict[str, Any]:
            if depth >= max_depth or current_key in visited:
                return {"key": current_key, "children": []}

            visited.add(current_key)
            children = []

            for child_key in self._data_lineage.get(current_key, set()):
                child_tree = build_tree(child_key, depth + 1, visited.copy())
                children.append(child_tree)

            return {"key": current_key, "children": children}

        return build_tree(key, 0, set())

    def get_flow_rules(self) -> Dict[str, DataFlowRule]:
        """获取所有数据流规则"""
        return self._flow_rules.copy()

    def get_execution_history(self, limit: int = 100) -> List[DataFlowExecution]:
        """获取执行历史"""
        return self._execution_history[-limit:] if limit > 0 else self._execution_history.copy()

    def get_active_flows(self) -> Dict[str, DataFlowExecution]:
        """获取活跃的数据流"""
        return self._active_flows.copy()

    def cancel_flow(self, rule_id: str) -> bool:
        """
        取消数据流执行

        Args:
            rule_id: 规则ID

        Returns:
            bool: 取消是否成功
        """
        with self._lock:
            if rule_id in self._active_flows:
                execution = self._active_flows[rule_id]
                execution.status = DataFlowStatus.CANCELLED
                execution.completed_at = datetime.now()
                execution.error_message = "Cancelled by user"
                self._complete_execution(execution)

                self.logger.info(f"Flow rule {rule_id} cancelled")
                return True
            else:
                self.logger.warning(f"Flow rule {rule_id} not active, cannot cancel")
                return False

    def remove_flow_rule(self, rule_id: str) -> bool:
        """
        移除数据流规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 移除是否成功
        """
        with self._lock:
            try:
                # 取消活跃的执行
                if rule_id in self._active_flows:
                    self.cancel_flow(rule_id)

                # 移除规则
                if rule_id in self._flow_rules:
                    rule = self._flow_rules[rule_id]

                    # 更新血缘关系
                    source_key = rule.source_key
                    target_key = rule.target_key

                    if source_key in self._data_lineage:
                        self._data_lineage[source_key].discard(target_key)
                        if not self._data_lineage[source_key]:
                            del self._data_lineage[source_key]

                    if target_key in self._reverse_lineage:
                        self._reverse_lineage[target_key].discard(source_key)
                        if not self._reverse_lineage[target_key]:
                            del self._reverse_lineage[target_key]

                    del self._flow_rules[rule_id]

                    self.logger.info(f"Flow rule {rule_id} removed")
                    return True
                else:
                    self.logger.warning(f"Flow rule {rule_id} not found")
                    return False

            except Exception as e:
                self.logger.error(f"Failed to remove flow rule {rule_id}: {e}")
                return False

    def get_stats(self) -> Dict[str, Any]:
        """获取数据流控制器统计信息"""
        return {
            'total_rules': len(self._flow_rules),
            'active_flows': len(self._active_flows),
            'total_executions': self._total_executions,
            'successful_executions': self._successful_executions,
            'failed_executions': self._failed_executions,
            'success_rate': (
                self._successful_executions / self._total_executions
                if self._total_executions > 0 else 0.0
            ),
            'lineage_nodes': len(self._data_lineage) + len(self._reverse_lineage),
            'execution_history_size': len(self._execution_history)
        }

    def validate_flow_integrity(self) -> Dict[str, List[str]]:
        """
        验证数据流完整性

        Returns:
            Dict: 验证结果，包含各种问题的列表
        """
        issues = {
            'missing_source_keys': [],
            'missing_target_modules': [],
            'circular_dependencies': [],
            'orphaned_rules': []
        }

        try:
            # 检查源键是否存在
            for rule_id, rule in self._flow_rules.items():
                if not self.unified_manager.has_state(rule.source_key):
                    issues['missing_source_keys'].append(f"{rule_id}: {rule.source_key}")

                # 检查目标模块是否注册
                if self.module_manager and not self.module_manager.is_module_registered(rule.target_module):
                    issues['missing_target_modules'].append(f"{rule_id}: {rule.target_module}")

            # 检查循环依赖（简化版）
            for key in self._data_lineage:
                if self._has_circular_dependency(key, set()):
                    issues['circular_dependencies'].append(key)

        except Exception as e:
            self.logger.error(f"Failed to validate flow integrity: {e}")

        return issues

    def _has_circular_dependency(self, key: str, visited: Set[str]) -> bool:
        """检查是否存在循环依赖"""
        if key in visited:
            return True

        visited.add(key)
        for dependent in self._data_lineage.get(key, set()):
            if self._has_circular_dependency(dependent, visited.copy()):
                return True

        return False
