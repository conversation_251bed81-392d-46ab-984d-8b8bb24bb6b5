# -*- coding: utf-8 -*-
"""
数据计算组件
整合原有的数据计算UI组件功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import logging

from .base import PreprocessingComponent

logger = logging.getLogger(__name__)


class DataComputeComponent(PreprocessingComponent):
    """数据计算组件"""
    
    def __init__(self):
        super().__init__("data_compute", "数据计算")
    
    def render_variable_calculator(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染变量计算界面"""
        
        st_obj.markdown("#### 变量计算")
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        
        if not numeric_cols:
            st_obj.warning("没有数值列可进行计算")
            return data
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            calculation_type = st_obj.selectbox(
                "计算类型:",
                options=["移动平均", "累计求和", "差分", "对数变换", "标准化", "归一化"],
                key=f"{self.component_name}_calc_type"
            )
            
            selected_cols = st_obj.multiselect(
                "选择计算列:",
                options=numeric_cols,
                default=numeric_cols[:3] if len(numeric_cols) > 3 else numeric_cols,
                key=f"{self.component_name}_calc_cols"
            )
        
        with col2:
            # 根据计算类型显示不同参数
            if calculation_type == "移动平均":
                window_size = st_obj.number_input(
                    "窗口大小:",
                    min_value=2,
                    max_value=min(50, len(data) // 4),
                    value=5,
                    key=f"{self.component_name}_window_size"
                )
                param_value = window_size
            elif calculation_type == "差分":
                diff_periods = st_obj.number_input(
                    "差分期数:",
                    min_value=1,
                    max_value=10,
                    value=1,
                    key=f"{self.component_name}_diff_periods"
                )
                param_value = diff_periods
            else:
                param_value = None
            
            new_col_suffix = st_obj.text_input(
                "新列名后缀:",
                value=f"_{calculation_type.lower()}",
                key=f"{self.component_name}_suffix"
            )
        
        if st_obj.button("执行计算", key=f"{self.component_name}_execute_calc"):
            if not selected_cols:
                st_obj.error("请选择要计算的列")
                return data
            
            processed_data = data.copy()
            
            try:
                for col in selected_cols:
                    new_col_name = f"{col}{new_col_suffix}"
                    
                    if calculation_type == "移动平均":
                        processed_data[new_col_name] = processed_data[col].rolling(window=param_value).mean()
                    elif calculation_type == "累计求和":
                        processed_data[new_col_name] = processed_data[col].cumsum()
                    elif calculation_type == "差分":
                        processed_data[new_col_name] = processed_data[col].diff(periods=param_value)
                    elif calculation_type == "对数变换":
                        # 确保数据为正数
                        if (processed_data[col] <= 0).any():
                            st_obj.warning(f"列 '{col}' 包含非正数，将使用 log(x+1) 变换")
                            processed_data[new_col_name] = np.log1p(processed_data[col])
                        else:
                            processed_data[new_col_name] = np.log(processed_data[col])
                    elif calculation_type == "标准化":
                        processed_data[new_col_name] = (processed_data[col] - processed_data[col].mean()) / processed_data[col].std()
                    elif calculation_type == "归一化":
                        min_val = processed_data[col].min()
                        max_val = processed_data[col].max()
                        processed_data[new_col_name] = (processed_data[col] - min_val) / (max_val - min_val)
                
                # 记录操作
                self.add_to_operation_history(
                    "变量计算",
                    f"对列 {selected_cols} 执行 {calculation_type}",
                    {"type": calculation_type, "columns": selected_cols, "parameter": param_value}
                )
                
                st_obj.success(f"变量计算完成！新增 {len(selected_cols)} 列")
                return processed_data
                
            except Exception as e:
                st_obj.error(f"变量计算失败: {str(e)}")
                return data
        
        return data
    
    def render_aggregation_calculator(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染聚合计算界面"""
        
        st_obj.markdown("#### 数据聚合")
        
        # 检查是否有时间列
        time_cols = []
        for col in data.columns:
            try:
                pd.to_datetime(data[col], errors='raise')
                time_cols.append(col)
            except:
                continue
        
        if not time_cols:
            st_obj.warning("没有找到时间列，无法进行时间聚合")
            return data
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        
        if not numeric_cols:
            st_obj.warning("没有数值列可进行聚合")
            return data
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            time_col = st_obj.selectbox(
                "选择时间列:",
                options=time_cols,
                key=f"{self.component_name}_time_col"
            )
            
            freq = st_obj.selectbox(
                "聚合频率:",
                options=["D", "W", "M", "Q", "Y"],
                format_func=lambda x: {"D": "日", "W": "周", "M": "月", "Q": "季", "Y": "年"}[x],
                key=f"{self.component_name}_freq"
            )
        
        with col2:
            agg_method = st_obj.selectbox(
                "聚合方法:",
                options=["mean", "sum", "min", "max", "std", "count"],
                format_func=lambda x: {"mean": "平均值", "sum": "求和", "min": "最小值", 
                                     "max": "最大值", "std": "标准差", "count": "计数"}[x],
                key=f"{self.component_name}_agg_method"
            )
            
            selected_cols = st_obj.multiselect(
                "选择聚合列:",
                options=numeric_cols,
                default=numeric_cols[:3] if len(numeric_cols) > 3 else numeric_cols,
                key=f"{self.component_name}_agg_cols"
            )
        
        if st_obj.button("执行聚合", key=f"{self.component_name}_execute_agg"):
            if not selected_cols:
                st_obj.error("请选择要聚合的列")
                return data
            
            try:
                # 设置时间列为索引
                temp_data = data.copy()
                temp_data[time_col] = pd.to_datetime(temp_data[time_col])
                temp_data = temp_data.set_index(time_col)
                
                # 执行聚合
                if agg_method == "count":
                    aggregated_data = temp_data[selected_cols].resample(freq).count()
                else:
                    aggregated_data = temp_data[selected_cols].resample(freq).agg(agg_method)
                
                # 重置索引
                aggregated_data = aggregated_data.reset_index()
                
                # 记录操作
                self.add_to_operation_history(
                    "数据聚合",
                    f"按 {freq} 频率对列 {selected_cols} 执行 {agg_method} 聚合",
                    {"frequency": freq, "method": agg_method, "columns": selected_cols}
                )
                
                st_obj.success(f"数据聚合完成！聚合后数据形状: {aggregated_data.shape}")
                
                # 显示聚合结果预览
                st_obj.markdown("**聚合结果预览:**")
                st_obj.dataframe(aggregated_data.head(10), use_container_width=True)
                
                return aggregated_data
                
            except Exception as e:
                st_obj.error(f"数据聚合失败: {str(e)}")
                return data
        
        return data
    
    def render_column_operations(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染列操作界面"""
        
        st_obj.markdown("#### 列操作")
        
        operation_type = st_obj.selectbox(
            "操作类型:",
            options=["删除列", "重命名列", "复制列", "列运算"],
            key=f"{self.component_name}_col_operation"
        )
        
        processed_data = data.copy()
        
        if operation_type == "删除列":
            cols_to_delete = st_obj.multiselect(
                "选择要删除的列:",
                options=data.columns.tolist(),
                key=f"{self.component_name}_delete_cols"
            )
            
            if st_obj.button("删除列", key=f"{self.component_name}_delete_btn"):
                if cols_to_delete:
                    processed_data = processed_data.drop(columns=cols_to_delete)
                    self.add_to_operation_history("删除列", f"删除列: {cols_to_delete}")
                    st_obj.success(f"已删除 {len(cols_to_delete)} 列")
                    return processed_data
        
        elif operation_type == "重命名列":
            col1, col2 = st_obj.columns(2)
            with col1:
                old_name = st_obj.selectbox("选择要重命名的列:", data.columns.tolist(), key=f"{self.component_name}_old_name")
            with col2:
                new_name = st_obj.text_input("新列名:", key=f"{self.component_name}_new_name")
            
            if st_obj.button("重命名", key=f"{self.component_name}_rename_btn"):
                if new_name and new_name not in data.columns:
                    processed_data = processed_data.rename(columns={old_name: new_name})
                    self.add_to_operation_history("重命名列", f"将 '{old_name}' 重命名为 '{new_name}'")
                    st_obj.success(f"列 '{old_name}' 已重命名为 '{new_name}'")
                    return processed_data
                else:
                    st_obj.error("请输入有效的新列名")
        
        elif operation_type == "复制列":
            col1, col2 = st_obj.columns(2)
            with col1:
                source_col = st_obj.selectbox("选择要复制的列:", data.columns.tolist(), key=f"{self.component_name}_source_col")
            with col2:
                copy_name = st_obj.text_input("复制列名:", value=f"{source_col}_copy", key=f"{self.component_name}_copy_name")
            
            if st_obj.button("复制列", key=f"{self.component_name}_copy_btn"):
                if copy_name and copy_name not in data.columns:
                    processed_data[copy_name] = processed_data[source_col].copy()
                    self.add_to_operation_history("复制列", f"复制 '{source_col}' 为 '{copy_name}'")
                    st_obj.success(f"已复制列 '{source_col}' 为 '{copy_name}'")
                    return processed_data
                else:
                    st_obj.error("请输入有效的复制列名")
        
        elif operation_type == "列运算":
            numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
            if len(numeric_cols) >= 2:
                col1, col2, col3 = st_obj.columns(3)
                with col1:
                    col_a = st_obj.selectbox("列A:", numeric_cols, key=f"{self.component_name}_col_a")
                with col2:
                    operator = st_obj.selectbox("运算符:", ["+", "-", "*", "/"], key=f"{self.component_name}_operator")
                with col3:
                    col_b = st_obj.selectbox("列B:", numeric_cols, key=f"{self.component_name}_col_b")
                
                result_name = st_obj.text_input("结果列名:", value=f"{col_a}_{operator}_{col_b}", key=f"{self.component_name}_result_name")
                
                if st_obj.button("执行运算", key=f"{self.component_name}_calc_btn"):
                    if result_name and result_name not in data.columns:
                        try:
                            if operator == "+":
                                processed_data[result_name] = processed_data[col_a] + processed_data[col_b]
                            elif operator == "-":
                                processed_data[result_name] = processed_data[col_a] - processed_data[col_b]
                            elif operator == "*":
                                processed_data[result_name] = processed_data[col_a] * processed_data[col_b]
                            elif operator == "/":
                                processed_data[result_name] = processed_data[col_a] / processed_data[col_b]
                            
                            self.add_to_operation_history("列运算", f"{col_a} {operator} {col_b} = {result_name}")
                            st_obj.success(f"列运算完成，新增列 '{result_name}'")
                            return processed_data
                        except Exception as e:
                            st_obj.error(f"列运算失败: {str(e)}")
                    else:
                        st_obj.error("请输入有效的结果列名")
            else:
                st_obj.warning("列运算需要至少两个数值列")
        
        return data
    
    def render_processing_interface(self, st_obj, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """渲染数据计算处理界面"""
        
        # 选择计算操作
        compute_operation = st_obj.selectbox(
            "选择计算操作:",
            options=["变量计算", "数据聚合", "列操作"],
            key=f"{self.component_name}_operation_selector"
        )
        
        processed_data = data
        
        if compute_operation == "变量计算":
            processed_data = self.render_variable_calculator(st_obj, data)
        elif compute_operation == "数据聚合":
            processed_data = self.render_aggregation_calculator(st_obj, data)
        elif compute_operation == "列操作":
            processed_data = self.render_column_operations(st_obj, data)
        
        # 如果数据被处理，保存到状态
        if processed_data is not data:
            self.set_state('processed_data', processed_data)
            self.set_state('last_operation', compute_operation)
        
        return processed_data


__all__ = ['DataComputeComponent']
