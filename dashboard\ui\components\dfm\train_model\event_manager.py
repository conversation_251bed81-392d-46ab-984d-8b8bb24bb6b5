"""
DFM训练事件管理器（已弃用）

⚠️ 警告：此模块已弃用，请使用 StateEventSystem 替代
该模块保留仅为兼容性，不应在新代码中使用。

推荐使用：
from dashboard.state_management.events.state_events import get_global_event_system
"""

import threading
import queue
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from collections import defaultdict

from .events import BaseEvent, TrainingLogEvent

logger = logging.getLogger(__name__)


class LegacyEventManager:
    """
    事件管理器
    
    线程安全的事件管理系统，支持事件发布、订阅和处理。
    使用单例模式确保全局唯一实例。
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(LegacyEventManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化事件管理器"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        
        # 事件队列，线程安全
        self._event_queue = queue.Queue(maxsize=1000)
        
        # 事件统计
        self._event_stats = defaultdict(int)
        
        # 配置参数
        self._max_queue_size = 1000
        self._event_retention_hours = 24
        
        # 线程锁
        self._stats_lock = threading.Lock()
        
        # 已处理事件的历史记录（用于调试）
        self._processed_events = []
        self._max_history_size = 100
        
        logger.info("EventManager initialized")
    
    def publish_event(self, event: BaseEvent) -> bool:
        """
        发布事件到队列
        
        Args:
            event: 要发布的事件
            
        Returns:
            bool: 发布是否成功
        """
        try:
            # 检查队列是否已满
            if self._event_queue.qsize() >= self._max_queue_size:
                logger.warning(f"Event queue is full ({self._max_queue_size}), dropping oldest events")
                self._cleanup_old_events()
            
            # 添加事件到队列
            self._event_queue.put_nowait(event)
            
            # 更新统计
            with self._stats_lock:
                self._event_stats[event.event_type] += 1
                self._event_stats['total_published'] += 1
            
            logger.debug(f"Published event: {event}")
            return True
            
        except queue.Full:
            logger.error(f"Failed to publish event, queue is full: {event}")
            return False
        except Exception as e:
            logger.error(f"Error publishing event {event}: {e}")
            return False
    
    def get_pending_events(self) -> List[BaseEvent]:
        """
        获取所有待处理的事件
        
        Returns:
            List[BaseEvent]: 待处理事件列表
        """
        events = []
        try:
            # 非阻塞方式获取所有待处理事件
            while True:
                try:
                    event = self._event_queue.get_nowait()
                    events.append(event)
                    
                    # 添加到历史记录
                    self._add_to_history(event)
                    
                except queue.Empty:
                    break
            
            if events:
                with self._stats_lock:
                    self._event_stats['total_processed'] += len(events)
                
                logger.debug(f"Retrieved {len(events)} pending events")
            
            return events
            
        except Exception as e:
            logger.error(f"Error getting pending events: {e}")
            return []
    
    def has_pending_events(self) -> bool:
        """
        检查是否有待处理的事件
        
        Returns:
            bool: 是否有待处理事件
        """
        return not self._event_queue.empty()
    
    def get_queue_size(self) -> int:
        """
        获取当前队列大小
        
        Returns:
            int: 队列中的事件数量
        """
        return self._event_queue.qsize()
    
    def clear_events(self) -> int:
        """
        清空所有待处理事件
        
        Returns:
            int: 清空的事件数量
        """
        cleared_count = 0
        try:
            while True:
                try:
                    self._event_queue.get_nowait()
                    cleared_count += 1
                except queue.Empty:
                    break
            
            if cleared_count > 0:
                logger.info(f"Cleared {cleared_count} pending events")
                
                with self._stats_lock:
                    self._event_stats['total_cleared'] += cleared_count
            
            return cleared_count
            
        except Exception as e:
            logger.error(f"Error clearing events: {e}")
            return cleared_count
    
    def get_event_stats(self) -> Dict[str, Any]:
        """
        获取事件统计信息
        
        Returns:
            Dict[str, Any]: 事件统计信息
        """
        with self._stats_lock:
            stats = dict(self._event_stats)
        
        stats.update({
            'queue_size': self.get_queue_size(),
            'max_queue_size': self._max_queue_size,
            'history_size': len(self._processed_events),
            'max_history_size': self._max_history_size
        })
        
        return stats
    
    def get_recent_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近处理的事件
        
        Args:
            limit: 返回的事件数量限制
            
        Returns:
            List[Dict[str, Any]]: 最近事件的字典表示
        """
        recent_events = self._processed_events[-limit:] if self._processed_events else []
        return [event.to_dict() for event in recent_events]
    
    def _add_to_history(self, event: BaseEvent) -> None:
        """
        添加事件到历史记录
        
        Args:
            event: 要添加的事件
        """
        self._processed_events.append(event)
        
        # 限制历史记录大小
        if len(self._processed_events) > self._max_history_size:
            self._processed_events = self._processed_events[-self._max_history_size:]
    
    def _cleanup_old_events(self) -> None:
        """清理队列中的旧事件"""
        try:
            # 简单的清理策略：移除队列前半部分的事件
            cleanup_count = self._max_queue_size // 2
            for _ in range(cleanup_count):
                try:
                    self._event_queue.get_nowait()
                except queue.Empty:
                    break
            
            logger.info(f"Cleaned up {cleanup_count} old events from queue")
            
        except Exception as e:
            logger.error(f"Error during event cleanup: {e}")
    
    def reset(self) -> None:
        """
        重置事件管理器状态
        
        清空所有事件和统计信息
        """
        try:
            # 清空事件队列
            cleared_count = self.clear_events()
            
            # 重置统计信息
            with self._stats_lock:
                self._event_stats.clear()
            
            # 清空历史记录
            self._processed_events.clear()
            
            logger.info(f"EventManager reset completed, cleared {cleared_count} events")
            
        except Exception as e:
            logger.error(f"Error resetting EventManager: {e}")
    
    def publish_log_event(self, message: str, log_level: str = "INFO", source: str = None) -> bool:
        """
        便捷方法：发布日志事件
        
        Args:
            message: 日志消息
            log_level: 日志级别
            source: 日志来源
            
        Returns:
            bool: 发布是否成功
        """
        log_event = TrainingLogEvent(
            message=message,
            log_level=log_level,
            source=source
        )
        return self.publish_event(log_event)


# 全局事件管理器实例
_global_event_manager = None
_global_manager_lock = threading.Lock()


def get_legacy_event_manager() -> LegacyEventManager:
    """
    获取遗留事件管理器实例（已弃用）

    ⚠️ 警告：此函数已弃用，请使用 get_global_event_system() 替代

    Returns:
        LegacyEventManager: 遗留事件管理器实例
    """
    global _global_event_manager

    if _global_event_manager is None:
        with _global_manager_lock:
            if _global_event_manager is None:
                _global_event_manager = LegacyEventManager()

    return _global_event_manager


def get_event_manager():
    """
    兼容性函数：重定向到正确的事件系统

    ⚠️ 警告：此函数已弃用，请直接使用 get_global_event_system()
    """
    from dashboard.state_management.events.state_events import get_global_event_system
    return get_global_event_system()


def reset_event_manager() -> None:
    """重置全局事件管理器"""
    global _global_event_manager
    
    with _global_manager_lock:
        if _global_event_manager is not None:
            _global_event_manager.reset()
            _global_event_manager = None
