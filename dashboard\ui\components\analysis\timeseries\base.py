# -*- coding: utf-8 -*-
"""
时间序列分析组件基类
提供时间序列分析组件的基础接口和通用功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from abc import abstractmethod
import logging
import time

from ...base import UIComponent
from ....utils.state_helpers import (
    get_exploration_state,
    set_exploration_state
)

logger = logging.getLogger(__name__)


class TimeSeriesAnalysisComponent(UIComponent):
    """时间序列分析组件基类"""
    
    def __init__(self, analysis_type: str, title: str = None):
        # 先设置属性
        self.analysis_type = analysis_type
        self.title = title or analysis_type
        self.logger = logging.getLogger(f"{__name__}.{analysis_type}")

        # 调用父类初始化方法
        super().__init__(component_name=f"timeseries_{analysis_type}")
    
    def get_component_id(self) -> str:
        """获取组件ID"""
        return f"timeseries_{self.analysis_type}"
    
    def get_state_keys(self) -> List[str]:
        """获取组件相关的状态键"""
        return [
            f'{self.analysis_type}_data',
            f'{self.analysis_type}_results',
            f'{self.analysis_type}_parameters',
            f'{self.analysis_type}_last_analysis_time',
            f'{self.analysis_type}_active'
        ]
    
    def get_state(self, key: str, default=None):
        """获取分析状态 - 使用ToolsModuleRefactor"""
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor

            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 使用analysis作为工具类型，analysis_type作为子模块
                return tools_refactor.get_tools_state('analysis', f'{self.analysis_type}.{key}', default)
            else:
                self.logger.warning(f"ToolsModuleRefactor不可用，使用fallback获取状态: {key}")
                return get_exploration_state(self.analysis_type, key, default)
        except Exception as e:
            self.logger.error(f"获取分析状态失败: {key} - {e}")
            return default

    def set_state(self, key: str, value):
        """设置分析状态 - 使用ToolsModuleRefactor"""
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor

            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 使用analysis作为工具类型，analysis_type作为子模块
                success = tools_refactor.set_tools_state('analysis', f'{self.analysis_type}.{key}', value)
                if success:
                    self.logger.debug(f"设置分析状态成功: {self.analysis_type}.{key}")
                    return True
                else:
                    self.logger.warning(f"设置分析状态失败: {self.analysis_type}.{key}")
                    return False
            else:
                self.logger.warning(f"ToolsModuleRefactor不可用，使用fallback设置状态: {key}")
                return set_exploration_state(self.analysis_type, key, value)
        except Exception as e:
            self.logger.error(f"设置分析状态失败: {key} - {e}")
            return False
    
    def set_state_value(self, key: str, value):
        """设置状态值 - 只使用ToolsModuleRefactor"""
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor

            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 设置到tools状态
                success = tools_refactor.set_tools_state('ui_state', key, value)
                if success:
                    self.logger.debug(f"设置UI状态成功: {key}")
                    return
                else:
                    self.logger.error(f"设置UI状态失败: {key}")
                    raise RuntimeError(f"状态设置失败: {key}")
            else:
                self.logger.error(f"ToolsModuleRefactor不可用，无法设置状态: {key}")
                raise RuntimeError(f"ToolsModuleRefactor不可用: {key}")

        except Exception as e:
            self.logger.error(f"设置状态失败: {key} - {e}")
            raise

    def get_session_state_value(self, key: str, default=None):
        """获取session state值 - 迁移到ToolsModuleRefactor"""
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor

            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 从tools状态获取
                value = tools_refactor.get_tools_state('ui_state', f'session.{key}', None)
                if value is not None:
                    return value
                return default
            else:
                self.logger.warning(f"ToolsModuleRefactor不可用，返回默认值: {key}")
                return default
        except Exception as e:
            self.logger.error(f"获取session state失败: {key} - {e}")
            return default
    
    def detect_tab_activation(self, st_obj, tab_index: int) -> bool:
        """
        检测当前标签页是否激活
        
        Args:
            st_obj: Streamlit对象
            tab_index: 标签页索引
            
        Returns:
            bool: 是否激活
        """
        is_really_active = False
        
        # 尝试从统一状态管理器检测当前激活的标签页
        try:
            from dashboard.state_management.refactor import get_global_tools_refactor

            tools_refactor = get_global_tools_refactor()
            if tools_refactor:
                # 首先检查我们自己管理的标签页状态
                current_active_tab = tools_refactor.get_tools_state('ui_state', 'data_exploration_active_tab')
                if current_active_tab == self.analysis_type:
                    is_really_active = True
                else:
                    # Fallback到Streamlit内置状态检测
                    if hasattr(st, 'session_state'):
                        for key in st.session_state.keys():
                            if 'TabState' in str(key) or 'tab' in str(key).lower():
                                try:
                                    tab_state = st.session_state.get(key)

                                    if hasattr(tab_state, 'active_tab') and tab_state.active_tab == tab_index:
                                        is_really_active = True
                                        break
                                    elif hasattr(tab_state, 'value') and tab_state.value == tab_index:
                                        is_really_active = True
                                        break
                                except Exception as e:
                                    self.logger.debug(f"检查键 {key} 时出错: {e}")
                                    continue
            else:
                # 如果tools_refactor不可用，使用原有逻辑
                if hasattr(st, 'session_state'):
                    for key in st.session_state.keys():
                        if 'TabState' in str(key) or 'tab' in str(key).lower():
                            try:
                                tab_state = st.session_state.get(key)

                                if hasattr(tab_state, 'active_tab') and tab_state.active_tab == tab_index:
                                    is_really_active = True
                                    break
                                elif hasattr(tab_state, 'value') and tab_state.value == tab_index:
                                    is_really_active = True
                                    break
                            except Exception as e:
                                self.logger.debug(f"检查键 {key} 时出错: {e}")
                                continue
        except Exception as e:
            self.logger.error(f"检测标签页激活状态失败: {e}")
            # 最后的fallback
            if hasattr(st, 'session_state'):
                for key in st.session_state.keys():
                    if 'TabState' in str(key) or 'tab' in str(key).lower():
                        try:
                            tab_state = st.session_state.get(key)

                            if hasattr(tab_state, 'active_tab') and tab_state.active_tab == tab_index:
                                is_really_active = True
                                break
                            elif hasattr(tab_state, 'value') and tab_state.value == tab_index:
                                is_really_active = True
                                break
                        except Exception as e:
                            self.logger.debug(f"检查键 {key} 时出错: {e}")
                            continue
        
        # 只有在真正激活时才设置标志
        if is_really_active:
            self.logger.debug(f"{self.analysis_type} 标签页真正激活，设置标志")
            
            # 设置当前模块活跃状态
            previous_active_tab = self.get_session_state_value('data_exploration_active_tab', None)
            
            # 只在状态真正改变时才更新
            if previous_active_tab != self.analysis_type:
                # 清除其他模块的活跃状态
                for module in ['stationarity', 'time_lag_corr', 'lead_lag']:
                    if module != self.analysis_type:
                        self.set_session_state_value(f'currently_in_{module}_tab', False)
                
                # 设置当前模块活跃
                self.set_session_state_value(f'currently_in_{self.analysis_type}_tab', True)
                
                # 设置时间戳和活跃标签页标识
                current_time = time.time()
                self.set_session_state_value(f'{self.analysis_type}_tab_set_time', current_time)
                self.set_session_state_value('data_exploration_active_tab', self.analysis_type)
                
                self.logger.debug(f"{self.analysis_type} 状态已更新")
        
        # 更新模块活动时间戳
        self.set_state('last_activity_time', time.time())
        
        return is_really_active
    
    def get_module_data(self) -> Tuple[Optional[pd.DataFrame], str, str]:
        """
        获取当前模块的数据
        
        Returns:
            Tuple[Optional[pd.DataFrame], str, str]: (数据, 数据源描述, 数据名称)
        """
        try:
            # 从统一状态管理器获取数据探索适配器
            from dashboard.state_management.adapters.data_exploration_adapter import get_data_exploration_adapter
            
            adapter = get_data_exploration_adapter()
            if not adapter:
                return None, "数据探索适配器不可用", ""
            
            # 获取当前模块的数据
            module_info = adapter.get_module_info(self.analysis_type)
            
            if module_info and module_info.get('data') is not None:
                selected_data = module_info['data']
                file_name = module_info.get('file_name', '')
                data_source_type = module_info.get('data_source', 'unknown')
                
                if data_source_type == 'upload':
                    data_source = f"上传文件: {file_name}" if file_name else "上传文件"
                else:
                    data_source = f"数据源: {data_source_type}"
                
                selected_df_name = file_name or "data"
                
                return selected_data, data_source, selected_df_name
            else:
                return None, "未选择数据", ""
                
        except Exception as e:
            self.logger.error(f"获取模块数据失败: {e}")
            return None, f"获取数据失败: {str(e)}", ""
    
    def render_data_status(self, st_obj):
        """渲染数据状态信息"""
        selected_data, data_source, selected_df_name = self.get_module_data()
        
        if selected_data is None:
            st_obj.info("📋 请在左侧侧边栏上传数据文件以进行分析")
            st_obj.markdown(f"""
            **📋 使用说明：**
            1. **数据上传**：在左侧侧边栏上传数据文件（唯一上传入口）
            2. **数据格式**：第一列为时间戳，其余列为变量数据
            3. **支持格式**：CSV、Excel (.xlsx, .xls)
            4. **编码支持**：UTF-8、GBK、GB2312等

            **🔄 数据共享说明：**
            - 侧边栏上传的数据在三个分析模块间自动共享
            - 平稳性分析、相关性分析、领先滞后分析使用同一数据源
            - 无需重复上传，一次上传即可在所有模块中使用
            """)
            return None, "", ""
        else:
            st_obj.success(f"✅ 当前数据源: {data_source}")
            st_obj.info(f"📊 数据形状: {selected_data.shape[0]} 行 × {selected_data.shape[1]} 列")
            return selected_data, data_source, selected_df_name
    
    @abstractmethod
    def render_analysis_interface(self, st_obj, data: pd.DataFrame, data_name: str) -> Any:
        """
        渲染分析界面
        
        Args:
            st_obj: Streamlit对象
            data: 分析数据
            data_name: 数据名称
            
        Returns:
            Any: 分析结果
        """
        pass
    
    def render(self, st_obj, **kwargs) -> Any:
        """
        渲染完整的时间序列分析组件
        
        Args:
            st_obj: Streamlit对象
            **kwargs: 其他参数
            
        Returns:
            Any: 分析结果
        """
        try:
            # 检测标签页激活状态
            tab_index = kwargs.get('tab_index', 0)
            self.detect_tab_activation(st_obj, tab_index)

            # 移除重复的标题渲染，因为标签页已经显示了标题
            # st_obj.markdown(f"### {self.title}")

            # 渲染数据状态和获取数据
            data, data_source, data_name = self.render_data_status(st_obj)
            
            if data is None:
                return None
            
            # 渲染分析界面
            return self.render_analysis_interface(st_obj, data, data_name)
            
        except Exception as e:
            self.handle_error(st_obj, e, f"渲染{self.title}组件")
            return None


__all__ = ['TimeSeriesAnalysisComponent']
