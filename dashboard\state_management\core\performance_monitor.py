# -*- coding: utf-8 -*-
"""
性能监控器
监控系统性能、识别瓶颈、提供优化建议
"""

import logging
import time
import threading
import psutil
import gc
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import statistics


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"          # 计数器
    GAUGE = "gauge"             # 仪表盘
    HISTOGRAM = "histogram"     # 直方图
    TIMER = "timer"             # 计时器


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    metric_type: MetricType
    value: float
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)
    description: str = ""


@dataclass
class PerformanceAlert:
    """性能告警"""
    alert_id: str
    metric_name: str
    level: AlertLevel
    message: str
    value: float
    threshold: float
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None


@dataclass
class PerformanceThreshold:
    """性能阈值"""
    metric_name: str
    warning_threshold: Optional[float] = None
    error_threshold: Optional[float] = None
    critical_threshold: Optional[float] = None
    comparison: str = "greater"  # greater, less, equal
    enabled: bool = True


@dataclass
class SystemSnapshot:
    """系统快照"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    process_count: int
    thread_count: int
    gc_collections: Dict[int, int]


class PerformanceMonitor:
    """性能监控器 - 监控系统性能和应用指标"""
    
    def __init__(self, unified_manager=None, max_history_size: int = 10000):
        """
        初始化性能监控器
        
        Args:
            unified_manager: 统一状态管理器实例
            max_history_size: 最大历史记录数量
        """
        self.unified_manager = unified_manager
        self.logger = logging.getLogger(__name__)
        self.max_history_size = max_history_size
        
        # 指标存储
        self._metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        self._current_metrics: Dict[str, PerformanceMetric] = {}
        
        # 告警管理
        self._alerts: Dict[str, PerformanceAlert] = {}
        self._thresholds: Dict[str, PerformanceThreshold] = {}
        
        # 系统监控
        self._system_snapshots: deque = deque(maxlen=max_history_size)
        self._monitoring_enabled = False
        self._monitoring_interval = 5.0  # 秒
        self._monitoring_thread: Optional[threading.Thread] = None
        
        # 性能计时器
        self._active_timers: Dict[str, float] = {}
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 统计信息
        self._total_metrics_recorded = 0
        self._total_alerts_generated = 0
        self._start_time = datetime.now()
        
        # 预定义的系统指标阈值
        self._setup_default_thresholds()
        
        self.logger.info("PerformanceMonitor initialized")
    
    def _setup_default_thresholds(self):
        """设置默认的系统指标阈值"""
        default_thresholds = [
            PerformanceThreshold("cpu_percent", 70.0, 85.0, 95.0),
            PerformanceThreshold("memory_percent", 75.0, 90.0, 98.0),
            PerformanceThreshold("disk_usage_percent", 80.0, 90.0, 95.0),
            PerformanceThreshold("response_time_ms", 1000.0, 3000.0, 5000.0),
            PerformanceThreshold("error_rate", 0.05, 0.1, 0.2),
        ]
        
        for threshold in default_thresholds:
            self._thresholds[threshold.metric_name] = threshold
    
    def record_metric(self, 
                     name: str, 
                     value: float, 
                     metric_type: MetricType = MetricType.GAUGE,
                     tags: Dict[str, str] = None,
                     description: str = "") -> bool:
        """
        记录性能指标
        
        Args:
            name: 指标名称
            value: 指标值
            metric_type: 指标类型
            tags: 标签
            description: 描述
            
        Returns:
            bool: 记录是否成功
        """
        with self._lock:
            try:
                metric = PerformanceMetric(
                    name=name,
                    metric_type=metric_type,
                    value=value,
                    tags=tags or {},
                    description=description
                )
                
                # 存储当前指标
                self._current_metrics[name] = metric
                
                # 添加到历史记录
                self._metrics_history[name].append(metric)
                
                # 更新统计
                self._total_metrics_recorded += 1
                
                # 检查阈值
                self._check_thresholds(metric)
                
                self.logger.debug(f"Recorded metric {name}: {value}")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to record metric {name}: {e}")
                return False
    
    def start_timer(self, name: str) -> bool:
        """
        开始计时器
        
        Args:
            name: 计时器名称
            
        Returns:
            bool: 启动是否成功
        """
        with self._lock:
            try:
                self._active_timers[name] = time.time()
                self.logger.debug(f"Started timer {name}")
                return True
            except Exception as e:
                self.logger.error(f"Failed to start timer {name}: {e}")
                return False
    
    def stop_timer(self, name: str, tags: Dict[str, str] = None) -> Optional[float]:
        """
        停止计时器并记录耗时
        
        Args:
            name: 计时器名称
            tags: 标签
            
        Returns:
            Optional[float]: 耗时（毫秒），如果失败返回None
        """
        with self._lock:
            try:
                if name not in self._active_timers:
                    self.logger.warning(f"Timer {name} not found")
                    return None
                
                start_time = self._active_timers.pop(name)
                elapsed_ms = (time.time() - start_time) * 1000
                
                # 记录耗时指标
                self.record_metric(
                    f"{name}_duration_ms",
                    elapsed_ms,
                    MetricType.TIMER,
                    tags,
                    f"Duration of {name} operation"
                )
                
                self.logger.debug(f"Stopped timer {name}: {elapsed_ms:.2f}ms")
                return elapsed_ms
                
            except Exception as e:
                self.logger.error(f"Failed to stop timer {name}: {e}")
                return None
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Dict[str, str] = None) -> bool:
        """
        增加计数器
        
        Args:
            name: 计数器名称
            value: 增加值
            tags: 标签
            
        Returns:
            bool: 操作是否成功
        """
        with self._lock:
            try:
                current_value = 0.0
                if name in self._current_metrics:
                    current_value = self._current_metrics[name].value
                
                new_value = current_value + value
                
                return self.record_metric(
                    name,
                    new_value,
                    MetricType.COUNTER,
                    tags,
                    f"Counter for {name}"
                )
                
            except Exception as e:
                self.logger.error(f"Failed to increment counter {name}: {e}")
                return False
    
    def set_threshold(self, 
                     metric_name: str,
                     warning_threshold: Optional[float] = None,
                     error_threshold: Optional[float] = None,
                     critical_threshold: Optional[float] = None,
                     comparison: str = "greater") -> bool:
        """
        设置性能阈值
        
        Args:
            metric_name: 指标名称
            warning_threshold: 警告阈值
            error_threshold: 错误阈值
            critical_threshold: 严重阈值
            comparison: 比较方式 ("greater", "less", "equal")
            
        Returns:
            bool: 设置是否成功
        """
        with self._lock:
            try:
                threshold = PerformanceThreshold(
                    metric_name=metric_name,
                    warning_threshold=warning_threshold,
                    error_threshold=error_threshold,
                    critical_threshold=critical_threshold,
                    comparison=comparison
                )
                
                self._thresholds[metric_name] = threshold
                self.logger.info(f"Set threshold for {metric_name}")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to set threshold for {metric_name}: {e}")
                return False
    
    def _check_thresholds(self, metric: PerformanceMetric):
        """检查指标是否超过阈值"""
        try:
            threshold = self._thresholds.get(metric.name)
            if not threshold or not threshold.enabled:
                return
            
            value = metric.value
            alert_level = None
            threshold_value = None
            
            # 检查严重阈值
            if threshold.critical_threshold is not None:
                if self._compare_value(value, threshold.critical_threshold, threshold.comparison):
                    alert_level = AlertLevel.CRITICAL
                    threshold_value = threshold.critical_threshold

            # 检查错误阈值
            if alert_level is None and threshold.error_threshold is not None:
                if self._compare_value(value, threshold.error_threshold, threshold.comparison):
                    alert_level = AlertLevel.ERROR
                    threshold_value = threshold.error_threshold

            # 检查警告阈值
            if alert_level is None and threshold.warning_threshold is not None:
                if self._compare_value(value, threshold.warning_threshold, threshold.comparison):
                    alert_level = AlertLevel.WARNING
                    threshold_value = threshold.warning_threshold
            
            # 生成告警
            if alert_level and threshold_value is not None:
                self._generate_alert(metric, alert_level, threshold_value)
                
        except Exception as e:
            self.logger.error(f"Failed to check thresholds for {metric.name}: {e}")
    
    def _compare_value(self, value: float, threshold: float, comparison: str) -> bool:
        """比较值和阈值"""
        if comparison == "greater":
            return value > threshold
        elif comparison == "less":
            return value < threshold
        elif comparison == "equal":
            return abs(value - threshold) < 1e-6
        else:
            return False
    
    def _generate_alert(self, metric: PerformanceMetric, level: AlertLevel, threshold: float):
        """生成告警"""
        try:
            alert_id = f"{metric.name}_{level.value}_{int(time.time())}"
            
            alert = PerformanceAlert(
                alert_id=alert_id,
                metric_name=metric.name,
                level=level,
                message=f"Metric {metric.name} ({metric.value}) exceeded {level.value} threshold ({threshold})",
                value=metric.value,
                threshold=threshold
            )
            
            self._alerts[alert_id] = alert
            self._total_alerts_generated += 1
            
            self.logger.warning(f"Performance alert: {alert.message}")
            
        except Exception as e:
            self.logger.error(f"Failed to generate alert: {e}")
    
    def start_system_monitoring(self, interval: float = 5.0) -> bool:
        """
        开始系统监控
        
        Args:
            interval: 监控间隔（秒）
            
        Returns:
            bool: 启动是否成功
        """
        with self._lock:
            try:
                if self._monitoring_enabled:
                    self.logger.warning("System monitoring already running")
                    return True
                
                self._monitoring_interval = interval
                self._monitoring_enabled = True
                
                self._monitoring_thread = threading.Thread(
                    target=self._monitoring_loop,
                    daemon=True
                )
                self._monitoring_thread.start()
                
                self.logger.info(f"Started system monitoring with {interval}s interval")
                return True
                
            except Exception as e:
                self.logger.error(f"Failed to start system monitoring: {e}")
                return False

    def stop_system_monitoring(self) -> bool:
        """
        停止系统监控

        Returns:
            bool: 停止是否成功
        """
        with self._lock:
            try:
                if not self._monitoring_enabled:
                    self.logger.warning("System monitoring not running")
                    return True

                self._monitoring_enabled = False

                if self._monitoring_thread and self._monitoring_thread.is_alive():
                    self._monitoring_thread.join(timeout=10.0)

                self.logger.info("Stopped system monitoring")
                return True

            except Exception as e:
                self.logger.error(f"Failed to stop system monitoring: {e}")
                return False

    def _monitoring_loop(self):
        """系统监控循环"""
        while self._monitoring_enabled:
            try:
                # 收集系统快照
                snapshot = self._collect_system_snapshot()
                if snapshot:
                    self._system_snapshots.append(snapshot)

                    # 记录系统指标
                    self.record_metric("cpu_percent", snapshot.cpu_percent)
                    self.record_metric("memory_percent", snapshot.memory_percent)
                    self.record_metric("memory_used_mb", snapshot.memory_used_mb)
                    self.record_metric("disk_usage_percent", snapshot.disk_usage_percent)
                    self.record_metric("process_count", snapshot.process_count)
                    self.record_metric("thread_count", snapshot.thread_count)

                # 等待下次监控
                time.sleep(self._monitoring_interval)

            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self._monitoring_interval)

    def _collect_system_snapshot(self) -> Optional[SystemSnapshot]:
        """收集系统快照"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            memory_available_mb = memory.available / (1024 * 1024)

            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.percent

            # 进程和线程数
            process_count = len(psutil.pids())
            thread_count = threading.active_count()

            # GC统计
            gc_collections = {i: gc.get_count()[i] for i in range(len(gc.get_count()))}

            return SystemSnapshot(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_usage_percent=disk_usage_percent,
                process_count=process_count,
                thread_count=thread_count,
                gc_collections=gc_collections
            )

        except Exception as e:
            self.logger.error(f"Failed to collect system snapshot: {e}")
            return None

    def get_metric_history(self, name: str, limit: int = 100) -> List[PerformanceMetric]:
        """
        获取指标历史

        Args:
            name: 指标名称
            limit: 限制数量

        Returns:
            List[PerformanceMetric]: 指标历史列表
        """
        with self._lock:
            if name not in self._metrics_history:
                return []

            history = list(self._metrics_history[name])
            return history[-limit:] if limit > 0 else history

    def get_metric_statistics(self, name: str, duration_minutes: int = 60) -> Dict[str, float]:
        """
        获取指标统计信息

        Args:
            name: 指标名称
            duration_minutes: 统计时间范围（分钟）

        Returns:
            Dict[str, float]: 统计信息
        """
        with self._lock:
            try:
                if name not in self._metrics_history:
                    return {}

                # 获取指定时间范围内的数据
                cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
                recent_metrics = [
                    m for m in self._metrics_history[name]
                    if m.timestamp >= cutoff_time
                ]

                if not recent_metrics:
                    return {}

                values = [m.value for m in recent_metrics]

                return {
                    'count': len(values),
                    'min': min(values),
                    'max': max(values),
                    'mean': statistics.mean(values),
                    'median': statistics.median(values),
                    'std_dev': statistics.stdev(values) if len(values) > 1 else 0.0,
                    'latest': values[-1],
                    'first': values[0]
                }

            except Exception as e:
                self.logger.error(f"Failed to get statistics for {name}: {e}")
                return {}

    def get_current_metrics(self) -> Dict[str, PerformanceMetric]:
        """获取当前所有指标"""
        with self._lock:
            return self._current_metrics.copy()

    def get_alerts(self, resolved_only: bool = False, level: AlertLevel = None) -> List[PerformanceAlert]:
        """
        获取告警列表

        Args:
            resolved_only: 是否只返回已解决的告警
            level: 告警级别过滤

        Returns:
            List[PerformanceAlert]: 告警列表
        """
        with self._lock:
            alerts = list(self._alerts.values())

            if resolved_only:
                alerts = [a for a in alerts if a.resolved]

            if level:
                alerts = [a for a in alerts if a.level == level]

            # 按时间倒序排列
            alerts.sort(key=lambda x: x.timestamp, reverse=True)
            return alerts

    def resolve_alert(self, alert_id: str) -> bool:
        """
        解决告警

        Args:
            alert_id: 告警ID

        Returns:
            bool: 解决是否成功
        """
        with self._lock:
            try:
                if alert_id not in self._alerts:
                    self.logger.warning(f"Alert {alert_id} not found")
                    return False

                alert = self._alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = datetime.now()

                self.logger.info(f"Resolved alert {alert_id}")
                return True

            except Exception as e:
                self.logger.error(f"Failed to resolve alert {alert_id}: {e}")
                return False

    def get_system_snapshots(self, limit: int = 100) -> List[SystemSnapshot]:
        """
        获取系统快照历史

        Args:
            limit: 限制数量

        Returns:
            List[SystemSnapshot]: 系统快照列表
        """
        with self._lock:
            snapshots = list(self._system_snapshots)
            return snapshots[-limit:] if limit > 0 else snapshots

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            try:
                # 基本统计
                uptime = datetime.now() - self._start_time

                # 最新系统快照
                latest_snapshot = self._system_snapshots[-1] if self._system_snapshots else None

                # 告警统计
                active_alerts = len([a for a in self._alerts.values() if not a.resolved])
                alerts_by_level = defaultdict(int)
                for alert in self._alerts.values():
                    if not alert.resolved:
                        alerts_by_level[alert.level.value] += 1

                # 指标统计
                metrics_count = len(self._current_metrics)

                return {
                    'uptime_seconds': uptime.total_seconds(),
                    'total_metrics_recorded': self._total_metrics_recorded,
                    'total_alerts_generated': self._total_alerts_generated,
                    'active_alerts': active_alerts,
                    'alerts_by_level': dict(alerts_by_level),
                    'metrics_count': metrics_count,
                    'monitoring_enabled': self._monitoring_enabled,
                    'monitoring_interval': self._monitoring_interval,
                    'latest_system_snapshot': {
                        'cpu_percent': latest_snapshot.cpu_percent if latest_snapshot else None,
                        'memory_percent': latest_snapshot.memory_percent if latest_snapshot else None,
                        'disk_usage_percent': latest_snapshot.disk_usage_percent if latest_snapshot else None,
                        'timestamp': latest_snapshot.timestamp if latest_snapshot else None
                    } if latest_snapshot else None
                }

            except Exception as e:
                self.logger.error(f"Failed to get performance summary: {e}")
                return {}

    def cleanup_old_data(self, days: int = 7) -> int:
        """
        清理旧数据

        Args:
            days: 保留天数

        Returns:
            int: 清理的记录数
        """
        with self._lock:
            try:
                cutoff_time = datetime.now() - timedelta(days=days)
                cleaned_count = 0

                # 清理指标历史
                for name, history in self._metrics_history.items():
                    original_size = len(history)
                    # 创建新的deque，只保留最近的数据
                    new_history = deque(
                        [m for m in history if m.timestamp >= cutoff_time],
                        maxlen=self.max_history_size
                    )
                    self._metrics_history[name] = new_history
                    cleaned_count += original_size - len(new_history)

                # 清理系统快照
                original_snapshots = len(self._system_snapshots)
                new_snapshots = deque(
                    [s for s in self._system_snapshots if s.timestamp >= cutoff_time],
                    maxlen=self.max_history_size
                )
                self._system_snapshots = new_snapshots
                cleaned_count += original_snapshots - len(new_snapshots)

                # 清理已解决的旧告警
                old_alerts = [
                    alert_id for alert_id, alert in self._alerts.items()
                    if alert.resolved and alert.resolved_at and alert.resolved_at < cutoff_time
                ]
                for alert_id in old_alerts:
                    del self._alerts[alert_id]
                    cleaned_count += 1

                self.logger.info(f"Cleaned up {cleaned_count} old performance records")
                return cleaned_count

            except Exception as e:
                self.logger.error(f"Failed to cleanup old data: {e}")
                return 0
