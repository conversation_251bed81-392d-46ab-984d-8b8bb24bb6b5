# -*- coding: utf-8 -*-
"""
时间处理组件
整合原有的时间列处理UI组件功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime, date

from .base import PreprocessingComponent

logger = logging.getLogger(__name__)


class TimeProcessingComponent(PreprocessingComponent):
    """时间处理组件"""
    
    # 频率层次定义
    FREQ_HIERARCHY = {
        'D': 1, 'B': 2, 'W': 7, 'M': 30, 'Q': 90, 'Y': 365
    }
    
    def __init__(self):
        super().__init__("time_processing", "时间处理")
    
    def render_time_column_identification(self, st_obj, data: pd.DataFrame) -> Dict[str, Any]:
        """渲染时间列识别界面"""
        
        st_obj.markdown("#### 时间列识别与处理")
        
        if data is None or data.empty:
            st_obj.error("没有可用数据")
            return {'time_column': None, 'time_info': {}}
        
        # 自动识别时间列
        potential_time_cols = []
        time_col_info = {}
        
        for col in data.columns:
            try:
                # 尝试转换为时间格式
                time_series = pd.to_datetime(data[col], errors='raise')
                potential_time_cols.append(col)
                
                # 分析时间列信息
                time_col_info[col] = {
                    'min_date': time_series.min(),
                    'max_date': time_series.max(),
                    'date_range': (time_series.max() - time_series.min()).days,
                    'unique_count': time_series.nunique(),
                    'null_count': time_series.isnull().sum(),
                    'inferred_freq': self._infer_frequency(time_series)
                }
            except:
                continue
        
        col1, col2 = st_obj.columns(2)
        
        with col1:
            if potential_time_cols:
                st_obj.success(f"✅ 识别到 {len(potential_time_cols)} 个潜在时间列")
                
                # 时间列选择
                selected_time_col = st_obj.selectbox(
                    "选择时间列:",
                    options=potential_time_cols,
                    key=f"{self.component_name}_time_column_select"
                )
                
                if selected_time_col:
                    info = time_col_info[selected_time_col]
                    st_obj.info(f"""
                    **时间列信息:**
                    - 时间范围: {info['min_date'].strftime('%Y-%m-%d')} 到 {info['max_date'].strftime('%Y-%m-%d')}
                    - 总天数: {info['date_range']} 天
                    - 唯一值: {info['unique_count']} 个
                    - 缺失值: {info['null_count']} 个
                    - 推断频率: {info['inferred_freq']}
                    """)
            else:
                st_obj.warning("⚠️ 未识别到时间列")
                
                # 手动指定时间列
                manual_time_col = st_obj.selectbox(
                    "手动选择时间列:",
                    options=[''] + list(data.columns),
                    key=f"{self.component_name}_manual_time_column"
                )
                
                if manual_time_col:
                    try:
                        # 尝试转换
                        time_series = pd.to_datetime(data[manual_time_col], errors='coerce')
                        if time_series.isnull().all():
                            st_obj.error(f"列 '{manual_time_col}' 无法转换为时间格式")
                            selected_time_col = None
                        else:
                            selected_time_col = manual_time_col
                            st_obj.success(f"✅ 手动指定时间列: {manual_time_col}")
                    except Exception as e:
                        st_obj.error(f"时间列转换失败: {e}")
                        selected_time_col = None
                else:
                    selected_time_col = None
        
        with col2:
            if potential_time_cols and selected_time_col:
                # 时间列预览
                st_obj.markdown("**时间列预览:**")
                time_preview = pd.to_datetime(data[selected_time_col])
                preview_df = pd.DataFrame({
                    '原始值': data[selected_time_col].head(10),
                    '转换后': time_preview.head(10)
                })
                st_obj.dataframe(preview_df, use_container_width=True)
        
        return {
            'time_column': selected_time_col,
            'time_info': time_col_info.get(selected_time_col, {}),
            'all_time_info': time_col_info
        }
    
    def render_time_filtering(self, st_obj, data: pd.DataFrame, time_column: str) -> pd.DataFrame:
        """渲染时间筛选界面"""
        
        if not time_column or time_column not in data.columns:
            st_obj.error("请先选择有效的时间列")
            return data
        
        st_obj.markdown("#### 时间范围筛选")
        
        try:
            time_series = pd.to_datetime(data[time_column])
            min_date = time_series.min().date()
            max_date = time_series.max().date()
            
            col1, col2, col3 = st_obj.columns(3)
            
            with col1:
                # 预设时间范围
                st_obj.markdown("**快速选择:**")
                
                if st_obj.button("最近1年", key=f"{self.component_name}_last_1year"):
                    end_date = max_date
                    start_date = (pd.Timestamp(end_date) - pd.DateOffset(years=1)).date()
                    self.set_state('filter_start_date', start_date)
                    self.set_state('filter_end_date', end_date)
                
                if st_obj.button("最近6个月", key=f"{self.component_name}_last_6months"):
                    end_date = max_date
                    start_date = (pd.Timestamp(end_date) - pd.DateOffset(months=6)).date()
                    self.set_state('filter_start_date', start_date)
                    self.set_state('filter_end_date', end_date)
                
                if st_obj.button("最近3个月", key=f"{self.component_name}_last_3months"):
                    end_date = max_date
                    start_date = (pd.Timestamp(end_date) - pd.DateOffset(months=3)).date()
                    self.set_state('filter_start_date', start_date)
                    self.set_state('filter_end_date', end_date)
            
            with col2:
                # 自定义时间范围
                st_obj.markdown("**自定义范围:**")
                
                start_date = st_obj.date_input(
                    "开始日期:",
                    value=self.get_state('filter_start_date', min_date),
                    min_value=min_date,
                    max_value=max_date,
                    key=f"{self.component_name}_start_date"
                )
                
                end_date = st_obj.date_input(
                    "结束日期:",
                    value=self.get_state('filter_end_date', max_date),
                    min_value=min_date,
                    max_value=max_date,
                    key=f"{self.component_name}_end_date"
                )
                
                # 保存到状态
                self.set_state('filter_start_date', start_date)
                self.set_state('filter_end_date', end_date)
            
            with col3:
                # 筛选结果预览
                st_obj.markdown("**筛选预览:**")
                
                if start_date <= end_date:
                    # 应用筛选
                    time_mask = (time_series.dt.date >= start_date) & (time_series.dt.date <= end_date)
                    filtered_count = time_mask.sum()
                    total_count = len(data)
                    
                    st_obj.metric("筛选后行数", filtered_count)
                    st_obj.metric("筛选比例", f"{filtered_count/total_count*100:.1f}%")
                    
                    if st_obj.button("应用时间筛选", key=f"{self.component_name}_apply_filter"):
                        filtered_data = data[time_mask].copy()
                        
                        # 保存到状态
                        self.set_state('filtered_data', filtered_data)
                        self.set_state('filter_applied', True)
                        
                        st_obj.success(f"✅ 时间筛选完成！数据从 {total_count} 行减少到 {len(filtered_data)} 行")
                        
                        return filtered_data
                else:
                    st_obj.error("开始日期不能晚于结束日期")
            
        except Exception as e:
            st_obj.error(f"时间筛选失败: {e}")
            logger.error(f"时间筛选失败: {e}")
        
        return data
    
    def render_frequency_alignment(self, st_obj, data: pd.DataFrame, time_column: str) -> pd.DataFrame:
        """渲染频率对齐界面"""
        
        if not time_column or time_column not in data.columns:
            st_obj.error("请先选择有效的时间列")
            return data
        
        st_obj.markdown("#### 时间频率对齐")
        
        try:
            time_series = pd.to_datetime(data[time_column])
            current_freq = self._infer_frequency(time_series)
            
            col1, col2 = st_obj.columns(2)
            
            with col1:
                st_obj.info(f"当前推断频率: {current_freq}")
                
                # 目标频率选择
                target_freq = st_obj.selectbox(
                    "选择目标频率:",
                    options=['D', 'B', 'W', 'M', 'Q', 'Y'],
                    format_func=lambda x: {
                        'D': '日频率 (D)',
                        'B': '工作日频率 (B)', 
                        'W': '周频率 (W)',
                        'M': '月频率 (M)',
                        'Q': '季度频率 (Q)',
                        'Y': '年频率 (Y)'
                    }[x],
                    key=f"{self.component_name}_target_freq"
                )
                
                # 聚合方法选择
                numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
                if time_column in numeric_cols:
                    numeric_cols.remove(time_column)
                
                if numeric_cols:
                    agg_method = st_obj.selectbox(
                        "数值列聚合方法:",
                        options=['mean', 'sum', 'first', 'last', 'min', 'max'],
                        key=f"{self.component_name}_agg_method"
                    )
                else:
                    agg_method = 'first'
                    st_obj.info("没有数值列，将使用 'first' 方法")
            
            with col2:
                if st_obj.button("预览频率对齐", key=f"{self.component_name}_preview_freq"):
                    aligned_data = self._align_frequency(data, time_column, target_freq, agg_method)
                    
                    if aligned_data is not None:
                        st_obj.success(f"预览：数据从 {len(data)} 行变为 {len(aligned_data)} 行")
                        st_obj.dataframe(aligned_data.head(), use_container_width=True)
                
                if st_obj.button("应用频率对齐", key=f"{self.component_name}_apply_freq"):
                    aligned_data = self._align_frequency(data, time_column, target_freq, agg_method)
                    
                    if aligned_data is not None:
                        # 保存到状态
                        self.set_state('aligned_data', aligned_data)
                        self.set_state('target_frequency', target_freq)
                        
                        st_obj.success(f"✅ 频率对齐完成！数据形状: {aligned_data.shape}")
                        
                        return aligned_data
                    else:
                        st_obj.error("频率对齐失败")
        
        except Exception as e:
            st_obj.error(f"频率对齐失败: {e}")
            logger.error(f"频率对齐失败: {e}")
        
        return data
    
    def _infer_frequency(self, time_series: pd.Series) -> str:
        """推断时间序列的频率"""
        try:
            # 移除缺失值
            clean_series = time_series.dropna().sort_values()
            
            if len(clean_series) < 2:
                return "未知"
            
            # 计算时间差
            diffs = clean_series.diff().dropna()
            mode_diff = diffs.mode()
            
            if mode_diff.empty:
                return "未知"
            
            mode_days = mode_diff.iloc[0].days
            
            # 根据天数推断频率
            if mode_days == 1:
                return "D (日频率)"
            elif mode_days == 7:
                return "W (周频率)"
            elif 28 <= mode_days <= 31:
                return "M (月频率)"
            elif 89 <= mode_days <= 92:
                return "Q (季度频率)"
            elif 365 <= mode_days <= 366:
                return "Y (年频率)"
            else:
                return f"自定义 ({mode_days}天)"
                
        except Exception as e:
            logger.error(f"频率推断失败: {e}")
            return "未知"
    
    def _align_frequency(self, data: pd.DataFrame, time_column: str, 
                        target_freq: str, agg_method: str) -> Optional[pd.DataFrame]:
        """对齐时间频率"""
        try:
            # 设置时间列为索引
            aligned_data = data.copy()
            aligned_data[time_column] = pd.to_datetime(aligned_data[time_column])
            aligned_data = aligned_data.set_index(time_column)
            
            # 根据聚合方法处理不同类型的列
            agg_dict = {}
            for col in aligned_data.columns:
                if aligned_data[col].dtype in ['int64', 'float64']:
                    agg_dict[col] = agg_method
                else:
                    agg_dict[col] = 'first'  # 非数值列使用first
            
            # 重采样
            resampled = aligned_data.resample(target_freq).agg(agg_dict)
            
            # 重置索引
            resampled = resampled.reset_index()
            
            return resampled
            
        except Exception as e:
            logger.error(f"频率对齐失败: {e}")
            return None
    
    def render_processing_interface(self, st_obj, data: pd.DataFrame) -> pd.DataFrame:
        """渲染时间处理界面（实现抽象方法）"""
        return self.render_input_section(st_obj, data=data)

    def render_input_section(self, st_obj, **kwargs) -> Optional[pd.DataFrame]:
        """渲染时间处理输入部分"""

        data = kwargs.get('data')
        if data is None:
            st_obj.error("请先提供数据")
            return None

        # 时间列识别
        time_info = self.render_time_column_identification(st_obj, data)
        time_column = time_info['time_column']

        if not time_column:
            return data

        # 时间筛选
        filtered_data = self.render_time_filtering(st_obj, data, time_column)

        # 频率对齐
        final_data = self.render_frequency_alignment(st_obj, filtered_data, time_column)

        return final_data


__all__ = ['TimeProcessingComponent']
