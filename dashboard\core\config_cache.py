# -*- coding: utf-8 -*-
"""
配置缓存器 - 第二阶段性能优化
实现配置文件的智能缓存，减少重复加载
"""

import streamlit as st
import time
import os
import json
import pickle
import hashlib
from typing import Dict, Any, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path

class ConfigCache:
    """智能配置缓存器"""
    
    def __init__(self, cache_dir: str = "cache", cache_ttl: int = 3600):
        """
        初始化配置缓存器

        Args:
            cache_dir: 缓存目录
            cache_ttl: 缓存生存时间（秒）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_ttl = cache_ttl
        self.memory_cache: Dict[str, Dict[str, Any]] = {}

        # 禁用缓存目录创建 - 只使用内存缓存
        # self.cache_dir.mkdir(exist_ok=True)
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'loads': 0,
            'saves': 0
        }
        

    
    def _get_file_hash(self, file_path: str) -> str:
        """获取文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception:
            return str(int(time.time()))
    
    def _get_cache_key(self, config_name: str, file_path: str = None) -> str:
        """生成缓存键"""
        if file_path and os.path.exists(file_path):
            file_hash = self._get_file_hash(file_path)
            return f"{config_name}_{file_hash}"
        return config_name
    
    def _is_cache_valid(self, cache_info: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        if 'timestamp' not in cache_info:
            return False
        
        cache_time = cache_info['timestamp']
        current_time = time.time()
        
        return (current_time - cache_time) < self.cache_ttl
    
    @st.cache_data(ttl=3600)
    def load_config(_self, config_name: str, file_path: str = None, 
                   loader_func: callable = None, force_reload: bool = False) -> Optional[Any]:
        """
        加载配置（带缓存）
        
        Args:
            config_name: 配置名称
            file_path: 配置文件路径
            loader_func: 自定义加载函数
            force_reload: 是否强制重新加载
            
        Returns:
            配置数据
        """
        cache_key = _self._get_cache_key(config_name, file_path)
        
        # 检查内存缓存
        if not force_reload and cache_key in _self.memory_cache:
            cache_info = _self.memory_cache[cache_key]
            if _self._is_cache_valid(cache_info):
                _self.stats['hits'] += 1
                return cache_info['data']
        
        # 禁用磁盘缓存 - 只使用内存缓存
        # disk_cache_path = _self.cache_dir / f"{cache_key}.cache"
        # if not force_reload and disk_cache_path.exists():
        #     try:
        #         with open(disk_cache_path, 'rb') as f:
        #             cache_info = pickle.load(f)
        #             if _self._is_cache_valid(cache_info):
        #                 # 加载到内存缓存
        #                 _self.memory_cache[cache_key] = cache_info
        #                 _self.stats['hits'] += 1
        #                 print(f"[ConfigCache] 磁盘缓存命中: {config_name}")
        #                 return cache_info['data']
        #     except Exception as e:
        #         print(f"[ConfigCache] 磁盘缓存读取失败: {e}")
        
        # 缓存未命中，重新加载
        _self.stats['misses'] += 1

        
        start_time = time.time()
        
        try:
            # 使用自定义加载函数或默认加载
            if loader_func:
                data = loader_func(file_path) if file_path else loader_func()
            elif file_path:
                data = _self._load_config_file(file_path)
            else:
                raise ValueError("必须提供file_path或loader_func")
            
            # 创建缓存信息
            cache_info = {
                'data': data,
                'timestamp': time.time(),
                'config_name': config_name,
                'file_path': file_path,
                'load_time': time.time() - start_time
            }
            
            # 保存到内存缓存
            _self.memory_cache[cache_key] = cache_info

            # 禁用磁盘缓存保存
            # try:
            #     with open(disk_cache_path, 'wb') as f:
            #         pickle.dump(cache_info, f)
            #     _self.stats['saves'] += 1
            # except Exception as e:
            #     print(f"[ConfigCache] 磁盘缓存保存失败: {e}")
            
            _self.stats['loads'] += 1
            load_time = cache_info['load_time'] * 1000
            return data

        except Exception as e:
            return None
    
    def _load_config_file(self, file_path: str) -> Any:
        """默认配置文件加载器"""
        file_ext = Path(file_path).suffix.lower()
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_ext == '.json':
                return json.load(f)
            elif file_ext in ['.yml', '.yaml']:
                try:
                    import yaml
                    return yaml.safe_load(f)
                except ImportError:
                    raise ImportError("需要安装PyYAML来加载YAML文件")
            else:
                return f.read()
    
    def invalidate_cache(self, config_name: str = None):
        """清除缓存"""
        if config_name:
            # 清除特定配置的缓存
            keys_to_remove = [k for k in self.memory_cache.keys() if k.startswith(config_name)]
            for key in keys_to_remove:
                del self.memory_cache[key]

                # 禁用磁盘缓存删除
                # disk_cache_path = self.cache_dir / f"{key}.cache"
                # if disk_cache_path.exists():
                #     disk_cache_path.unlink()

        else:
            # 清除所有缓存
            self.memory_cache.clear()

            # 禁用磁盘缓存删除
            # for cache_file in self.cache_dir.glob("*.cache"):
            #     cache_file.unlink()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hit_rate': f"{hit_rate:.1f}%",
            'total_requests': total_requests,
            'memory_cache_size': len(self.memory_cache),
            'disk_cache_files': 0,  # 禁用磁盘缓存
            **self.stats
        }
    
    def cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        # 清理内存缓存
        for key, cache_info in self.memory_cache.items():
            if (current_time - cache_info['timestamp']) > self.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self.memory_cache[key]

        # 禁用磁盘缓存清理
        # for cache_file in self.cache_dir.glob("*.cache"):
        #     try:
        #         with open(cache_file, 'rb') as f:
        #             cache_info = pickle.load(f)
        #             if (current_time - cache_info['timestamp']) > self.cache_ttl:
        #                 cache_file.unlink()
        #     except Exception:
        #         # 如果无法读取缓存文件，直接删除
        #         cache_file.unlink()

        if expired_keys:
            pass

# 全局配置缓存实例
@st.cache_resource
def get_config_cache():
    """获取全局配置缓存实例"""
    return ConfigCache()

# DFM配置专用缓存函数
@st.cache_data(ttl=3600)
def load_dfm_config():
    """加载DFM配置（缓存版本）"""
    cache = get_config_cache()
    
    def _load_dfm_config():
        # 这里实现DFM配置的加载逻辑
        from dashboard.state_management.adapters.dfm_adapter import DFMConfig
        return DFMConfig()
    
    return cache.load_config('dfm_config', loader_func=_load_dfm_config)


