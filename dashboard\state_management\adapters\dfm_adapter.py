# -*- coding: utf-8 -*-
"""
DFM (动态因子模型) 适配器
提供与动态因子模型系统的集成接口
"""

import logging
import threading
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import asyncio
from collections import defaultdict, deque

# 导入核心基础设施
from ..core.module_manager import ModuleManager
from ..core.data_flow_controller import DataFlowController
from ..core.state_synchronizer import StateSynchronizer
from ..core.performance_monitor import PerformanceMonitor
from ..core.error_handler import ErrorHandler
from ..core.config_manager import ConfigManager
from ..core.logging_monitor import LoggingMonitor, MetricType


@dataclass
class DataPacket:
    """数据包（临时定义）"""
    data_type: str
    payload: Any
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


class DFMModelType(Enum):
    """DFM模型类型枚举"""
    STATIC = "static"           # 静态因子模型
    DYNAMIC = "dynamic"         # 动态因子模型
    KALMAN = "kalman"          # 卡尔曼滤波模型
    BAYESIAN = "bayesian"      # 贝叶斯模型
    NEURAL = "neural"          # 神经网络模型


class DFMStatus(Enum):
    """DFM状态枚举"""
    IDLE = "idle"              # 空闲
    LOADING = "loading"        # 加载中
    TRAINING = "training"      # 训练中
    PREDICTING = "predicting"  # 预测中
    ERROR = "error"            # 错误
    READY = "ready"            # 就绪


class DataFrequency(Enum):
    """数据频率枚举"""
    DAILY = "daily"            # 日频
    WEEKLY = "weekly"          # 周频
    MONTHLY = "monthly"        # 月频
    QUARTERLY = "quarterly"    # 季频
    YEARLY = "yearly"          # 年频
    REAL_TIME = "real_time"    # 实时


@dataclass
class DFMConfig:
    """DFM配置"""
    model_type: DFMModelType = DFMModelType.DYNAMIC
    n_factors: int = 5
    max_lags: int = 4
    data_frequency: DataFrequency = DataFrequency.MONTHLY
    training_window: int = 120  # 训练窗口（月数）
    prediction_horizon: int = 12  # 预测期（月数）
    update_frequency: int = 1  # 更新频率（月）
    confidence_level: float = 0.95
    convergence_threshold: float = 1e-6
    max_iterations: int = 1000
    enable_real_time: bool = False
    auto_retrain: bool = True
    validation_split: float = 0.2


@dataclass
class DFMResult:
    """DFM结果"""
    model_id: str
    timestamp: datetime
    factors: np.ndarray
    loadings: np.ndarray
    predictions: np.ndarray
    confidence_intervals: np.ndarray
    model_metrics: Dict[str, float]
    status: DFMStatus
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataSeries:
    """数据序列"""
    series_id: str
    name: str
    data: pd.Series
    frequency: DataFrequency
    last_updated: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


class DFMAdapter:
    """DFM适配器"""
    
    def __init__(
        self,
        state_manager=None,  # 添加state_manager参数以兼容调用
        module_manager: ModuleManager = None,
        data_flow_controller: DataFlowController = None,
        state_synchronizer: StateSynchronizer = None,
        performance_monitor: PerformanceMonitor = None,
        error_handler: ErrorHandler = None,
        config_manager: ConfigManager = None,
        logging_monitor: LoggingMonitor = None
    ):
        """
        初始化DFM适配器

        Args:
            state_manager: 统一状态管理器（可选）
            module_manager: 模块管理器
            data_flow_controller: 数据流控制器
            state_synchronizer: 状态同步器
            performance_monitor: 性能监控器
            error_handler: 错误处理器
            config_manager: 配置管理器
            logging_monitor: 日志监控器
        """
        self.state_manager = state_manager  # 存储state_manager引用
        self.module_manager = module_manager
        self.data_flow_controller = data_flow_controller
        self.state_synchronizer = state_synchronizer
        self.performance_monitor = performance_monitor
        self.error_handler = error_handler
        self.config_manager = config_manager
        self.logging_monitor = logging_monitor
        
        # 获取日志器
        self.logger = self.logging_monitor.get_logger(__name__)
        
        # DFM状态
        self._status = DFMStatus.IDLE
        self._current_config: Optional[DFMConfig] = None
        self._models: Dict[str, Any] = {}
        self._data_series: Dict[str, DataSeries] = {}
        self._results: Dict[str, DFMResult] = {}
        
        # 数据缓存
        self._data_cache: Dict[str, pd.DataFrame] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        
        # 回调函数
        self._result_callbacks: List[Callable[[DFMResult], None]] = []
        self._status_callbacks: List[Callable[[DFMStatus], None]] = []
        
        # 统计信息
        self._stats = {
            'total_models': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'data_series_count': 0,
            'last_training_time': None,
            'last_prediction_time': None,
            'average_prediction_time': 0.0
        }
        
        # 线程安全锁
        self._lock = threading.RLock()
        
        # 异步任务队列
        self._task_queue = asyncio.Queue()
        self._worker_running = False
        
        # 状态存储
        self.state_storage: Dict[str, Any] = {}

        # 注册模块
        self._register_module()

        # 加载配置
        self._load_configuration()

        # 设置数据流处理
        self._setup_data_flow()
        
        self.logger.info("DFMAdapter initialized")
    
    def _register_module(self):
        """注册模块"""
        try:
            self.module_manager.register_module(
                name="dfm_adapter",
                version="1.0.0",
                description="DFM Adapter",
                dependencies={"data_flow_controller", "state_synchronizer"}
            )
            self.logger.info("DFM adapter module registered")
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_register_module"})
    
    def _load_configuration(self):
        """加载配置"""
        try:
            # 从配置管理器加载DFM配置
            config_data = {
                'model_type': self.config_manager.get('dfm.model_type', 'dynamic'),
                'n_factors': self.config_manager.get('dfm.n_factors', 5),
                'max_lags': self.config_manager.get('dfm.max_lags', 4),
                'data_frequency': self.config_manager.get('dfm.data_frequency', 'monthly'),
                'training_window': self.config_manager.get('dfm.training_window', 120),
                'prediction_horizon': self.config_manager.get('dfm.prediction_horizon', 12),
                'update_frequency': self.config_manager.get('dfm.update_frequency', 1),
                'confidence_level': self.config_manager.get('dfm.confidence_level', 0.95),
                'convergence_threshold': self.config_manager.get('dfm.convergence_threshold', 1e-6),
                'max_iterations': self.config_manager.get('dfm.max_iterations', 1000),
                'enable_real_time': self.config_manager.get('dfm.enable_real_time', False),
                'auto_retrain': self.config_manager.get('dfm.auto_retrain', True),
                'validation_split': self.config_manager.get('dfm.validation_split', 0.2)
            }
            
            self._current_config = DFMConfig(
                model_type=DFMModelType(config_data['model_type']),
                n_factors=config_data['n_factors'],
                max_lags=config_data['max_lags'],
                data_frequency=DataFrequency(config_data['data_frequency']),
                training_window=config_data['training_window'],
                prediction_horizon=config_data['prediction_horizon'],
                update_frequency=config_data['update_frequency'],
                confidence_level=config_data['confidence_level'],
                convergence_threshold=config_data['convergence_threshold'],
                max_iterations=config_data['max_iterations'],
                enable_real_time=config_data['enable_real_time'],
                auto_retrain=config_data['auto_retrain'],
                validation_split=config_data['validation_split']
            )
            
            self.logger.info(f"DFM configuration loaded: {self._current_config}")
            
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_load_configuration"})
            # 使用默认配置
            self._current_config = DFMConfig()
    
    def _setup_data_flow(self):
        """设置数据流处理"""
        try:
            # 注册数据流处理器
            self.data_flow_controller.register_processor(
                processor_id="dfm_data_processor",
                processor_func=self._process_data_packet,
                data_types=["economic_data", "financial_data", "market_data"]
            )
            
            # 注册状态同步处理器
            self.state_synchronizer.register_state_handler(
                state_key="dfm_status",
                handler=self._handle_status_change
            )
            
            self.logger.info("Data flow processing setup completed")
            
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_setup_data_flow"})
    
    def _process_data_packet(self, packet: DataPacket) -> Optional[DataPacket]:
        """处理数据包"""
        try:
            # 记录性能指标
            with self.performance_monitor.measure_time("dfm_data_processing"):
                # 解析数据包
                if packet.data_type in ["economic_data", "financial_data", "market_data"]:
                    self._update_data_series(packet)
                    
                    # 如果启用实时处理，触发预测
                    if self._current_config and self._current_config.enable_real_time:
                        self._trigger_real_time_prediction(packet)
                
                # 返回处理后的数据包
                return packet
                
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_process_data_packet"})
            return None
    
    def _update_data_series(self, packet: DataPacket):
        """更新数据序列"""
        try:
            with self._lock:
                # 解析数据包中的时间序列数据
                if isinstance(packet.payload, dict) and 'series_data' in packet.payload:
                    series_data = packet.payload['series_data']
                    
                    for series_id, data in series_data.items():
                        # 创建或更新数据序列
                        if isinstance(data, dict) and 'values' in data:
                            series = DataSeries(
                                series_id=series_id,
                                name=data.get('name', series_id),
                                data=pd.Series(data['values']),
                                frequency=DataFrequency(data.get('frequency', 'monthly')),
                                last_updated=datetime.now(),
                                metadata=data.get('metadata', {})
                            )
                            
                            self._data_series[series_id] = series
                            self._stats['data_series_count'] = len(self._data_series)
                            
                            # 记录指标
                            self.logging_monitor.record_metric(
                                "dfm_data_series_updated",
                                1,
                                MetricType.COUNTER,
                                tags={"series_id": series_id}
                            )
                
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_update_data_series"})
    
    def _trigger_real_time_prediction(self, packet: DataPacket):
        """触发实时预测"""
        try:
            # 检查是否有可用的模型
            if not self._models:
                self.logger.warning("No trained models available for real-time prediction")
                return
            
            # 异步执行预测
            asyncio.create_task(self._async_predict())
            
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_trigger_real_time_prediction"})
    
    def _handle_status_change(self, old_state: Any, new_state: Any):
        """处理状态变化"""
        try:
            if isinstance(new_state, dict) and 'dfm_status' in new_state:
                new_status = DFMStatus(new_state['dfm_status'])
                old_status = self._status
                
                if new_status != old_status:
                    self._status = new_status
                    self.logger.info(f"DFM status changed: {old_status} -> {new_status}")
                    
                    # 通知状态回调
                    for callback in self._status_callbacks:
                        try:
                            callback(new_status)
                        except Exception as e:
                            self.logger.error(f"Error in status callback: {e}")
                    
                    # 记录状态变化指标
                    self.logging_monitor.record_metric(
                        "dfm_status_change",
                        1,
                        MetricType.COUNTER,
                        tags={"from_status": old_status.value, "to_status": new_status.value}
                    )
                    
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_handle_status_change"})
    
    async def _async_predict(self):
        """异步预测"""
        try:
            # 设置状态为预测中
            self._set_status(DFMStatus.PREDICTING)
            
            # 执行预测逻辑（这里是模拟实现）
            await asyncio.sleep(0.1)  # 模拟预测计算时间
            
            # 生成模拟预测结果
            result = self._generate_mock_result()
            
            # 存储结果
            with self._lock:
                self._results[result.model_id] = result
                self._stats['successful_predictions'] += 1
                self._stats['last_prediction_time'] = datetime.now()
            
            # 通知结果回调
            for callback in self._result_callbacks:
                try:
                    callback(result)
                except Exception as e:
                    self.logger.error(f"Error in result callback: {e}")
            
            # 设置状态为就绪
            self._set_status(DFMStatus.READY)
            
            # 记录预测指标
            self.logging_monitor.record_metric(
                "dfm_prediction_completed",
                1,
                MetricType.COUNTER,
                tags={"model_id": result.model_id}
            )
            
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "_async_predict"})
            self._set_status(DFMStatus.ERROR)
            with self._lock:
                self._stats['failed_predictions'] += 1
    
    def _generate_mock_result(self) -> DFMResult:
        """生成模拟结果（用于测试）"""
        model_id = f"dfm_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 生成模拟数据
        n_factors = self._current_config.n_factors if self._current_config else 5
        n_series = len(self._data_series) if self._data_series else 10
        prediction_horizon = self._current_config.prediction_horizon if self._current_config else 12
        
        factors = np.random.randn(prediction_horizon, n_factors)
        loadings = np.random.randn(n_series, n_factors)
        predictions = np.random.randn(n_series, prediction_horizon)
        confidence_intervals = np.random.randn(n_series, prediction_horizon, 2)
        
        model_metrics = {
            'mse': np.random.uniform(0.01, 0.1),
            'mae': np.random.uniform(0.05, 0.2),
            'r_squared': np.random.uniform(0.7, 0.95),
            'log_likelihood': np.random.uniform(-100, -50)
        }
        
        return DFMResult(
            model_id=model_id,
            timestamp=datetime.now(),
            factors=factors,
            loadings=loadings,
            predictions=predictions,
            confidence_intervals=confidence_intervals,
            model_metrics=model_metrics,
            status=DFMStatus.READY,
            metadata={
                'n_factors': n_factors,
                'n_series': n_series,
                'prediction_horizon': prediction_horizon,
                'model_type': self._current_config.model_type.value if self._current_config else 'dynamic'
            }
        )
    
    def _set_status(self, status: DFMStatus):
        """设置状态"""
        with self._lock:
            old_status = self._status
            self._status = status
            
            # 同步状态到状态同步器
            self.state_synchronizer.update_state("dfm_status", status.value)
            
            if old_status != status:
                self.logger.info(f"DFM status set to: {status.value}")

    def get_state(self, key: str, default: Any = None) -> Any:
        """获取DFM状态"""
        try:
            value = self.state_storage.get(key, default)

            # 🔥 调试：针对数据相关的键添加调试信息
            if key == "dfm_prepared_data_df" and value is not None:
                if hasattr(value, 'shape'):
                    print(f"🔍 [状态管理器-GET] 键 '{key}': 形状 {value.shape}")
                else:
                    print(f"🔍 [状态管理器-GET] 键 '{key}': 类型 {type(value)}")

            return value
        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "dfm_adapter",
                "function": "get_state",
                "key": key
            })
            return default

    def set_state(self, key: str, value: Any) -> bool:
        """设置DFM状态"""
        try:
            # 🔥 调试：针对数据相关的键添加调试信息
            if key == "dfm_prepared_data_df":
                if value is not None and hasattr(value, 'shape'):
                    print(f"💾 [状态管理器-SET] 键 '{key}': 保存形状 {value.shape}")
                    print(f"    - 列数: {len(value.columns) if hasattr(value, 'columns') else 'N/A'}")
                    print(f"    - 内存: {value.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
                else:
                    print(f"💾 [状态管理器-SET] 键 '{key}': 保存类型 {type(value)}")

            with self._lock:
                self.state_storage[key] = value

            # 触发状态同步
            if self.state_synchronizer:
                self.state_synchronizer.trigger_sync(f"dfm.{key}")

            return True
        except Exception as e:
            self.error_handler.handle_error(e, {
                "module": "dfm_adapter",
                "function": "set_state",
                "key": key
            })
            return False

    def add_data_series(self, series: DataSeries):
        """
        添加数据序列
        
        Args:
            series: 数据序列
        """
        try:
            with self._lock:
                self._data_series[series.series_id] = series
                self._stats['data_series_count'] = len(self._data_series)
                
                self.logger.info(f"Added data series: {series.series_id}")
                
                # 记录指标
                self.logging_monitor.record_metric(
                    "dfm_data_series_added",
                    1,
                    MetricType.COUNTER,
                    tags={"series_id": series.series_id}
                )
                
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "add_data_series"})
    
    def remove_data_series(self, series_id: str) -> bool:
        """
        移除数据序列
        
        Args:
            series_id: 序列ID
            
        Returns:
            bool: 是否成功移除
        """
        try:
            with self._lock:
                if series_id in self._data_series:
                    del self._data_series[series_id]
                    self._stats['data_series_count'] = len(self._data_series)
                    
                    self.logger.info(f"Removed data series: {series_id}")
                    
                    # 记录指标
                    self.logging_monitor.record_metric(
                        "dfm_data_series_removed",
                        1,
                        MetricType.COUNTER,
                        tags={"series_id": series_id}
                    )
                    
                    return True
                
                return False
                
        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "remove_data_series"})
            return False

    def get_data_series(self, series_id: str = None) -> Union[DataSeries, Dict[str, DataSeries], None]:
        """
        获取数据序列

        Args:
            series_id: 序列ID，None表示获取所有序列

        Returns:
            Union[DataSeries, Dict[str, DataSeries], None]: 数据序列
        """
        try:
            with self._lock:
                if series_id:
                    return self._data_series.get(series_id)
                else:
                    return self._data_series.copy()

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "get_data_series"})
            return None

    async def train_model(self, config: DFMConfig = None) -> Optional[str]:
        """
        训练模型

        Args:
            config: DFM配置，None表示使用当前配置

        Returns:
            Optional[str]: 模型ID
        """
        try:
            # 设置状态为训练中
            self._set_status(DFMStatus.TRAINING)

            # 使用提供的配置或当前配置
            if config:
                self._current_config = config

            if not self._current_config:
                raise ValueError("No configuration available for training")

            # 检查数据可用性
            if not self._data_series:
                raise ValueError("No data series available for training")

            # 记录训练开始
            start_time = time.time()
            self.logger.info("Starting model training")

            # 模拟训练过程
            await asyncio.sleep(1.0)  # 模拟训练时间

            # 生成模型ID
            model_id = f"dfm_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 创建模拟模型
            model_data = {
                'model_id': model_id,
                'config': self._current_config,
                'training_data_count': len(self._data_series),
                'training_time': time.time() - start_time,
                'created_at': datetime.now()
            }

            # 存储模型
            with self._lock:
                self._models[model_id] = model_data
                self._stats['total_models'] += 1
                self._stats['last_training_time'] = datetime.now()

            # 设置状态为就绪
            self._set_status(DFMStatus.READY)

            # 记录训练指标
            training_time = time.time() - start_time
            self.logging_monitor.record_metric(
                "dfm_training_time",
                training_time,
                MetricType.TIMER,
                tags={"model_id": model_id}
            )

            self.logging_monitor.record_metric(
                "dfm_model_trained",
                1,
                MetricType.COUNTER,
                tags={"model_id": model_id}
            )

            self.logger.info(f"Model training completed: {model_id}")
            return model_id

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "train_model"})
            self._set_status(DFMStatus.ERROR)
            return None

    async def predict(self, model_id: str = None, horizon: int = None) -> Optional[DFMResult]:
        """
        执行预测

        Args:
            model_id: 模型ID，None表示使用最新模型
            horizon: 预测期，None表示使用配置中的默认值

        Returns:
            Optional[DFMResult]: 预测结果
        """
        try:
            # 设置状态为预测中
            self._set_status(DFMStatus.PREDICTING)

            # 选择模型
            if model_id and model_id not in self._models:
                raise ValueError(f"Model {model_id} not found")

            if not model_id and not self._models:
                raise ValueError("No models available for prediction")

            if not model_id:
                # 使用最新模型
                model_id = max(self._models.keys(), key=lambda k: self._models[k]['created_at'])

            # 记录预测开始
            start_time = time.time()
            self.logger.info(f"Starting prediction with model: {model_id}")

            # 模拟预测过程
            await asyncio.sleep(0.5)  # 模拟预测时间

            # 生成预测结果
            result = self._generate_mock_result()
            result.model_id = model_id

            # 如果指定了预测期，更新结果
            if horizon and self._current_config:
                result.predictions = np.random.randn(len(self._data_series), horizon)
                result.confidence_intervals = np.random.randn(len(self._data_series), horizon, 2)
                result.metadata['prediction_horizon'] = horizon

            # 存储结果
            with self._lock:
                self._results[result.model_id] = result
                self._stats['successful_predictions'] += 1
                self._stats['last_prediction_time'] = datetime.now()

                # 更新平均预测时间
                prediction_time = time.time() - start_time
                if self._stats['average_prediction_time'] == 0:
                    self._stats['average_prediction_time'] = prediction_time
                else:
                    self._stats['average_prediction_time'] = (
                        self._stats['average_prediction_time'] * 0.9 + prediction_time * 0.1
                    )

            # 设置状态为就绪
            self._set_status(DFMStatus.READY)

            # 通知结果回调
            for callback in self._result_callbacks:
                try:
                    callback(result)
                except Exception as e:
                    self.logger.error(f"Error in result callback: {e}")

            # 记录预测指标
            self.logging_monitor.record_metric(
                "dfm_prediction_time",
                prediction_time,
                MetricType.TIMER,
                tags={"model_id": model_id}
            )

            self.logging_monitor.record_metric(
                "dfm_prediction_completed",
                1,
                MetricType.COUNTER,
                tags={"model_id": model_id}
            )

            self.logger.info(f"Prediction completed: {result.model_id}")
            return result

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "predict"})
            self._set_status(DFMStatus.ERROR)
            with self._lock:
                self._stats['failed_predictions'] += 1
            return None

    def get_models(self) -> Dict[str, Any]:
        """
        获取所有模型

        Returns:
            Dict[str, Any]: 模型字典
        """
        try:
            with self._lock:
                return self._models.copy()

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "get_models"})
            return {}

    def get_results(self, model_id: str = None) -> Union[DFMResult, Dict[str, DFMResult], None]:
        """
        获取预测结果

        Args:
            model_id: 模型ID，None表示获取所有结果

        Returns:
            Union[DFMResult, Dict[str, DFMResult], None]: 预测结果
        """
        try:
            with self._lock:
                if model_id:
                    return self._results.get(model_id)
                else:
                    return self._results.copy()

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "get_results"})
            return None

    def get_status(self) -> DFMStatus:
        """
        获取当前状态

        Returns:
            DFMStatus: 当前状态
        """
        return self._status

    def get_config(self) -> Optional[DFMConfig]:
        """
        获取当前配置

        Returns:
            Optional[DFMConfig]: 当前配置
        """
        return self._current_config

    def update_config(self, config: DFMConfig):
        """
        更新配置

        Args:
            config: 新配置
        """
        try:
            with self._lock:
                self._current_config = config

                # 同步配置到配置管理器
                self.config_manager.set('dfm.model_type', config.model_type.value)
                self.config_manager.set('dfm.n_factors', config.n_factors)
                self.config_manager.set('dfm.max_lags', config.max_lags)
                self.config_manager.set('dfm.data_frequency', config.data_frequency.value)
                self.config_manager.set('dfm.training_window', config.training_window)
                self.config_manager.set('dfm.prediction_horizon', config.prediction_horizon)

                self.logger.info("DFM configuration updated")

                # 记录配置更新指标
                self.logging_monitor.record_metric(
                    "dfm_config_updated",
                    1,
                    MetricType.COUNTER
                )

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "update_config"})

    def add_result_callback(self, callback: Callable[[DFMResult], None]):
        """
        添加结果回调函数

        Args:
            callback: 回调函数
        """
        self._result_callbacks.append(callback)

    def add_status_callback(self, callback: Callable[[DFMStatus], None]):
        """
        添加状态回调函数

        Args:
            callback: 回调函数
        """
        self._status_callbacks.append(callback)

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with self._lock:
                stats = self._stats.copy()
                stats.update({
                    'current_status': self._status.value,
                    'models_count': len(self._models),
                    'results_count': len(self._results),
                    'data_series_count': len(self._data_series),
                    'config': self._current_config.__dict__ if self._current_config else None
                })
                return stats

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "get_statistics"})
            return {}

    def clear_cache(self):
        """清除缓存"""
        try:
            with self._lock:
                self._data_cache.clear()
                self._cache_timestamps.clear()

                self.logger.info("DFM cache cleared")

                # 记录缓存清理指标
                self.logging_monitor.record_metric(
                    "dfm_cache_cleared",
                    1,
                    MetricType.COUNTER
                )

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "clear_cache"})

    def export_results(self, format: str = "json") -> str:
        """
        导出结果

        Args:
            format: 导出格式

        Returns:
            str: 导出的数据
        """
        try:
            with self._lock:
                export_data = {
                    'export_timestamp': datetime.now().isoformat(),
                    'models': {},
                    'results': {},
                    'statistics': self.get_statistics()
                }

                # 导出模型信息
                for model_id, model_data in self._models.items():
                    export_data['models'][model_id] = {
                        'model_id': model_data['model_id'],
                        'training_data_count': model_data['training_data_count'],
                        'training_time': model_data['training_time'],
                        'created_at': model_data['created_at'].isoformat()
                    }

                # 导出结果信息
                for result_id, result in self._results.items():
                    export_data['results'][result_id] = {
                        'model_id': result.model_id,
                        'timestamp': result.timestamp.isoformat(),
                        'status': result.status.value,
                        'model_metrics': result.model_metrics,
                        'metadata': result.metadata
                    }

                if format.lower() == "json":
                    return json.dumps(export_data, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"Unsupported export format: {format}")

        except Exception as e:
            self.error_handler.handle_error(e, {"module": "dfm_adapter", "function": "export_results"})
            return "{}"


def create_dfm_config(
    model_type: DFMModelType = DFMModelType.DYNAMIC,
    n_factors: int = 5,
    max_lags: int = 4,
    data_frequency: DataFrequency = DataFrequency.MONTHLY,
    training_window: int = 120,
    prediction_horizon: int = 12,
    **kwargs
) -> DFMConfig:
    """
    创建DFM配置的便捷函数

    Args:
        model_type: 模型类型
        n_factors: 因子数量
        max_lags: 最大滞后期
        data_frequency: 数据频率
        training_window: 训练窗口
        prediction_horizon: 预测期
        **kwargs: 其他参数

    Returns:
        DFMConfig: DFM配置
    """
    return DFMConfig(
        model_type=model_type,
        n_factors=n_factors,
        max_lags=max_lags,
        data_frequency=data_frequency,
        training_window=training_window,
        prediction_horizon=prediction_horizon,
        **kwargs
    )


def create_data_series(
    series_id: str,
    name: str,
    data: Union[List, np.ndarray, pd.Series],
    frequency: DataFrequency = DataFrequency.MONTHLY,
    **kwargs
) -> DataSeries:
    """
    创建数据序列的便捷函数

    Args:
        series_id: 序列ID
        name: 序列名称
        data: 数据
        frequency: 数据频率
        **kwargs: 其他参数

    Returns:
        DataSeries: 数据序列
    """
    if not isinstance(data, pd.Series):
        data = pd.Series(data)

    return DataSeries(
        series_id=series_id,
        name=name,
        data=data,
        frequency=frequency,
        last_updated=datetime.now(),
        metadata=kwargs.get('metadata', {})
    )
