# -*- coding: utf-8 -*-
"""
状态管理接口定义
定义状态管理模块的核心接口
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional


class IStateAdapter(ABC):
    """状态适配器接口"""
    
    @abstractmethod
    def set_state_manager(self, state_manager) -> None:
        """设置状态管理器引用"""
        pass
    
    @abstractmethod
    def get_state(self, key: str, default: Any = None) -> Any:
        """获取状态"""
        pass
    
    @abstractmethod
    def set_state(self, key: str, value: Any) -> bool:
        """设置状态"""
        pass
    
    @abstractmethod
    def delete_state(self, key: str) -> bool:
        """删除状态"""
        pass


class IStateValidator(ABC):
    """状态验证器接口"""
    
    @abstractmethod
    def validate(self, key: str, value: Any) -> bool:
        """验证状态值"""
        pass


class IStateMonitor(ABC):
    """状态监控器接口"""
    
    @abstractmethod
    def on_state_changed(self, key: str, old_value: Any, new_value: Any) -> None:
        """状态变化回调"""
        pass
