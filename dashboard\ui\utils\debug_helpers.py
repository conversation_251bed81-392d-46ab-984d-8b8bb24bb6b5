# -*- coding: utf-8 -*-
"""
UI调试工具函数
提供统一的调试日志功能，支持不同级别的日志输出和调用栈跟踪
"""

import os
import inspect
from datetime import datetime
from typing import Optional

# 全局调试开关 - 可以通过环境变量控制
DEBUG_ENABLED = os.getenv('HFTA_DEBUG', 'false').lower() == 'true'
NAVIGATION_DEBUG_ENABLED = os.getenv('HFTA_NAV_DEBUG', 'false').lower() == 'true'


def debug_log(message: str, level: str = "INFO", include_stack: bool = False, max_stack_depth: int = 3) -> None:
    """
    增强的调试日志函数，包含时间戳和可选的调用栈信息

    Args:
        message: 日志消息
        level: 日志级别 (INFO, DEBUG, WARNING, ERROR, etc.)
        include_stack: 是否包含调用栈信息
        max_stack_depth: 最大调用栈深度
    """
    # 检查全局调试开关
    if not DEBUG_ENABLED:
        return

    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
    caller_info = ""

    if include_stack:
        # 获取调用栈信息
        stack = inspect.stack()
        stack_info = []
        for i in range(1, min(max_stack_depth + 1, len(stack))):
            frame = stack[i]
            func_name = frame.function
            filename = os.path.basename(frame.filename)
            line_no = frame.lineno
            stack_info.append(f"{filename}:{func_name}():{line_no}")
        caller_info = f" [Stack: {' -> '.join(stack_info)}]"

    print(f"[{timestamp}] [{level}] {message}{caller_info}")


def debug_state_change(operation: str, old_value: any, new_value: any, 
                      context: str = "", include_stack: bool = True) -> None:
    """
    专门用于状态变更的调试日志
    
    Args:
        operation: 操作描述
        old_value: 旧值
        new_value: 新值
        context: 上下文信息
        include_stack: 是否包含调用栈信息
    """
    debug_log(
        f"STATE_CHANGE: {operation} | {old_value} -> {new_value} | Context: {context}",
        level="STATE", 
        include_stack=include_stack, 
        max_stack_depth=2
    )


def debug_navigation(event: str, details: str = "", include_stack: bool = True) -> None:
    """
    专门用于导航事件的调试日志

    Args:
        event: 导航事件描述
        details: 详细信息
        include_stack: 是否包含调用栈信息
    """
    # 检查导航调试开关
    if not NAVIGATION_DEBUG_ENABLED:
        return

    debug_log(
        f"NAVIGATION: {event} | {details}",
        level="NAV",
        include_stack=include_stack,
        max_stack_depth=2
    )


def debug_button_click(button_name: str, action: str = "", include_stack: bool = False) -> None:
    """
    专门用于按钮点击的调试日志
    
    Args:
        button_name: 按钮名称
        action: 执行的动作
        include_stack: 是否包含调用栈信息
    """
    debug_log(
        f"BUTTON_CLICK: {button_name} | {action}",
        level="BTN", 
        include_stack=include_stack, 
        max_stack_depth=1
    )


def debug_ui_component(component_name: str, operation: str, details: str = "", 
                      include_stack: bool = False) -> None:
    """
    专门用于UI组件的调试日志
    
    Args:
        component_name: 组件名称
        operation: 操作类型 (render, update, destroy, etc.)
        details: 详细信息
        include_stack: 是否包含调用栈信息
    """
    debug_log(
        f"UI_COMPONENT: {component_name} | {operation} | {details}",
        level="UI", 
        include_stack=include_stack, 
        max_stack_depth=2
    )


def debug_performance(operation: str, duration_ms: float, details: str = "") -> None:
    """
    专门用于性能监控的调试日志
    
    Args:
        operation: 操作描述
        duration_ms: 耗时（毫秒）
        details: 详细信息
    """
    debug_log(
        f"PERFORMANCE: {operation} | {duration_ms:.2f}ms | {details}",
        level="PERF", 
        include_stack=False
    )


def debug_error(error: Exception, context: str = "", include_stack: bool = True) -> None:
    """
    专门用于错误的调试日志
    
    Args:
        error: 异常对象
        context: 错误上下文
        include_stack: 是否包含调用栈信息
    """
    error_msg = f"ERROR: {type(error).__name__}: {str(error)}"
    if context:
        error_msg += f" | Context: {context}"
    
    debug_log(
        error_msg,
        level="ERROR", 
        include_stack=include_stack, 
        max_stack_depth=3
    )


# 为了向后兼容，保持原有的函数签名
__all__ = [
    'debug_log',
    'debug_state_change', 
    'debug_navigation',
    'debug_button_click',
    'debug_ui_component',
    'debug_performance',
    'debug_error'
]
