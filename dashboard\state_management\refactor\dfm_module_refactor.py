# -*- coding: utf-8 -*-
"""
DFM模块重构适配器
将DFM模块迁移到统一状态管理系统
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from .module_refactor_base import ModuleRefactorBase
from ..unified_state_manager import UnifiedStateManager


class DFMModuleRefactor(ModuleRefactorBase):
    """DFM模块重构适配器"""
    
    def __init__(self, unified_manager: UnifiedStateManager = None):
        super().__init__("dfm", unified_manager)
        
        # DFM特定的状态键映射
        self.dfm_state_mapping = {
            # train_model子模块
            'train_model': {
                'uploaded_file': 'uploaded_file',
                'processed_data': 'processed_data',
                'model_config': 'model_config',
                'training_status': 'training_status',
                'training_progress': 'training_progress',
                'training_results': 'training_results',
                'model_parameters': 'model_parameters',
                'validation_results': 'validation_results',
                'selected_variables': 'selected_variables',
                'factor_loadings': 'factor_loadings',
                'forecast_results': 'forecast_results',
                # 从data_prep模块共享的数据键
                'dfm_prepared_data_df': 'dfm_prepared_data_df',
                'dfm_transform_log_obj': 'dfm_transform_log_obj',
                'dfm_industry_map_obj': 'dfm_industry_map_obj',
                'dfm_removed_vars_log_obj': 'dfm_removed_vars_log_obj',
                'dfm_var_type_map_obj': 'dfm_var_type_map_obj'
            },
            # model_analysis子模块
            'model_analysis': {
                'analysis_type': 'analysis_type',
                'analysis_results': 'analysis_results',
                'selected_model': 'selected_model',
                'comparison_results': 'comparison_results',
                'performance_metrics': 'performance_metrics',
                'diagnostic_plots': 'diagnostic_plots',
                'residual_analysis': 'residual_analysis'
            },
            # news_analysis子模块
            'news_analysis': {
                'news_data': 'news_data',
                'sentiment_analysis': 'sentiment_analysis',
                'nowcasting_results': 'nowcasting_results',
                'news_impact': 'news_impact',
                'text_processing_status': 'text_processing_status',
                'keyword_extraction': 'keyword_extraction'
            },
            # data_prep子模块
            'data_prep': {
                'raw_data': 'raw_data',
                'cleaned_data': 'cleaned_data',
                'preprocessing_steps': 'preprocessing_steps',
                'data_quality_report': 'data_quality_report',
                'missing_data_handling': 'missing_data_handling',
                'outlier_detection': 'outlier_detection',
                'data_transformation': 'data_transformation',
                # 🔥 新增：关键数据键映射
                'dfm_prepared_data_df': 'dfm_prepared_data_df',
                'dfm_transform_log_obj': 'dfm_transform_log_obj',
                'dfm_industry_map_obj': 'dfm_industry_map_obj',
                'dfm_removed_vars_log_obj': 'dfm_removed_vars_log_obj',
                'dfm_var_type_map_obj': 'dfm_var_type_map_obj'
            }
        }
    
    def _initialize_module(self):
        """初始化DFM模块"""
        # 检查是否已经初始化过，避免重复初始化
        if hasattr(self, '_module_initialized') and self._module_initialized:
            return

        self.log_debug("Initializing DFM module refactor")

        # 注册DFM导航项
        self._setup_navigation()

        # 初始化默认状态
        self._setup_default_states()

        # 标记为已初始化
        self._module_initialized = True
    
    def _setup_navigation(self):
        """设置DFM模块导航"""
        # 添加主DFM导航项
        self.add_navigation_item(
            item_id="dfm",
            label="动态因子模型",
            path="/dfm",
            icon="📊",
            order=1
        )
        
        # 添加子模块导航项
        submodules = [
            ("dfm_train", "模型训练", "/dfm/train", "🏋️"),
            ("dfm_analysis", "模型分析", "/dfm/analysis", "📈"),
            ("dfm_news", "新闻分析", "/dfm/news", "📰"),
            ("dfm_data_prep", "数据准备", "/dfm/data_prep", "🔧")
        ]
        
        for i, (item_id, label, path, icon) in enumerate(submodules):
            self.add_navigation_item(
                item_id=item_id,
                label=label,
                path=path,
                icon=icon,
                parent_id="dfm",
                order=i + 1
            )
    
    def _setup_default_states(self):
        """设置默认状态"""
        # 设置默认的DFM配置
        default_config = {
            'max_factors': 3,
            'max_lags': 4,
            'standardize': True,
            'em_convergence_tol': 1e-6,
            'max_em_iterations': 1000
        }
        
        if not self.get_state('default_config'):
            self.set_state('default_config', default_config)
    
    def _get_from_unified_manager(self, key: str, default: Any = None) -> Any:
        """从统一状态管理器获取DFM状态"""
        if self.unified_manager:
            try:
                # 尝试多个可能的键格式，使用统一状态管理器而不是直接访问session_state
                for possible_key in [f"dfm.{key}", key, f"dfm_{key}"]:
                    result = self.unified_manager.get_state(possible_key, None)
                    if result is not None:
                        return result
                return default
            except Exception as e:
                self.log_warning(f"Failed to get state from unified manager for key {key}: {e}")
                return default
        return default

    def _set_to_unified_manager(self, key: str, value: Any) -> bool:
        """设置DFM状态到统一状态管理器"""
        if self.unified_manager:
            try:
                # 使用统一状态管理器设置状态，保持命名空间
                self.unified_manager.set_state(f"dfm.{key}", value)
                # 为兼容性保留无前缀的键
                self.unified_manager.set_state(key, value)
                return True
            except Exception as e:
                self.log_warning(f"Failed to set state to unified manager for key {key}: {e}")
                return False
        return False
    
    def _delete_from_unified_manager(self, key: str) -> bool:
        """从统一状态管理器删除DFM状态"""
        if not self.unified_manager:
            return False

        # 尝试从DFM命名空间删除
        if hasattr(self.unified_manager, 'delete_dfm_state'):
            return self.unified_manager.delete_dfm_state(key)

        # 回退到全局状态删除
        if hasattr(self.unified_manager, 'delete_state'):
            return self.unified_manager.delete_state(f"dfm.{key}")

        return True  # 假设删除成功

    def _clear_unified_manager_state(self):
        """清空统一状态管理器中的DFM状态"""
        if not self.unified_manager:
            return

        # 尝试清空DFM命名空间
        if hasattr(self.unified_manager, 'clear_dfm_states'):
            self.unified_manager.clear_dfm_states()
        elif hasattr(self.unified_manager, 'clear_module_states'):
            self.unified_manager.clear_module_states('dfm')
    
    # DFM特定的状态管理方法
    def get_dfm_state(self, submodule: str, key: str, default: Any = None) -> Any:
        """获取DFM子模块状态"""
        # 🔥 修复：防止循环引用，直接使用组合键
        full_key = f"dfm.{submodule}.{key}"
        return self.get_state(full_key, default)

    def set_dfm_state(self, submodule: str, key: str, value: Any) -> bool:
        """设置DFM子模块状态"""
        # 🔥 修复：防止循环引用，直接使用组合键
        full_key = f"dfm.{submodule}.{key}"
        return self.set_state(full_key, value)

    def get_submodule_state(self, submodule: str, key: str, default: Any = None) -> Any:
        """获取子模块状态（兼容性方法）"""
        return self.get_dfm_state(submodule, key, default)
    
    def set_submodule_state(self, submodule: str, key: str, value: Any) -> bool:
        """设置子模块状态"""
        if submodule in self.dfm_state_mapping:
            if key in self.dfm_state_mapping[submodule]:
                unified_key = self.dfm_state_mapping[submodule][key]
                return self.set_state(unified_key, value)
        
        # 回退到直接键设置
        full_key = f"{submodule}.{key}"
        return self.set_state(full_key, value)
    
    # 模型管理方法
    def create_model(self, model_config: Dict[str, Any]) -> str:
        """创建DFM模型"""
        if self.unified_manager:
            model_id = self.unified_manager.create_dfm_model(model_config)
            if model_id:
                self.log_info(f"Created DFM model: {model_id}")
                self.record_metric("dfm_model_created", 1)
            return model_id
        return ""
    
    def get_model_config(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型配置"""
        if self.unified_manager:
            return self.unified_manager.get_dfm_model_config(model_id)
        return None
    
    def update_training_progress(self, progress: float, status: str = None):
        """更新训练进度"""
        self.set_state('training_progress', progress)
        if status:
            self.set_state('training_status', status)
        
        self.record_metric("dfm_training_progress", progress)
        self.log_info(f"Training progress updated: {progress:.2%}")
    
    def save_training_results(self, results: Dict[str, Any]):
        """保存训练结果"""
        self.set_state('training_results', results)
        self.store_data('latest_training_results', results, 'file')
        
        self.log_info("Training results saved")
        self.record_metric("dfm_training_completed", 1)
    
    def get_training_results(self) -> Optional[Dict[str, Any]]:
        """获取训练结果"""
        # 首先尝试从状态获取
        results = self.get_state('training_results')
        if results:
            return results
        
        # 然后尝试从存储获取
        return self.retrieve_data('latest_training_results')
    
    # 数据处理方法
    def upload_data(self, data: Any, filename: str = None):
        """上传数据"""
        self.set_state('uploaded_file', filename)
        self.set_state('raw_data', data)
        
        self.log_info(f"Data uploaded: {filename}")
        self.record_metric("dfm_data_uploaded", 1)
    
    def process_data(self, processing_config: Dict[str, Any] = None):
        """处理数据"""
        timer_id = self.start_timer("data_processing")
        
        try:
            # 这里应该调用实际的数据处理逻辑
            raw_data = self.get_state('raw_data')
            if raw_data is not None:
                # 模拟数据处理
                processed_data = raw_data  # 实际处理逻辑应该在这里
                self.set_state('processed_data', processed_data)
                
                self.log_info("Data processing completed")
                self.record_metric("dfm_data_processed", 1)
                
        except Exception as e:
            self.handle_error(e, {'operation': 'data_processing'})
        finally:
            self.end_timer(timer_id)
    
    def get_processed_data(self) -> Any:
        """获取处理后的数据"""
        return self.get_state('processed_data')
    
    # 分析方法
    def run_analysis(self, analysis_type: str, parameters: Dict[str, Any] = None):
        """运行分析"""
        timer_id = self.start_timer(f"analysis_{analysis_type}")
        
        try:
            self.set_submodule_state('model_analysis', 'analysis_type', analysis_type)
            
            # 这里应该调用实际的分析逻辑
            # 模拟分析结果
            results = {
                'analysis_type': analysis_type,
                'parameters': parameters or {},
                'timestamp': datetime.now(),
                'status': 'completed'
            }
            
            self.set_submodule_state('model_analysis', 'analysis_results', results)
            
            self.log_info(f"Analysis completed: {analysis_type}")
            self.record_metric(f"dfm_analysis_{analysis_type}", 1)
            
        except Exception as e:
            self.handle_error(e, {'operation': 'analysis', 'type': analysis_type})
        finally:
            self.end_timer(timer_id)
    
    def get_analysis_results(self, analysis_type: str = None) -> Optional[Dict[str, Any]]:
        """获取分析结果"""
        if analysis_type:
            results = self.get_submodule_state('model_analysis', 'analysis_results')
            if results and results.get('analysis_type') == analysis_type:
                return results
            return None
        else:
            return self.get_submodule_state('model_analysis', 'analysis_results')
    
    # 新闻分析方法
    def process_news_data(self, news_data: Any):
        """处理新闻数据"""
        timer_id = self.start_timer("news_processing")
        
        try:
            self.set_submodule_state('news_analysis', 'news_data', news_data)
            self.set_submodule_state('news_analysis', 'text_processing_status', 'processing')
            
            # 这里应该调用实际的新闻处理逻辑
            # 模拟处理结果
            sentiment_results = {
                'positive': 0.6,
                'negative': 0.2,
                'neutral': 0.2,
                'timestamp': datetime.now()
            }
            
            self.set_submodule_state('news_analysis', 'sentiment_analysis', sentiment_results)
            self.set_submodule_state('news_analysis', 'text_processing_status', 'completed')
            
            self.log_info("News data processing completed")
            self.record_metric("dfm_news_processed", 1)
            
        except Exception as e:
            self.handle_error(e, {'operation': 'news_processing'})
            self.set_submodule_state('news_analysis', 'text_processing_status', 'error')
        finally:
            self.end_timer(timer_id)
    
    def get_news_analysis_results(self) -> Optional[Dict[str, Any]]:
        """获取新闻分析结果"""
        return {
            'sentiment_analysis': self.get_submodule_state('news_analysis', 'sentiment_analysis'),
            'nowcasting_results': self.get_submodule_state('news_analysis', 'nowcasting_results'),
            'news_impact': self.get_submodule_state('news_analysis', 'news_impact'),
            'processing_status': self.get_submodule_state('news_analysis', 'text_processing_status')
        }
    
    # 兼容性方法
    def migrate_legacy_state(self):
        """迁移旧版本的状态"""
        # 定义旧状态键到新状态键的映射
        legacy_mapping = {
            'dfm.uploaded_file': 'uploaded_file',
            'dfm.processed_data': 'processed_data',
            'dfm.model_config': 'model_config',
            'dfm.training_results': 'training_results',
            # 添加更多映射...
        }
        
        self.migrate_session_state(legacy_mapping)
        self.log_info("Legacy state migration completed")
