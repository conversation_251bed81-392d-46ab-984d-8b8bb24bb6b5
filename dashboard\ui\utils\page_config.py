# -*- coding: utf-8 -*-
"""
页面配置管理工具
提供安全的Streamlit页面配置设置功能，避免重复配置错误
"""

import streamlit as st
from typing import Optional, Dict, Any
import logging

# 设置logger
logger = logging.getLogger(__name__)


def safe_set_page_config(
    page_title: str = "经济运行分析平台",
    page_icon: str = "📈", 
    layout: str = "wide",
    initial_sidebar_state: str = "expanded",
    menu_items: Optional[Dict[str, Any]] = None
) -> bool:
    """
    安全地设置页面配置，避免重复调用错误
    
    Args:
        page_title: 页面标题
        page_icon: 页面图标
        layout: 页面布局 ("wide" 或 "centered")
        initial_sidebar_state: 初始侧边栏状态 ("expanded" 或 "collapsed")
        menu_items: 菜单项配置
        
    Returns:
        bool: 配置是否成功设置
    """
    try:
        # 检查session_state中是否已标记页面配置已设置
        if get_page_config_status():
            return True
        
        # 验证参数
        validated_config = _validate_config_parameters(
            page_title, page_icon, layout, initial_sidebar_state, menu_items
        )
        
        # 尝试设置页面配置
        st.set_page_config(**validated_config)
        
        # 标记页面配置已设置
        _set_page_config_status(True)
        return True

    except Exception as e:
        # 如果出现错误（比如已经设置过），记录但不中断程序
        # 即使失败也标记为已设置，避免重复尝试
        _set_page_config_status(True)
        return False


def _set_page_config_status(status: bool) -> None:
    """设置页面配置状态"""
    try:
        # 直接使用session_state，避免循环导入
        st.session_state['dashboard_page_config_set'] = status
    except Exception as e:
        pass  # 静默处理错误


def get_page_config_status() -> bool:
    """
    获取页面配置状态

    Returns:
        bool: 页面配置是否已设置
    """
    try:
        # 直接使用session_state，避免循环导入
        return st.session_state.get('dashboard_page_config_set', False)
    except Exception as e:
        return False


def reset_page_config_flag() -> None:
    """
    重置页面配置标志
    注意：这不会重置实际的页面配置，只是清除标志
    """
    try:
        # 优先使用统一状态管理器
        from dashboard.state_management import get_unified_manager

        state_manager = get_unified_manager()
        if state_manager:
            state_manager.clear_state('dashboard.page.config_set')
            return

        # 降级到session_state
        pass  # 静默处理

    except Exception as e:
        pass  # 静默处理错误

    # 降级实现
    if 'dashboard_page_config_set' in st.session_state:
        del st.session_state['dashboard_page_config_set']


def _validate_config_parameters(
    page_title: str,
    page_icon: str, 
    layout: str,
    initial_sidebar_state: str,
    menu_items: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    验证和清理配置参数
    
    Args:
        page_title: 页面标题
        page_icon: 页面图标
        layout: 页面布局
        initial_sidebar_state: 初始侧边栏状态
        menu_items: 菜单项配置
        
    Returns:
        Dict[str, Any]: 验证后的配置参数
    """
    # 验证layout参数
    valid_layouts = ["wide", "centered"]
    if layout not in valid_layouts:
        layout = "wide"  # 默认值
    
    # 验证sidebar状态
    valid_sidebar_states = ["expanded", "collapsed", "auto"]
    if initial_sidebar_state not in valid_sidebar_states:
        initial_sidebar_state = "expanded"  # 默认值
    
    # 构建配置字典
    config = {
        'page_title': page_title,
        'page_icon': page_icon,
        'layout': layout,
        'initial_sidebar_state': initial_sidebar_state
    }
    
    # 添加菜单项（如果提供）
    if menu_items is not None:
        config['menu_items'] = menu_items
    
    return config


def get_default_config() -> Dict[str, Any]:
    """
    获取默认页面配置
    
    Returns:
        Dict[str, Any]: 默认配置参数
    """
    return {
        'page_title': "经济运行分析平台",
        'page_icon': "📈",
        'layout': "wide",
        'initial_sidebar_state': "expanded"
    }


def set_custom_config(config: Dict[str, Any]) -> bool:
    """
    使用自定义配置设置页面
    
    Args:
        config: 自定义配置字典
        
    Returns:
        bool: 配置是否成功设置
    """
    default_config = get_default_config()
    default_config.update(config)
    
    return safe_set_page_config(**default_config)


# 为了向后兼容，保持原有的函数名
def _safe_set_page_config(**kwargs) -> bool:
    """向后兼容的函数名"""
    return safe_set_page_config(**kwargs)


__all__ = [
    'safe_set_page_config',
    'get_page_config_status', 
    'reset_page_config_flag',
    'get_default_config',
    'set_custom_config',
    '_safe_set_page_config'  # 向后兼容
]
