# -*- coding: utf-8 -*-
"""
数据探索适配器
提供数据探索模块的统一状态管理接口
"""

import logging
from typing import Any, Optional, Dict, List
import pandas as pd
from datetime import datetime

from ..unified_state_manager import UnifiedStateManager


class DataExplorationAdapter:
    """数据探索适配器 - 提供统一的状态管理接口"""
    
    # 状态键常量
    CURRENT_MODULE_KEY = "data_exploration.current_module"
    ACTIVE_TAB_KEY = "data_exploration.active_tab"
    FORCE_CARD_VIEW_KEY = "data_exploration.force_card_view"
    CLEARING_STATE_KEY = "data_exploration.clearing_state"
    
    # 模块常量
    MODULE_STATIONARITY = "stationarity"
    MODULE_TIME_LAG_CORR = "time_lag_corr"
    MODULE_LEAD_LAG = "lead_lag"
    
    def __init__(self, unified_manager: UnifiedStateManager):
        """初始化数据探索适配器"""
        self.unified_manager = unified_manager
        self.logger = logging.getLogger(__name__)
        
    def set_current_module(self, module_name: str) -> bool:
        """设置当前激活模块"""
        try:
            success = self.unified_manager.set_state(self.CURRENT_MODULE_KEY, module_name)
            if success:
                self.logger.debug(f"Current module set to: {module_name}")
            return success
        except Exception as e:
            self.logger.error(f"Error setting current module {module_name}: {e}")
            return False
    
    def get_current_module(self) -> Optional[str]:
        """获取当前激活模块"""
        try:
            return self.unified_manager.get_state(self.CURRENT_MODULE_KEY)
        except Exception as e:
            self.logger.error(f"Error getting current module: {e}")
            return None
    
    def set_active_tab(self, tab_name: str) -> bool:
        """设置活跃标签页"""
        try:
            success = self.unified_manager.set_state(self.ACTIVE_TAB_KEY, tab_name)
            if success:
                self.logger.debug(f"Active tab set to: {tab_name}")
            return success
        except Exception as e:
            self.logger.error(f"Error setting active tab {tab_name}: {e}")
            return False
    
    def get_active_tab(self) -> Optional[str]:
        """获取活跃标签页"""
        try:
            return self.unified_manager.get_state(self.ACTIVE_TAB_KEY)
        except Exception as e:
            self.logger.error(f"Error getting active tab: {e}")
            return None
    
    def has_active_tab(self) -> bool:
        """检查是否有活跃标签页"""
        try:
            active_tab = self.get_active_tab()
            return active_tab is not None and active_tab != ""
        except Exception as e:
            self.logger.error(f"Error checking active tab: {e}")
            return False
    
    def is_clearing_state(self) -> bool:
        """检查是否正在清除状态"""
        try:
            return self.unified_manager.get_state(self.CLEARING_STATE_KEY, False)
        except Exception as e:
            self.logger.error(f"Error checking clearing state: {e}")
            return False
    
    def set_module_data(self, module_name: str, data: pd.DataFrame, 
                       file_name: str = None, data_source: str = "upload") -> bool:
        """设置模块数据"""
        try:
            data_key = f"data_exploration.{module_name}.data"
            info_key = f"data_exploration.{module_name}.info"
            
            # 设置数据
            data_success = self.unified_manager.set_state(data_key, data)
            
            # 设置元信息
            info = {
                'file_name': file_name,
                'data_source': data_source,
                'timestamp': datetime.now().isoformat(),
                'shape': data.shape if data is not None else None
            }
            info_success = self.unified_manager.set_state(info_key, info)
            
            success = data_success and info_success
            if success:
                self.logger.debug(f"Data set for module {module_name}: {data.shape if data is not None else 'None'}")
            return success
            
        except Exception as e:
            self.logger.error(f"Error setting data for module {module_name}: {e}")
            return False
    
    def get_module_data(self, module_name: str) -> Optional[pd.DataFrame]:
        """获取模块数据"""
        try:
            data_key = f"data_exploration.{module_name}.data"
            data = self.unified_manager.get_state(data_key)
            
            if data is not None:
                self.logger.debug(f"Data retrieved for module {module_name}: {data.shape}")
            else:
                self.logger.debug(f"No data found for module {module_name}")
            
            return data
        except Exception as e:
            self.logger.error(f"Error getting data for module {module_name}: {e}")
            return None
    
    def get_module_info(self, module_name: str) -> Dict[str, Any]:
        """获取模块完整信息"""
        try:
            data_key = f"data_exploration.{module_name}.data"
            info_key = f"data_exploration.{module_name}.info"
            
            data = self.unified_manager.get_state(data_key)
            info = self.unified_manager.get_state(info_key, {})
            
            result = {
                'data': data,
                'file_name': info.get('file_name'),
                'data_source': info.get('data_source'),
                'timestamp': info.get('timestamp'),
                'shape': info.get('shape')
            }
            
            return result
        except Exception as e:
            self.logger.error(f"Error getting module info for {module_name}: {e}")
            return {}
    
    def clear_module_data(self, module_name: str) -> bool:
        """清除指定模块的数据"""
        try:
            data_key = f"data_exploration.{module_name}.data"
            info_key = f"data_exploration.{module_name}.info"
            
            data_success = self.unified_manager.delete_state(data_key)
            info_success = self.unified_manager.delete_state(info_key)
            
            success = data_success and info_success
            if success:
                self.logger.debug(f"Data cleared for module {module_name}")
            return success
            
        except Exception as e:
            self.logger.error(f"Error clearing data for module {module_name}: {e}")
            return False
    
    def clear_all_states(self) -> bool:
        """清除所有数据探索状态"""
        try:
            # 设置清除状态标志
            self.unified_manager.set_state(self.CLEARING_STATE_KEY, True)
            
            # 清除各模块数据
            modules = [self.MODULE_STATIONARITY, self.MODULE_TIME_LAG_CORR, self.MODULE_LEAD_LAG]
            for module in modules:
                self.clear_module_data(module)
            
            # 清除全局状态
            self.unified_manager.delete_state(self.CURRENT_MODULE_KEY)
            self.unified_manager.delete_state(self.ACTIVE_TAB_KEY)
            self.unified_manager.delete_state(self.FORCE_CARD_VIEW_KEY)
            
            # 清除清除状态标志
            self.unified_manager.delete_state(self.CLEARING_STATE_KEY)
            
            self.logger.info("All data exploration states cleared")
            return True
            
        except Exception as e:
            self.logger.error(f"Error clearing all states: {e}")
            return False
    
    def get_all_modules_data(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模块的数据"""
        try:
            modules = [self.MODULE_STATIONARITY, self.MODULE_TIME_LAG_CORR, self.MODULE_LEAD_LAG]
            result = {}
            
            for module in modules:
                result[module] = self.get_module_info(module)
            
            return result
        except Exception as e:
            self.logger.error(f"Error getting all modules data: {e}")
            return {}


# 全局实例缓存
_data_exploration_adapter = None


def get_data_exploration_adapter() -> Optional[DataExplorationAdapter]:
    """获取数据探索适配器的全局实例"""
    global _data_exploration_adapter

    if _data_exploration_adapter is None:
        try:
            # 使用正确的导入路径
            from .. import get_unified_manager
            unified_manager = get_unified_manager()

            if unified_manager is None:
                logging.getLogger(__name__).error("Unified manager not available")
                return None

            _data_exploration_adapter = DataExplorationAdapter(unified_manager)
            logging.getLogger(__name__).info("DataExplorationAdapter initialized successfully")

        except Exception as e:
            logging.getLogger(__name__).error(f"Failed to initialize DataExplorationAdapter: {e}")
            return None

    return _data_exploration_adapter


def reset_data_exploration_adapter():
    """重置数据探索适配器实例（用于测试）"""
    global _data_exploration_adapter
    _data_exploration_adapter = None


__all__ = ['DataExplorationAdapter', 'get_data_exploration_adapter', 'reset_data_exploration_adapter']
