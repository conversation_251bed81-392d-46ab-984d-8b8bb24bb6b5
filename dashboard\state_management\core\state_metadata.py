# -*- coding: utf-8 -*-
"""
状态元数据管理
提供状态的元数据跟踪和管理功能
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Set, Optional
from enum import Enum
import pandas as pd

class StateScope(Enum):
    """状态作用域枚举"""
    GLOBAL = "global"           # 全局状态，所有模块可访问
    MODULE = "module"           # 模块私有状态
    SHARED = "shared"           # 模块间共享状态
    TEMPORARY = "temporary"     # 临时状态，会话结束时清理
    CACHED = "cached"           # 缓存状态，有TTL

class DataType(Enum):
    """数据类型枚举"""
    DATAFRAME = "dataframe"
    DICT = "dict"
    LIST = "list"
    SCALAR = "scalar"
    FILE = "file"
    METADATA = "metadata"

@dataclass
class StateMetadata:
    """状态元数据"""
    key: str
    scope: StateScope
    data_type: DataType
    module_owner: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_accessed: datetime = field(default_factory=datetime.now)
    last_modified: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    size_bytes: int = 0
    ttl_seconds: Optional[int] = None
    dependencies: Set[str] = field(default_factory=set)
    subscribers: Set[str] = field(default_factory=set)
    tags: Set[str] = field(default_factory=set)
    description: str = ""
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def update_modification(self):
        """更新修改信息"""
        self.last_modified = datetime.now()
        self.last_accessed = datetime.now()
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl_seconds is None:
            return False
        
        elapsed = (datetime.now() - self.last_modified).total_seconds()
        return elapsed > self.ttl_seconds
    
    def add_dependency(self, key: str):
        """添加依赖"""
        self.dependencies.add(key)
    
    def remove_dependency(self, key: str):
        """移除依赖"""
        self.dependencies.discard(key)
    
    def add_subscriber(self, key: str):
        """添加订阅者"""
        self.subscribers.add(key)
    
    def remove_subscriber(self, key: str):
        """移除订阅者"""
        self.subscribers.discard(key)
    
    def add_tag(self, tag: str):
        """添加标签"""
        self.tags.add(tag)
    
    def remove_tag(self, tag: str):
        """移除标签"""
        self.tags.discard(tag)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'key': self.key,
            'scope': self.scope.value,
            'data_type': self.data_type.value,
            'module_owner': self.module_owner,
            'created_at': self.created_at.isoformat(),
            'last_accessed': self.last_accessed.isoformat(),
            'last_modified': self.last_modified.isoformat(),
            'access_count': self.access_count,
            'size_bytes': self.size_bytes,
            'ttl_seconds': self.ttl_seconds,
            'dependencies': list(self.dependencies),
            'subscribers': list(self.subscribers),
            'tags': list(self.tags),
            'description': self.description
        }

class MetadataManager:
    """元数据管理器"""
    
    def __init__(self):
        self._metadata_registry = {}
    
    def register_metadata(self, metadata: StateMetadata) -> bool:
        """注册元数据"""
        try:
            self._metadata_registry[metadata.key] = metadata
            return True
        except Exception:
            return False
    
    def get_metadata(self, key: str) -> Optional[StateMetadata]:
        """获取元数据"""
        return self._metadata_registry.get(key)
    
    def update_metadata(self, key: str, **kwargs) -> bool:
        """更新元数据"""
        try:
            if key in self._metadata_registry:
                metadata = self._metadata_registry[key]
                for attr, value in kwargs.items():
                    if hasattr(metadata, attr):
                        setattr(metadata, attr, value)
                return True
            return False
        except Exception:
            return False
    
    def remove_metadata(self, key: str) -> bool:
        """移除元数据"""
        try:
            if key in self._metadata_registry:
                del self._metadata_registry[key]
            return True
        except Exception:
            return False
    
    def list_keys(self) -> list:
        """列出所有键"""
        return list(self._metadata_registry.keys())
    
    def get_by_scope(self, scope: StateScope) -> list:
        """按作用域获取元数据"""
        return [
            metadata for metadata in self._metadata_registry.values()
            if metadata.scope == scope
        ]
    
    def get_by_module(self, module_name: str) -> list:
        """按模块获取元数据"""
        return [
            metadata for metadata in self._metadata_registry.values()
            if metadata.module_owner == module_name
        ]
    
    def get_by_data_type(self, data_type: DataType) -> list:
        """按数据类型获取元数据"""
        return [
            metadata for metadata in self._metadata_registry.values()
            if metadata.data_type == data_type
        ]
    
    def get_expired_keys(self) -> list:
        """获取过期的键"""
        return [
            key for key, metadata in self._metadata_registry.items()
            if metadata.is_expired()
        ]
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        total_count = len(self._metadata_registry)
        scope_counts = {}
        type_counts = {}
        
        for metadata in self._metadata_registry.values():
            scope_counts[metadata.scope.value] = scope_counts.get(metadata.scope.value, 0) + 1
            type_counts[metadata.data_type.value] = type_counts.get(metadata.data_type.value, 0) + 1
        
        return {
            'total_count': total_count,
            'scope_distribution': scope_counts,
            'type_distribution': type_counts,
            'expired_count': len(self.get_expired_keys())
        }

def infer_data_type(value) -> DataType:
    """推断数据类型"""
    if isinstance(value, pd.DataFrame):
        return DataType.DATAFRAME
    elif isinstance(value, dict):
        return DataType.DICT
    elif isinstance(value, list):
        return DataType.LIST
    else:
        return DataType.SCALAR

def calculate_data_size(value) -> int:
    """计算数据大小（字节）"""
    try:
        if isinstance(value, pd.DataFrame):
            return value.memory_usage(deep=True).sum()
        elif isinstance(value, (dict, list)):
            import pickle
            return len(pickle.dumps(value))
        else:
            return len(str(value).encode())
    except Exception:
        return 0
