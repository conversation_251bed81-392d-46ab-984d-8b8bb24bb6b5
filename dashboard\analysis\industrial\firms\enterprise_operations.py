"""
Industrial Enterprise Operations Analysis Module
工业企业经营分析模块
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from typing import Optional


def read_profit_breakdown_data(uploaded_file) -> Optional[pd.DataFrame]:
    """
    读取Excel文件中的"分上中下游利润拆解"sheet数据

    Args:
        uploaded_file: 上传的Excel文件对象或文件路径

    Returns:
        DataFrame: 包含分上中下游利润拆解数据，如果读取失败则返回None
    """
    try:
        # 处理不同类型的文件输入
        file_input = uploaded_file

        # 如果是自定义对象，尝试获取文件内容
        if hasattr(uploaded_file, 'getvalue'):
            try:
                from io import BytesIO
                file_input = BytesIO(uploaded_file.getvalue())
            except:
                pass
        elif hasattr(uploaded_file, 'path'):
            # 如果有path属性，使用文件路径
            file_input = uploaded_file.path

        # 读取"分上中下游利润拆解"工作表
        # 使用header=1读取，第2行作为列名（索引为1）
        # 跳过频率和单位行，从第5行开始读取数据（skiprows=3表示跳过前3行，然后header=1表示第4行作为列名）
        df_profit_breakdown = pd.read_excel(file_input, sheet_name='分上中下游利润拆解', header=1, skiprows=[2, 3])

        # 检查数据是否为空
        if df_profit_breakdown.empty:
            return None

        # 清理列名中的空值和无效字符
        new_columns = []
        for col in df_profit_breakdown.columns:
            if pd.isna(col) or str(col).strip() == '' or 'Unnamed' in str(col):
                new_columns.append(f'未命名列_{len(new_columns)}')
            else:
                new_columns.append(str(col).strip())
        df_profit_breakdown.columns = new_columns

        # 尝试设置时间索引（假设第一列是时间）
        if len(df_profit_breakdown.columns) > 0:
            # 检查第一列是否可以转换为日期
            try:
                df_profit_breakdown.iloc[:, 0] = pd.to_datetime(df_profit_breakdown.iloc[:, 0], errors='coerce')
                df_profit_breakdown = df_profit_breakdown.set_index(df_profit_breakdown.columns[0])
                df_profit_breakdown = df_profit_breakdown.sort_index()  # 按时间排序
                df_profit_breakdown = df_profit_breakdown.dropna(how='all')  # 删除全为空的行
            except:
                # 如果第一列不是日期，保持原样
                pass

        return df_profit_breakdown

    except Exception as e:
        return None


def read_enterprise_profit_data(uploaded_file) -> Optional[pd.DataFrame]:
    """
    读取Excel文件中的"工业企业利润拆解"sheet数据

    Args:
        uploaded_file: 上传的Excel文件对象或文件路径

    Returns:
        DataFrame: 包含企业利润拆解数据，如果读取失败则返回None
    """
    try:
        # 处理不同类型的文件输入
        file_input = uploaded_file

        # 如果是自定义对象，尝试获取文件内容
        if hasattr(uploaded_file, 'getvalue'):
            try:
                from io import BytesIO
                file_input = BytesIO(uploaded_file.getvalue())
            except:
                pass
        elif hasattr(uploaded_file, 'path'):
            # 如果有path属性，使用文件路径
            file_input = uploaded_file.path

        # 读取"工业企业利润拆解"工作表
        df_profit = pd.read_excel(file_input, sheet_name='工业企业利润拆解', skiprows=1)

        # 检查数据是否为空
        if df_profit.empty:
            return None

        # 删除频率和单位行（前两行）
        if len(df_profit) >= 2:
            df_profit = df_profit.iloc[2:].reset_index(drop=True)

        # 清理列名中的空值和无效字符
        new_columns = []
        for col in df_profit.columns:
            if pd.isna(col) or str(col).strip() == '' or 'Unnamed' in str(col):
                new_columns.append(f'未命名列_{len(new_columns)}')
            else:
                new_columns.append(str(col).strip())
        df_profit.columns = new_columns

        # 尝试设置时间索引（假设第一列是时间）
        if len(df_profit.columns) > 0:
            # 检查第一列是否可以转换为日期
            try:
                df_profit.iloc[:, 0] = pd.to_datetime(df_profit.iloc[:, 0], errors='coerce')
                df_profit = df_profit.set_index(df_profit.columns[0])
                df_profit = df_profit.sort_index()  # 按时间排序
                df_profit = df_profit.dropna(how='all')  # 删除全为空的行
            except:
                # 如果第一列不是日期，保持原样
                pass

        return df_profit

    except Exception as e:
        return None


def calculate_grouped_profit_yoy(df_profit_breakdown: pd.DataFrame, df_weights: pd.DataFrame) -> pd.DataFrame:
    """
    根据权重数据中的分组映射，计算分组利润总额累计同比

    Args:
        df_profit_breakdown: 分上中下游利润拆解数据
        df_weights: 权重数据，包含出口依赖、上中下游映射

    Returns:
        DataFrame: 包含各分组利润总额累计同比的数据
    """
    try:
        if df_profit_breakdown is None or df_profit_breakdown.empty:
            return pd.DataFrame()

        if df_weights is None or df_weights.empty:
            return pd.DataFrame()

        # 获取工业企业累计利润总额（第2列，索引为1）
        if len(df_profit_breakdown.columns) < 2:
            return pd.DataFrame()

        total_profit_col = df_profit_breakdown.columns[1]  # 第2列

        # 注意：不再直接计算总利润的年同比，而是在最后通过分组加权计算
        # 这样确保数学关系的一致性：总同比 = 各分组的加权和

        # 初始化结果DataFrame
        result_df = pd.DataFrame(index=df_profit_breakdown.index)

        # 获取分行业利润数据（第3列到最后一列）
        industry_columns = df_profit_breakdown.columns[2:]  # 从第3列开始

        if len(industry_columns) == 0:
            return result_df

        # 创建行业名称到权重信息的映射
        weights_mapping = {}
        for _, row in df_weights.iterrows():
            indicator_name = row['指标名称']
            if pd.notna(indicator_name):
                weights_mapping[indicator_name] = {
                    '出口依赖': row['出口依赖'],
                    '上中下游': row['上中下游']
                }

        # 按出口依赖分组
        export_groups = {}
        # 按上中下游分组
        stream_groups = {}

        # 对每个行业列进行分组
        for col in industry_columns:
            # 尝试匹配权重数据中的指标名称
            matched_indicator = None

            # 首先尝试精确匹配
            if col in weights_mapping:
                matched_indicator = col
            else:
                # 使用正则表达式提取行业名称进行匹配
                import re

                # 从利润拆解列名中提取行业名称
                # 格式: 规模以上工业企业:利润总额:行业名称:累计值
                profit_match = re.search(r'规模以上工业企业:利润总额:([^:]+):累计值', col)
                if profit_match:
                    profit_industry = profit_match.group(1)

                    # 在权重数据中查找匹配的行业
                    for indicator in weights_mapping.keys():
                        # 从权重指标名称中提取行业名称
                        # 格式: 规模以上工业增加值:行业名称:当月同比
                        weight_match = re.search(r'规模以上工业增加值:([^:]+):当月同比', indicator)
                        if weight_match:
                            weight_industry = weight_match.group(1)
                            # 检查行业名称是否匹配
                            if profit_industry == weight_industry:
                                matched_indicator = indicator
                                break

                # 如果上述匹配失败，尝试直接部分匹配
                if not matched_indicator:
                    for indicator in weights_mapping.keys():
                        if col in indicator or indicator in col:
                            matched_indicator = indicator
                            break

            if matched_indicator and matched_indicator in weights_mapping:
                info = weights_mapping[matched_indicator]
                export_dep = info['出口依赖']
                stream_type = info['上中下游']

                # 分组到出口依赖
                if pd.notna(export_dep):
                    if export_dep not in export_groups:
                        export_groups[export_dep] = []
                    export_groups[export_dep].append(col)

                # 分组到上中下游
                if pd.notna(stream_type):
                    if stream_type not in stream_groups:
                        stream_groups[stream_type] = []
                    stream_groups[stream_type].append(col)
            else:
                pass  # 未找到匹配的权重数据

        # 第一步：计算所有行业的同比增速
        industry_yoy_data = {}
        for col in industry_columns:
            yoy_data = convert_cumulative_to_yoy(df_profit_breakdown[col])
            industry_yoy_data[col] = yoy_data

        # 第二步：计算动态权重（各行业利润占总利润的比例）
        total_profit_data = df_profit_breakdown[total_profit_col]
        industry_weights = {}
        for col in industry_columns:
            # 计算每个时期该行业利润占总利润的比例
            weights = df_profit_breakdown[col] / total_profit_data
            # 处理除零和无效值
            weights = weights.fillna(0).replace([float('inf'), -float('inf')], 0)
            industry_weights[col] = weights

        # 第三步：按照映射汇总各分组的利润累计值
        # 出口依赖分组利润汇总
        export_group_profits = {}
        for group_name, columns in export_groups.items():
            if columns:
                # 汇总该分组内所有行业的利润
                group_profit = pd.Series(index=df_profit_breakdown.index, dtype=float)
                group_profit[:] = 0.0

                for idx in df_profit_breakdown.index:
                    group_total = sum(df_profit_breakdown[col].loc[idx] for col in columns if col in df_profit_breakdown.columns)
                    group_profit.loc[idx] = group_total

                export_group_profits[group_name] = group_profit

        # 上中下游分组利润汇总
        stream_group_profits = {}
        for group_name, columns in stream_groups.items():
            if columns:
                # 汇总该分组内所有行业的利润
                group_profit = pd.Series(index=df_profit_breakdown.index, dtype=float)
                group_profit[:] = 0.0

                for idx in df_profit_breakdown.index:
                    group_total = sum(df_profit_breakdown[col].loc[idx] for col in columns if col in df_profit_breakdown.columns)
                    group_profit.loc[idx] = group_total

                stream_group_profits[group_name] = group_profit

        # 第四步：计算各分组的年同比
        # 出口依赖分组年同比
        for group_name, group_profit in export_group_profits.items():
            group_yoy = convert_cumulative_to_yoy(group_profit)
            result_df[f'出口依赖_{group_name}_利润累计同比'] = group_yoy

        # 上中下游分组年同比
        for group_name, group_profit in stream_group_profits.items():
            group_yoy = convert_cumulative_to_yoy(group_profit)
            result_df[f'上中下游_{group_name}_利润累计同比'] = group_yoy

        # 注意：上中下游分组的数据已经在第四步中计算完成，不需要重复计算

        # 第五步：计算总利润同比，使用上中下游分组的动态权重加权和
        # 总同比 = 各分组同比的动态权重加权和（权重为各分组利润占总利润的比例）
        total_profit_yoy = pd.Series(index=df_profit_breakdown.index, dtype=float)
        total_profit_yoy[:] = 0.0

        for idx in df_profit_breakdown.index:
            # 检查是否为1月或2月，如果是则设为NaN
            if hasattr(idx, 'month') and idx.month in [1, 2]:
                total_profit_yoy.loc[idx] = float('nan')
                continue

            # 计算总利润（所有上中下游分组利润之和）
            total_profit_at_idx = sum(stream_group_profits[group_name].loc[idx] for group_name in stream_group_profits.keys())

            if total_profit_at_idx > 0:
                # 使用动态权重计算总同比
                total_weighted_sum = 0.0

                for group_name in stream_group_profits.keys():
                    group_col = f'上中下游_{group_name}_利润累计同比'
                    if group_col in result_df.columns:
                        group_profit = stream_group_profits[group_name].loc[idx]
                        group_yoy = result_df[group_col].loc[idx]

                        if pd.notna(group_yoy) and group_profit > 0:
                            # 动态权重 = 该分组利润 / 总利润
                            weight = group_profit / total_profit_at_idx
                            total_weighted_sum += weight * group_yoy

                total_profit_yoy.loc[idx] = total_weighted_sum
            else:
                total_profit_yoy.loc[idx] = float('nan')

        # 将总同比添加到结果的第一列
        result_df.insert(0, '工业企业累计利润总额累计同比', total_profit_yoy)

        return result_df

    except Exception:
        return pd.DataFrame()


def convert_cumulative_to_yoy(data_series: pd.Series, periods: int = 12) -> pd.Series:
    """
    将累计值转换为年同比数据，并将1、2月设为缺失值
    修复版本：使用DateOffset而不是shift来确保正确的12个月前匹配

    Args:
        data_series: 累计值数据序列
        periods: 年同比的期数，默认12个月

    Returns:
        Series: 年同比数据
    """
    try:
        # 确保数据是数值型
        data_series = pd.to_numeric(data_series, errors='coerce')

        # 创建结果序列
        yoy_series = pd.Series(index=data_series.index, dtype=float)

        for current_date in data_series.index:
            try:
                # 计算12个月前的日期
                prev_year_date = current_date - pd.DateOffset(months=periods)

                # 查找精确匹配的日期
                if prev_year_date in data_series.index:
                    prev_value = data_series.loc[prev_year_date]
                    current_value = data_series.loc[current_date]

                    if pd.notna(prev_value) and pd.notna(current_value) and prev_value != 0:
                        yoy = ((current_value - prev_value) / prev_value) * 100
                        yoy_series.loc[current_date] = yoy
                    else:
                        yoy_series.loc[current_date] = float('nan')
                else:
                    # 如果精确日期不存在，查找最接近的日期（容差30天）
                    tolerance = pd.Timedelta(days=30)
                    close_dates = data_series.index[abs(data_series.index - prev_year_date) <= tolerance]

                    if len(close_dates) > 0:
                        closest_date = close_dates[abs(close_dates - prev_year_date).argmin()]
                        prev_value = data_series.loc[closest_date]
                        current_value = data_series.loc[current_date]

                        if pd.notna(prev_value) and pd.notna(current_value) and prev_value != 0:
                            yoy = ((current_value - prev_value) / prev_value) * 100
                            yoy_series.loc[current_date] = yoy
                        else:
                            yoy_series.loc[current_date] = float('nan')
                    else:
                        yoy_series.loc[current_date] = float('nan')

            except Exception as e:
                yoy_series.loc[current_date] = float('nan')

        # 将1月和2月的值设为NaN（因为累计值在这两个月不具有可比性）
        jan_feb_mask = (yoy_series.index.month == 1) | (yoy_series.index.month == 2)
        yoy_series.loc[jan_feb_mask] = float('nan')

        return yoy_series

    except Exception:
        return pd.Series()


def filter_data_by_time_range(df: pd.DataFrame, time_range: str, custom_start_date: Optional[str] = None, custom_end_date: Optional[str] = None) -> pd.DataFrame:
    """
    Filter dataframe by time range (copied from macro operations for consistency)
    """
    if df.empty or time_range == "全部":
        return df.copy()

    filtered_df = df.copy()

    # Get the latest date in the dataset
    latest_date = df.index.max()

    if time_range == "自定义" and custom_start_date and custom_end_date:
        # Handle custom date range
        try:
            start_date = pd.to_datetime(custom_start_date + "-01")
            end_date = pd.to_datetime(custom_end_date + "-01") + pd.offsets.MonthEnd(0)
        except:
            start_date = df.index.min()
            end_date = df.index.max()
    else:
        # Calculate start date based on time range selection - from latest date backwards
        if time_range == "1年":
            start_date = latest_date - pd.DateOffset(years=1)
        elif time_range == "3年":
            start_date = latest_date - pd.DateOffset(years=3)
        elif time_range == "5年":
            start_date = latest_date - pd.DateOffset(years=5)
        elif time_range == "自定义":
            # Fallback for custom range when dates are not provided - use full range
            start_date = df.index.min()
            end_date = df.index.max()
        else:
            # Default fallback
            start_date = df.index.min()
            end_date = df.index.max()

        # Set end_date for non-custom ranges
        if time_range != "自定义":
            end_date = latest_date

    # Filter data using date range
    try:
        # Ensure the index is datetime
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)

        # Filter using boolean indexing - include data within date range
        if time_range == "自定义":
            mask = (df.index >= start_date) & (df.index <= end_date)
        else:
            mask = df.index >= start_date
        filtered_df = df.loc[mask]

    except Exception as e:
        # Fallback: if date filtering fails, use tail method
        if time_range == "1年":
            filtered_df = df.tail(12)
        elif time_range == "3年":
            filtered_df = df.tail(36)
        elif time_range == "5年":
            filtered_df = df.tail(60)
        else:
            filtered_df = df.copy()

    return filtered_df


def create_enterprise_indicators_chart(df_data: pd.DataFrame, time_range: str = "3年",
                                     custom_start_date: Optional[str] = None,
                                     custom_end_date: Optional[str] = None) -> Optional[go.Figure]:
    """
    创建企业经营四个指标的时间序列线图

    Args:
        df_data: 包含指标数据的DataFrame
        time_range: 时间范围选择
        custom_start_date: 自定义开始日期
        custom_end_date: 自定义结束日期

    Returns:
        plotly Figure对象
    """
    try:
        # Apply time filtering first
        filtered_df = filter_data_by_time_range(df_data, time_range, custom_start_date, custom_end_date)

        # 定义需要的四个指标
        required_indicators = [
            '规模以上工业企业:利润总额:累计同比',
            '规模以上工业增加值:累计同比',
            'PPI:累计同比',
            '规模以上工业企业:营业收入利润率:累计值年同比'
        ]

        # 检查哪些指标存在于数据中
        available_indicators = []

        # 首先尝试精确匹配
        for indicator in required_indicators:
            exact_match = [col for col in df_data.columns if col == indicator]
            if exact_match:
                available_indicators.append(exact_match[0])
                continue

            # 尝试关键词匹配
            keywords_to_match = []
            if '工业增加值' in indicator and '累计同比' in indicator:
                keywords_to_match = ['工业增加值', '累计同比']
            elif '利润总额' in indicator and '累计同比' in indicator:
                keywords_to_match = ['利润总额', '累计同比']
            elif '营业收入利润率' in indicator and '累计值年同比' in indicator:
                keywords_to_match = ['营业收入利润率', '累计值年同比']
            elif 'PPI' in indicator and '累计同比' in indicator:
                keywords_to_match = ['PPI', '累计同比']

            if keywords_to_match:
                partial_matches = [col for col in df_data.columns
                                 if all(keyword in str(col) for keyword in keywords_to_match)]
                if partial_matches:
                    available_indicators.append(partial_matches[0])
                    continue

            # 最后尝试单个关键词匹配
            single_keyword_matches = [col for col in df_data.columns
                                    if any(keyword in str(col) for keyword in indicator.split(':'))]
            if single_keyword_matches:
                available_indicators.append(single_keyword_matches[0])

        if not available_indicators:
            return None

        # 处理数据 - 改为更灵活的缺失数据策略
        complete_data_df = filtered_df[available_indicators].copy()

        # 转换所有列为数值型
        for col in complete_data_df.columns:
            complete_data_df[col] = pd.to_numeric(complete_data_df[col], errors='coerce')

        # 新策略：只要有至少3个指标有数据就显示（而不是要求全部4个）
        # 计算每行的非空值数量
        non_null_counts = complete_data_df.count(axis=1)

        # 保留至少有3个指标数据的行
        min_indicators = 3
        valid_rows = non_null_counts >= min_indicators
        complete_data_df = complete_data_df[valid_rows]

        if complete_data_df.empty:
            return None

        # 创建图表
        fig = go.Figure()

        # 定义颜色
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        def get_legend_name(indicator):
            """将完整的指标名称转换为简化的图例名称"""
            legend_mapping = {
                '规模以上工业企业:利润总额:累计同比': '利润总额累计同比',
                'PPI:累计同比': 'PPI累计同比',
                '规模以上工业增加值:累计同比': '工业增加值累计同比',
                '规模以上工业企业:营业收入利润率:累计值年同比': '营业收入利润率累计同比'
            }
            return legend_mapping.get(indicator, indicator)

        # 定义哪些指标用条形图（堆积），哪些用线图
        bar_indicators = [
            '规模以上工业增加值:累计同比',
            'PPI:累计同比',
            '规模以上工业企业:营业收入利润率:累计值年同比'
        ]
        line_indicators = [
            '规模以上工业企业:利润总额:累计同比'
        ]

        # 先添加线图指标
        for i, indicator in enumerate(available_indicators):
            if indicator in complete_data_df.columns:
                # 获取数据，保留NaN值用于正确处理缺失点
                y_data = complete_data_df[indicator].dropna()

                if not y_data.empty:
                    # 检查是否应该用线图
                    is_line_indicator = any(line_key in indicator for line_key in line_indicators)

                    if is_line_indicator:
                        # 添加线图
                        fig.add_trace(go.Scatter(
                            x=y_data.index,
                            y=y_data,
                            mode='lines+markers',
                            name=get_legend_name(indicator),
                            line=dict(width=2.5, color=colors[i % len(colors)]),
                            marker=dict(size=4),
                            connectgaps=False,  # 不连接缺失点
                            hovertemplate=f'<b>{get_legend_name(indicator)}</b><br>' +
                                          '时间: %{x}<br>' +
                                          '数值: %{y:.2f}%<extra></extra>'
                        ))

        # 添加条形图指标（恢复简单逻辑，让Plotly正确处理正负值堆叠）
        for i, indicator in enumerate(available_indicators):
            if indicator in complete_data_df.columns:
                # 获取数据，保留NaN值用于正确处理缺失点
                y_data = complete_data_df[indicator].dropna()

                if not y_data.empty:
                    # 检查是否应该用条形图
                    is_bar_indicator = any(bar_key in indicator for bar_key in bar_indicators)

                    if is_bar_indicator:
                        # 计算渐变透明度：按原始指标顺序
                        opacity_value = 0.9 - (i * 0.15)  # 0.9, 0.75, 0.6, 0.45...
                        opacity_value = max(0.5, opacity_value)  # 最小透明度为0.5

                        # 添加堆积条形图
                        fig.add_trace(go.Bar(
                            x=y_data.index,
                            y=y_data,
                            name=get_legend_name(indicator),
                            marker_color=colors[i % len(colors)],
                            opacity=opacity_value,
                            hovertemplate=f'<b>{get_legend_name(indicator)}</b><br>' +
                                          '时间: %{x}<br>' +
                                          '数值: %{y:.2f}%<extra></extra>'
                        ))

        # Calculate actual data range for x-axis using complete data
        if not complete_data_df.empty:
            min_date = complete_data_df.index.min()
            max_date = complete_data_df.index.max()
        else:
            min_date = max_date = None

        # Configure x-axis with 3-month intervals
        xaxis_config = dict(
            title="",  # No x-axis title
            type="date",
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick="M3"  # 3-month intervals
        )

        # Set the range to actual data if available
        if min_date and max_date:
            xaxis_config['range'] = [min_date, max_date]

        # 更新布局 - 移除所有文字，设置堆积条形图
        fig.update_layout(
            title="",  # No chart title
            xaxis=xaxis_config,
            yaxis=dict(
                title="",  # No y-axis title
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray'
            ),
            barmode='relative',  # 设置为相对堆积模式，正确处理正负值堆叠
            hovermode='x unified',
            height=500,
            margin=dict(l=50, r=50, t=30, b=80),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="top",
                y=-0.1,
                xanchor="center",
                x=0.5
            ),
            plot_bgcolor='white',
            paper_bgcolor='white'
        )

        return fig

    except Exception:
        return None


def create_upstream_downstream_profit_chart(df_grouped_profit: pd.DataFrame, time_range: str = "3年",
                                          custom_start_date: Optional[str] = None,
                                          custom_end_date: Optional[str] = None) -> Optional[go.Figure]:
    """
    创建上中下游分组利润总额累计同比图表

    Args:
        df_grouped_profit: 包含分组利润累计同比数据的DataFrame
        time_range: 时间范围选择
        custom_start_date: 自定义开始日期
        custom_end_date: 自定义结束日期

    Returns:
        plotly Figure对象
    """
    try:
        if df_grouped_profit is None or df_grouped_profit.empty:
            return None

        # Apply time filtering first
        filtered_df = filter_data_by_time_range(df_grouped_profit, time_range, custom_start_date, custom_end_date)

        if filtered_df.empty:
            return None

        # 创建图表
        fig = go.Figure()

        # 定义颜色（与第一个图表保持一致）
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        # 首先添加工业企业累计利润总额累计同比（线图）
        if '工业企业累计利润总额累计同比' in filtered_df.columns:
            total_profit_data = filtered_df['工业企业累计利润总额累计同比'].dropna()
            if not total_profit_data.empty:
                fig.add_trace(go.Scatter(
                    x=total_profit_data.index,
                    y=total_profit_data,
                    mode='lines+markers',
                    name='工业企业累计利润总额累计同比',
                    line=dict(width=2.5, color=colors[0]),
                    marker=dict(size=4),
                    connectgaps=False,
                    hovertemplate='<b>工业企业累计利润总额累计同比</b><br>' +
                                  '时间: %{x}<br>' +
                                  '数值: %{y:.2f}%<extra></extra>'
                ))

        # 添加上中下游分组数据（条形图）
        # 收集所有上中下游分组列
        stream_columns = []
        for col in filtered_df.columns:
            if '上中下游_' in col and '利润累计同比' in col:
                stream_columns.append(col)

        # 按名称排序以保持一致性
        stream_columns.sort()

        color_index = 1  # 从第二个颜色开始

        for col in stream_columns:
            group_data = filtered_df[col].dropna()
            if not group_data.empty:
                # 清理列名用于显示 - 使用改进的逻辑
                if col.startswith('上中下游_') and col.endswith('_利润累计同比'):
                    # 提取中间部分：上中下游_[中间部分]_利润累计同比
                    display_name = col[len('上中下游_'):-len('_利润累计同比')]
                else:
                    # 回退到原始逻辑
                    display_name = col.replace('上中下游_', '').replace('_利润累计同比', '')

                # 计算透明度（与第一个图表保持一致）
                opacity_value = 0.9 - ((color_index - 1) * 0.15)
                opacity_value = max(0.5, opacity_value)

                fig.add_trace(go.Bar(
                    x=group_data.index,
                    y=group_data,
                    name=display_name,
                    marker_color=colors[color_index % len(colors)],
                    opacity=opacity_value,
                    hovertemplate=f'<b>{display_name}</b><br>' +
                                  '时间: %{x}<br>' +
                                  '数值: %{y:.2f}%<extra></extra>'
                ))
                color_index += 1

        # 设置x轴配置（与第一个图表保持一致）
        min_date = filtered_df.index.min() if not filtered_df.empty else None
        max_date = filtered_df.index.max() if not filtered_df.empty else None

        xaxis_config = dict(
            title="",
            type="date",
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick="M3"  # 3-month intervals
        )

        if min_date and max_date:
            xaxis_config['range'] = [min_date, max_date]

        # 更新布局（与第一个图表保持一致）
        fig.update_layout(
            title="",
            xaxis=xaxis_config,
            yaxis=dict(
                title="",
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray'
            ),
            barmode='relative',  # 设置为相对堆积模式，正确处理正负值堆叠
            hovermode='x unified',
            height=500,
            margin=dict(l=50, r=50, t=30, b=80),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="top",
                y=-0.1,
                xanchor="center",
                x=0.5
            ),
            plot_bgcolor='white',
            paper_bgcolor='white'
        )

        return fig

    except Exception:
        return None


def create_export_dependency_profit_chart(df_grouped_profit: pd.DataFrame, time_range: str = "3年",
                                         custom_start_date: Optional[str] = None,
                                         custom_end_date: Optional[str] = None) -> Optional[go.Figure]:
    """
    创建出口依赖分组利润总额累计同比图表

    Args:
        df_grouped_profit: 包含分组利润累计同比数据的DataFrame
        time_range: 时间范围选择
        custom_start_date: 自定义开始日期
        custom_end_date: 自定义结束日期

    Returns:
        plotly Figure对象
    """
    try:
        if df_grouped_profit is None or df_grouped_profit.empty:
            return None

        # Apply time filtering first
        filtered_df = filter_data_by_time_range(df_grouped_profit, time_range, custom_start_date, custom_end_date)

        if filtered_df.empty:
            return None

        # 创建图表
        fig = go.Figure()

        # 定义颜色（与第一个图表保持一致）
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        # 首先添加工业企业累计利润总额累计同比（线图）
        if '工业企业累计利润总额累计同比' in filtered_df.columns:
            total_profit_data = filtered_df['工业企业累计利润总额累计同比'].dropna()
            if not total_profit_data.empty:
                fig.add_trace(go.Scatter(
                    x=total_profit_data.index,
                    y=total_profit_data,
                    mode='lines+markers',
                    name='工业企业累计利润总额累计同比',
                    line=dict(width=2.5, color=colors[0]),
                    marker=dict(size=4),
                    connectgaps=False,
                    hovertemplate='<b>工业企业累计利润总额累计同比</b><br>' +
                                  '时间: %{x}<br>' +
                                  '数值: %{y:.2f}%<extra></extra>'
                ))

        # 添加出口依赖分组数据（条形图）
        # 收集所有出口依赖分组列
        export_columns = []
        for col in filtered_df.columns:
            if '出口依赖_' in col and '利润累计同比' in col:
                export_columns.append(col)

        # 按名称排序以保持一致性
        export_columns.sort()

        color_index = 1  # 从第二个颜色开始

        for col in export_columns:
            group_data = filtered_df[col].dropna()
            if not group_data.empty:
                # 清理列名用于显示 - 使用改进的逻辑
                if col.startswith('出口依赖_') and col.endswith('_利润累计同比'):
                    # 提取中间部分：出口依赖_[中间部分]_利润累计同比
                    display_name = col[len('出口依赖_'):-len('_利润累计同比')]
                else:
                    # 回退到原始逻辑
                    display_name = col.replace('出口依赖_', '').replace('_利润累计同比', '')

                # 计算透明度（与第一个图表保持一致）
                opacity_value = 0.9 - ((color_index - 1) * 0.15)
                opacity_value = max(0.5, opacity_value)

                fig.add_trace(go.Bar(
                    x=group_data.index,
                    y=group_data,
                    name=display_name,
                    marker_color=colors[color_index % len(colors)],
                    opacity=opacity_value,
                    hovertemplate=f'<b>{display_name}</b><br>' +
                                  '时间: %{x}<br>' +
                                  '数值: %{y:.2f}%<extra></extra>'
                ))
                color_index += 1

        # 设置x轴配置（与第一个图表保持一致）
        min_date = filtered_df.index.min() if not filtered_df.empty else None
        max_date = filtered_df.index.max() if not filtered_df.empty else None

        xaxis_config = dict(
            title="",
            type="date",
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick="M3"  # 3-month intervals
        )

        if min_date and max_date:
            xaxis_config['range'] = [min_date, max_date]

        # 更新布局（与第一个图表保持一致）
        fig.update_layout(
            title="",
            xaxis=xaxis_config,
            yaxis=dict(
                title="",
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray'
            ),
            barmode='relative',  # 设置为相对堆积模式，正确处理正负值堆叠
            hovermode='x unified',
            height=500,
            margin=dict(l=50, r=50, t=30, b=80),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="top",
                y=-0.1,
                xanchor="center",
                x=0.5
            ),
            plot_bgcolor='white',
            paper_bgcolor='white'
        )

        return fig

    except Exception:
        return None


def render_enterprise_operations_analysis_with_data(st_obj, df_macro: Optional[pd.DataFrame], df_weights: Optional[pd.DataFrame], uploaded_file=None):
    """
    使用预加载数据渲染企业经营分析（用于统一模块）

    Args:
        st_obj: Streamlit对象
        df_macro: 宏观运行数据
        df_weights: 权重数据
        uploaded_file: 上传的Excel文件对象
    """
    # 如果没有传入上传的文件，尝试从统一状态管理器获取
    if uploaded_file is None:
        try:
            from dashboard.state_management import get_unified_manager

            state_manager = get_unified_manager()
            if state_manager:
                uploaded_file = state_manager.get_state('analysis.industrial.unified_file_uploader')
            else:
                # 降级到session_state
                if 'industrial_unified_file_uploader' in st.session_state:
                    uploaded_file = st.session_state['industrial_unified_file_uploader']
        except Exception as e:
            logger.error(f"获取上传文件失败: {e}")
            # 降级到session_state
            if 'industrial_unified_file_uploader' in st.session_state:
                uploaded_file = st.session_state['industrial_unified_file_uploader']

    # Remove title text as requested

    if uploaded_file is None:
        # Remove info text as requested
        return

    # 读取企业利润拆解数据
    df_profit = read_enterprise_profit_data(uploaded_file)

    if df_profit is None:
        return

    # Remove success message and data overview as requested

    # 处理"规模以上工业企业:营业收入利润率:累计值"转换为年同比
    profit_margin_col = None
    for col in df_profit.columns:
        if '营业收入利润率' in str(col) and '累计值' in str(col):
            profit_margin_col = col
            break

    if profit_margin_col:
        # Remove processing info text as requested

        # 转换为年同比
        yoy_data = convert_cumulative_to_yoy(df_profit[profit_margin_col])

        # 创建新的列名
        yoy_col_name = profit_margin_col.replace('累计值', '累计值年同比')
        df_profit[yoy_col_name] = yoy_data

    # Remove section title as requested

    # 使用fragment封装企业经营第一个图表
    @st_obj.fragment
    def render_enterprise_chart1_with_controls():
        # 添加第1个图的标题
        st_obj.markdown("#### 利润总额拆解")

        # Add time range selector with stable state management
        # 导入统一状态管理函数
        from ..industrial_analysis import get_industrial_state, set_industrial_state, initialize_industrial_states

        # 预初始化状态以避免第一次点击时刷新
        initialize_industrial_states()

        current_time_range = get_industrial_state('enterprise_time_range_chart1', "3年")
        time_range_options = ["1年", "3年", "5年", "全部", "自定义"]
        default_index = time_range_options.index(current_time_range) if current_time_range in time_range_options else 1

        time_range = st_obj.radio(
            "时间范围",
            time_range_options,
            index=default_index,
            horizontal=True,
            key="industrial_enterprise_operations_time_range_selector_fragment",
            label_visibility="collapsed"
        )

        # 只在值真正改变时更新状态
        if time_range != current_time_range:
            set_industrial_state('enterprise_time_range_chart1', time_range, is_initialization=False)

        # Custom date range inputs when "自定义" is selected
        custom_start = None
        custom_end = None
        if time_range == "自定义":
            col_start, col_end = st_obj.columns([1, 1])
            with col_start:
                custom_start = st_obj.text_input("开始年月", placeholder="2020-01", key="industrial_enterprise_operations_custom_start_date_fragment")
            with col_end:
                custom_end = st_obj.text_input("结束年月", placeholder="2024-12", key="industrial_enterprise_operations_custom_end_date_fragment")

        # 创建第一个图表（基于已经计算好年同比的完整数据，然后内部进行时间筛选）
        fig1 = create_enterprise_indicators_chart(df_profit, time_range, custom_start, custom_end)

        if fig1 is not None:
            st_obj.plotly_chart(fig1, use_container_width=True, key="enterprise_operations_chart_1_fragment")

        return time_range, custom_start, custom_end

    # 调用fragment
    time_range, custom_start, custom_end = render_enterprise_chart1_with_controls()

    # 添加第一个图表的数据下载功能
    download_df1 = filter_data_by_time_range(df_profit, time_range, custom_start, custom_end)

    if not download_df1.empty:
            # 创建Excel文件
            import io
            excel_buffer1 = io.BytesIO()
            with pd.ExcelWriter(excel_buffer1, engine='openpyxl') as writer:
                download_df1.to_excel(writer, sheet_name='企业经营指标', index=True)
            excel_data1 = excel_buffer1.getvalue()

            col1, _ = st_obj.columns([1, 3])
            with col1:
                st_obj.download_button(
                    label="下载数据",
                    data=excel_data1,
                    file_name=f"企业经营指标_{time_range}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    key="industrial_enterprise_operations_download_data_button"
                )

    # 添加第二个和第三个图表：分组利润总额累计同比
    if df_weights is not None:
        # 读取分上中下游利润拆解数据
        df_profit_breakdown = read_profit_breakdown_data(uploaded_file)

        if df_profit_breakdown is not None:
            # 计算分组利润总额累计同比
            df_grouped_profit = calculate_grouped_profit_yoy(df_profit_breakdown, df_weights)

            if not df_grouped_profit.empty:
                # 添加一些间距
                st_obj.markdown("---")

                # 使用fragment封装第二个图表
                @st_obj.fragment
                def render_enterprise_chart2_with_controls():
                    # 第二个图表：上中下游分组 - 添加独立的时间筛选器
                    st_obj.markdown("#### 上中下游分组利润总额累计同比")

                    # 导入统一状态管理函数
                    from ..industrial_analysis import get_industrial_state, set_industrial_state

                    current_time_range_2 = get_industrial_state('enterprise_time_range_chart2', "3年")
                    time_range_options = ["1年", "3年", "5年", "全部", "自定义"]
                    default_index_2 = time_range_options.index(current_time_range_2) if current_time_range_2 in time_range_options else 1

                    time_range_2 = st_obj.radio(
                        "时间范围",
                        time_range_options,
                        index=default_index_2,
                        horizontal=True,
                        key="industrial_enterprise_operations_time_range_selector_chart2_fragment",
                        label_visibility="collapsed"
                    )

                    # 只在值真正改变时更新状态
                    if time_range_2 != current_time_range_2:
                        set_industrial_state('enterprise_time_range_chart2', time_range_2)

                    # Custom date range inputs when "自定义" is selected for chart 2
                    custom_start_2 = None
                    custom_end_2 = None
                    if time_range_2 == "自定义":
                        col_start, col_end = st_obj.columns([1, 1])
                        with col_start:
                            custom_start_2 = st_obj.text_input("开始年月", placeholder="2020-01", key="industrial_enterprise_operations_custom_start_date_chart2_fragment")
                        with col_end:
                            custom_end_2 = st_obj.text_input("结束年月", placeholder="2024-12", key="industrial_enterprise_operations_custom_end_date_chart2_fragment")

                    # 创建第二个图表：上中下游分组
                    fig2 = create_upstream_downstream_profit_chart(df_grouped_profit, time_range_2, custom_start_2, custom_end_2)

                    if fig2 is not None:
                        st_obj.plotly_chart(fig2, use_container_width=True, key="enterprise_operations_chart_2_fragment")

                    return time_range_2, custom_start_2, custom_end_2

                # 调用fragment
                time_range_2, custom_start_2, custom_end_2 = render_enterprise_chart2_with_controls()

                # 添加第二个图表的独立数据下载功能
                download_df2 = filter_data_by_time_range(df_grouped_profit, time_range_2, custom_start_2, custom_end_2)

                if not download_df2.empty:
                        # 筛选上中下游相关列
                        upstream_downstream_cols = ['工业企业累计利润总额累计同比'] + [col for col in download_df2.columns if '上中下游_' in col and '利润累计同比' in col]
                        download_df2_filtered = download_df2[upstream_downstream_cols]

                        # 创建Excel文件
                        excel_buffer2 = io.BytesIO()
                        with pd.ExcelWriter(excel_buffer2, engine='openpyxl') as writer:
                            download_df2_filtered.to_excel(writer, sheet_name='上中下游分组利润累计同比', index=True)
                        excel_data2 = excel_buffer2.getvalue()

                        col1, _ = st_obj.columns([1, 3])
                        with col1:
                            st_obj.download_button(
                                label="下载数据",
                                data=excel_data2,
                                file_name=f"上中下游分组利润累计同比_{time_range_2}.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                key="industrial_enterprise_operations_download_upstream_downstream_button"
                            )

                # 添加一些间距
                st_obj.markdown("---")

                # 使用fragment封装第三个图表
                @st_obj.fragment
                def render_enterprise_chart3_with_controls():
                    # 第三个图表：出口依赖分组 - 添加独立的时间筛选器
                    st_obj.markdown("#### 出口依赖分组利润总额累计同比")

                    # 导入统一状态管理函数
                    from ..industrial_analysis import get_industrial_state, set_industrial_state

                    current_time_range_3 = get_industrial_state('enterprise_time_range_chart3', "3年")
                    time_range_options = ["1年", "3年", "5年", "全部", "自定义"]
                    default_index_3 = time_range_options.index(current_time_range_3) if current_time_range_3 in time_range_options else 1

                    time_range_3 = st_obj.radio(
                        "时间范围",
                        time_range_options,
                        index=default_index_3,
                        horizontal=True,
                        key="industrial_enterprise_operations_time_range_selector_chart3_fragment",
                        label_visibility="collapsed"
                    )

                    # 只在值真正改变时更新状态
                    if time_range_3 != current_time_range_3:
                        set_industrial_state('enterprise_time_range_chart3', time_range_3)

                    # Custom date range inputs when "自定义" is selected for chart 3
                    custom_start_3 = None
                    custom_end_3 = None
                    if time_range_3 == "自定义":
                        col_start, col_end = st_obj.columns([1, 1])
                        with col_start:
                            custom_start_3 = st_obj.text_input("开始年月", placeholder="2020-01", key="industrial_enterprise_operations_custom_start_date_chart3_fragment")
                        with col_end:
                            custom_end_3 = st_obj.text_input("结束年月", placeholder="2024-12", key="industrial_enterprise_operations_custom_end_date_chart3_fragment")

                    # 创建第三个图表：出口依赖分组
                    fig3 = create_export_dependency_profit_chart(df_grouped_profit, time_range_3, custom_start_3, custom_end_3)

                    if fig3 is not None:
                        st_obj.plotly_chart(fig3, use_container_width=True, key="enterprise_operations_chart_3_fragment")

                    return time_range_3, custom_start_3, custom_end_3

                # 调用fragment
                time_range_3, custom_start_3, custom_end_3 = render_enterprise_chart3_with_controls()

                # 添加第三个图表的独立数据下载功能
                download_df3 = filter_data_by_time_range(df_grouped_profit, time_range_3, custom_start_3, custom_end_3)

                if not download_df3.empty:
                        # 筛选出口依赖相关列
                        export_dependency_cols = ['工业企业累计利润总额累计同比'] + [col for col in download_df3.columns if '出口依赖_' in col and '利润累计同比' in col]
                        download_df3_filtered = download_df3[export_dependency_cols]

                        # 创建Excel文件
                        excel_buffer3 = io.BytesIO()
                        with pd.ExcelWriter(excel_buffer3, engine='openpyxl') as writer:
                            download_df3_filtered.to_excel(writer, sheet_name='出口依赖分组利润累计同比', index=True)
                        excel_data3 = excel_buffer3.getvalue()

                        col1, _ = st_obj.columns([1, 3])
                        with col1:
                            st_obj.download_button(
                                label="下载数据",
                                data=excel_data3,
                                file_name=f"出口依赖分组利润累计同比_{time_range_3}.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                key="industrial_enterprise_operations_download_export_dependency_button"
                            )


def render_enterprise_operations_tab(st_obj):
    """
    原始的企业经营分析标签页（保持向后兼容）

    Args:
        st_obj: Streamlit 对象
    """
    # 直接调用新的分析函数
    render_enterprise_operations_analysis_with_data(st_obj, None, None)
