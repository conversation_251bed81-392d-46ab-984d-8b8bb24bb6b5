# -*- coding: utf-8 -*-
"""
导航管理工具
提供导航变化检测、状态同步等功能
"""

import streamlit as st
import time
import logging
from typing import Dict, Any, Optional
from .debug_helpers import debug_navigation

# 设置日志
logger = logging.getLogger(__name__)


def get_navigation_refactor():
    """获取导航重构器实例"""
    try:
        from ...state_management import get_unified_manager
        from ...state_management.refactor.navigation_module_refactor import NavigationModuleRefactor

        unified_manager = get_unified_manager()
        if unified_manager is None:
            logger.warning("统一状态管理器不可用")
            return None

        return NavigationModuleRefactor(unified_manager)
    except Exception as e:
        # 静默处理导航重构器获取失败，避免重复错误日志
        return None


def check_navigation_change(
    current_module: str, 
    selected_module: str, 
    nav_manager: Any,
    duplicate_threshold: float = 0.1
) -> Dict[str, Any]:
    """
    检查导航是否真的发生了变化，避免不必要的状态更新
    
    Args:
        current_module: 当前模块
        selected_module: 选择的模块
        nav_manager: 导航管理器实例
        duplicate_threshold: 重复请求检测阈值（秒）
        
    Returns:
        Dict[str, Any]: 导航变化结果
    """
    # 如果模块值相同，没有变化
    if current_module == selected_module:
        return {
            'has_change': False,
            'from': current_module,
            'to': selected_module,
            'success': True
        }
    
    # 检查是否是重复请求
    if is_duplicate_navigation_request(current_module, selected_module, duplicate_threshold):
        debug_navigation(
            "重复请求检测", 
            f"跳过重复导航请求: {current_module} -> {selected_module}"
        )
        return {
            'has_change': False,
            'from': current_module,
            'to': selected_module,
            'success': True
        }
    
    # 执行导航状态更新
    success = True
    if nav_manager:
        try:
            success = nav_manager.set_current_main_module(selected_module)
            if success:
                # 记录导航状态和时间到统一状态管理器
                nav_refactor = get_navigation_refactor()
                if nav_refactor:
                    current_time = time.time()
                    nav_refactor.set_navigation_state('navigation', 'last_navigation_state', f"{current_module}->{selected_module}")
                    nav_refactor.set_navigation_state('navigation', 'last_navigation_time', current_time)
                
                debug_navigation(
                    "状态更新成功", 
                    f"主模块导航状态更新成功: {current_module} -> {selected_module}"
                )
                
                # 清除TabStateDetector缓存，确保状态检测的准确性
                clear_tab_detector_cache()
                
                # 验证状态是否正确设置
                actual_main = nav_manager.get_current_main_module()
                actual_sub = nav_manager.get_current_sub_module()
                debug_navigation(
                    "状态验证", 
                    f"验证状态 - 主模块: {actual_main}, 子模块: {actual_sub}"
                )
        except Exception as e:
            debug_navigation(
                "状态更新失败", 
                f"导航状态更新失败: {e}", 
                include_stack=True
            )
            success = False
    
    return {
        'has_change': True,
        'from': current_module,
        'to': selected_module,
        'success': success
    }


def is_duplicate_navigation_request(
    current_module: str, 
    selected_module: str, 
    threshold: float = 0.1
) -> bool:
    """
    检查是否是重复的导航请求
    
    Args:
        current_module: 当前模块
        selected_module: 选择的模块
        threshold: 时间阈值（秒）
        
    Returns:
        bool: 是否是重复请求
    """
    current_time = time.time()

    # 从统一状态管理器检查上次导航状态
    nav_refactor = get_navigation_refactor()
    if nav_refactor:
        last_nav = nav_refactor.get_navigation_state('navigation', 'last_navigation_state')
        last_time = nav_refactor.get_navigation_state('navigation', 'last_navigation_time')

        # 如果在很短时间内有相同的导航请求，跳过
        if (last_nav and last_time and
            last_nav == f"{current_module}->{selected_module}" and
            current_time - last_time < threshold):
            return True
    
    return False


def sync_navigation_state(state_manager: Any, nav_manager: Any) -> bool:
    """
    统一的状态同步机制 - 消除状态不一致

    Args:
        state_manager: 状态管理器
        nav_manager: 导航管理器

    Returns:
        bool: 同步是否成功
    """
    try:
        # 获取当前导航状态
        current_main = nav_manager.get_current_main_module()
        current_sub = nav_manager.get_current_sub_module()

        # 同步到统一状态管理器
        nav_refactor = get_navigation_refactor()
        if nav_refactor:
            nav_refactor.set_navigation_state('navigation', 'main_module', current_main)
            nav_refactor.set_navigation_state('navigation', 'sub_module', current_sub)

            logger.info(f"导航状态同步完成 - 主模块: {current_main}, 子模块: {current_sub}")

        debug_navigation(
            "状态同步完成",
            f"主模块: {current_main}, 子模块: {current_sub}"
        )
        return True
    except Exception as e:
        logger.error(f"导航状态同步失败: {e}")
        debug_navigation("状态同步失败", f"同步失败: {e}")
        return False


def clear_tab_detector_cache() -> bool:
    """
    清除TabStateDetector缓存，确保状态检测的准确性
    
    Returns:
        bool: 清除是否成功
    """
    try:
        from dashboard.ui.utils.tab_detector import get_tab_detector
        tab_detector = get_tab_detector()
        tab_detector.clear_cache()
        debug_navigation("缓存清除", "TabStateDetector缓存已清除")
        return True
    except Exception as cache_error:
        debug_navigation("缓存清除失败", f"清除TabStateDetector缓存失败: {cache_error}")
        return False


def update_navigation_record(from_module: str, to_module: str) -> None:
    """
    更新导航记录

    Args:
        from_module: 源模块
        to_module: 目标模块
    """
    nav_refactor = get_navigation_refactor()
    if nav_refactor:
        current_time = time.time()
        nav_refactor.set_navigation_state('last_navigation_state', f"{from_module}->{to_module}")
        nav_refactor.set_navigation_state('last_navigation_time', current_time)
        logger.debug(f"导航记录已更新: {from_module} -> {to_module}")


def get_navigation_history() -> Dict[str, Any]:
    """
    获取导航历史记录

    Returns:
        Dict[str, Any]: 导航历史信息
    """
    nav_refactor = get_navigation_refactor()
    if nav_refactor:
        last_state = nav_refactor.get_navigation_state('last_navigation_state')
        last_time = nav_refactor.get_navigation_state('last_navigation_time')
        return {
            'last_state': last_state,
            'last_time': last_time,
            'exists': last_state is not None and last_time is not None
        }
    else:
        return {
            'last_state': None,
            'last_time': None,
            'exists': False
        }


def clear_navigation_history() -> None:
    """
    清除导航历史记录
    """
    nav_refactor = get_navigation_refactor()
    if nav_refactor:
        nav_refactor.set_navigation_state('last_navigation_state', None)
        nav_refactor.set_navigation_state('last_navigation_time', None)
        logger.info("导航历史记录已清除")


def validate_navigation_state(nav_manager: Any) -> Dict[str, Any]:
    """
    验证导航状态的一致性
    
    Args:
        nav_manager: 导航管理器
        
    Returns:
        Dict[str, Any]: 验证结果
    """
    try:
        # 从导航管理器获取状态
        nav_main = nav_manager.get_current_main_module()
        nav_sub = nav_manager.get_current_sub_module()

        # 从统一状态管理器获取状态
        nav_refactor = get_navigation_refactor()
        if nav_refactor:
            unified_main = nav_refactor.get_navigation_state('navigation', 'main_module')
            unified_sub = nav_refactor.get_navigation_state('navigation', 'sub_module')
        else:
            unified_main = None
            unified_sub = None

        # 检查一致性
        main_consistent = nav_main == unified_main
        sub_consistent = nav_sub == unified_sub

        return {
            'consistent': main_consistent and sub_consistent,
            'nav_manager': {'main': nav_main, 'sub': nav_sub},
            'unified_state': {'main': unified_main, 'sub': unified_sub},
            'main_consistent': main_consistent,
            'sub_consistent': sub_consistent
        }
    except Exception as e:
        return {
            'consistent': False,
            'error': str(e)
        }


# 为了向后兼容，保持原有的函数名
def _check_navigation_change(current_module, selected_module, nav_manager):
    """向后兼容的函数名"""
    return check_navigation_change(current_module, selected_module, nav_manager)


__all__ = [
    'check_navigation_change',
    'is_duplicate_navigation_request',
    'sync_navigation_state',
    'clear_tab_detector_cache',
    'update_navigation_record',
    'get_navigation_history',
    'clear_navigation_history',
    'validate_navigation_state',
    '_check_navigation_change'  # 向后兼容
]
