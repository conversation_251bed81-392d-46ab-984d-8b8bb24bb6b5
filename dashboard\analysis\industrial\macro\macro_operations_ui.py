#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工业宏观运行分析 - 前端UI模块
"""

import streamlit as st
import pandas as pd
from typing import Optional
from .data_processor import load_weights_data, load_macro_data, calculate_weighted_groups, get_group_details
from .chart_generator import create_single_axis_chart

# 导入新的UI组件
from dashboard.ui.components.analysis import (
    IndustrialTimeRangeSelectorComponent,
    IndustrialGroupDetailsComponent
)


def render_time_range_selector(key_suffix: str, default_index: int = 3):
    """
    Render time range selector with custom date inputs - 使用新的UI组件
    """
    # 使用新的UI组件
    time_selector_component = IndustrialTimeRangeSelectorComponent()
    return time_selector_component.render(st, key_suffix=key_suffix, default_index=default_index)


def render_group_details_expander(df_weights: pd.DataFrame, group_type: str, title: str):
    """
    Render expander with group details table - 使用新的UI组件
    """
    # 使用新的UI组件
    group_details_component = IndustrialGroupDetailsComponent()
    group_details_component.render(st, df_weights=df_weights, group_type=group_type, title=title)


def render_macro_operations_tab(st_obj, uploaded_file: Optional[object] = None):
    """
    Render the macro operations analysis tab
    """
    if uploaded_file is None:
        st_obj.info("请先上传Excel文件")
        return

    try:
        # Load data
        df_weights = load_weights_data(uploaded_file)
        df_macro = load_macro_data(uploaded_file)

        if df_macro.empty:
            st_obj.error("无法读取分行业工业增加值同比增速数据，请检查Excel文件格式")
            return

        if df_weights.empty:
            st_obj.warning("无法读取权重数据，将只显示原始时间序列图表")



        # Calculate weighted groups if weights data is available
        if not df_weights.empty:
            # Get target columns for weighted calculation
            target_columns = [col for col in df_macro.columns if col in df_weights['指标名称'].values]
            
            if target_columns:
                # Calculate weighted groups
                weighted_df = calculate_weighted_groups(df_macro, df_weights, target_columns)
                
                if not weighted_df.empty:
                    # Second chart - Export dependency groups with independent time selector
                    export_vars = [col for col in weighted_df.columns if col.startswith('出口依赖_')]
                    if export_vars:
                        time_range_2, custom_start_2, custom_end_2 = render_time_range_selector("chart2")
                        fig2 = create_single_axis_chart(weighted_df, export_vars, "", time_range_2, custom_start_2, custom_end_2)
                        st_obj.plotly_chart(fig2, use_container_width=True)
                        
                        # Add expander with export dependency group details
                        render_group_details_expander(df_weights, '出口依赖', "注释")

                    # Third chart - Upstream/downstream groups with independent time selector
                    stream_vars = [col for col in weighted_df.columns if col.startswith('上中下游_')]
                    if stream_vars:
                        time_range_3, custom_start_3, custom_end_3 = render_time_range_selector("chart3")
                        fig3 = create_single_axis_chart(weighted_df, stream_vars, "", time_range_3, custom_start_3, custom_end_3)
                        st_obj.plotly_chart(fig3, use_container_width=True)
                        
                        # Add expander with upstream/downstream group details
                        render_group_details_expander(df_weights, '上中下游', "注释")

                else:
                    st_obj.warning("加权计算结果为空，请检查数据")
            else:
                st_obj.warning("在分行业工业增加值同比增速数据中未找到权重表中的指标")
        else:
            st_obj.info("未提供权重数据，仅显示原始时间序列图表")

    except Exception as e:
        st_obj.error(f"加载工业分行业工业增加值同比增速分析模块时出错: {str(e)}")
        import traceback
        st_obj.error(f"详细错误信息: {traceback.format_exc()}")


# 保持向后兼容性的函数别名
def render_macro_operations_analysis(st_obj, uploaded_file: Optional[object] = None):
    """
    Backward compatibility function
    """
    return render_macro_operations_tab(st_obj, uploaded_file)
