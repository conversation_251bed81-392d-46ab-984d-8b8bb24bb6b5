# -*- coding: utf-8 -*-
"""
模块适配器
提供各个模块与统一状态管理器的适配功能
"""

from .preview_adapter import PreviewAdapter
from .data_exploration_adapter import (
    DataExplorationAdapter,
    get_data_exploration_adapter,
    reset_data_exploration_adapter
)
from .dfm_adapter import (
    DFMAdapter,
    DFMConfig,
    DFMResult,
    DataSeries,
    DFMModelType,
    DFMStatus,
    DataFrequency,
    create_dfm_config,
    create_data_series
)
from .tools_adapter import (
    ToolsAdapter,
    ProcessingStep,
    ProcessingPipeline,
    CacheItem,
    ToolsStats,
    ProcessingStage,
    CacheStrategy,
    ProcessingPriority,
    ToolType,
    create_processing_step,
    create_time_series_pretreat_pipeline,
    create_time_series_analysis_pipeline,
    create_batch_processing_pipeline
)
from .navigation_adapter import (
    NavigationAdapter,
    NavigationItem,
    UserPreference,
    NavigationHistory,
    NavigationStats,
    NavigationMode,
    NavigationState,
    NavigationEvent,
    ThemeMode,
    create_navigation_item,
    create_sidebar_navigation,
    create_breadcrumb_navigation,
    create_user_preference_config
)

__all__ = [
    'PreviewAdapter',
    'DataExplorationAdapter',
    'get_data_exploration_adapter',
    'reset_data_exploration_adapter',
    'DFMAdapter',
    'DFMConfig',
    'DFMResult',
    'DataSeries',
    'DFMModelType',
    'DFMStatus',
    'DataFrequency',
    'create_dfm_config',
    'create_data_series',
    'ToolsAdapter',
    'ProcessingStep',
    'ProcessingPipeline',
    'CacheItem',
    'ToolsStats',
    'ProcessingStage',
    'CacheStrategy',
    'ProcessingPriority',
    'ToolType',
    'create_processing_step',
    'create_time_series_pretreat_pipeline',
    'create_time_series_analysis_pipeline',
    'create_batch_processing_pipeline',
    'NavigationAdapter',
    'NavigationItem',
    'UserPreference',
    'NavigationHistory',
    'NavigationStats',
    'NavigationMode',
    'NavigationState',
    'NavigationEvent',
    'ThemeMode',
    'create_navigation_item',
    'create_sidebar_navigation',
    'create_breadcrumb_navigation',
    'create_user_preference_config'
]
